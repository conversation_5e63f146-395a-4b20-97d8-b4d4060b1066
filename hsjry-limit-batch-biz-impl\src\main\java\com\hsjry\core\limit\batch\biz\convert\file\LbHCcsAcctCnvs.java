/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.convert.file;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.hsjry.core.limit.batch.biz.entity.LbHCcsAcctData;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCcsAcctDo;

/**
 * 信用卡-历史表-第一币种贷记帐户文件数据转换器
 * 使用MapStruct进行高性能对象映射
 * 
 * 🎯 专门用于历史表文件数据和DO对象之间的转换
 * 支持286字段的完整映射，确保数据转换的准确性和高效性
 * 
 * ⚡ 性能特性：
 * - MapStruct编译期代码生成，零反射开销
 * - 支持批量转换，提升大数据量处理效率
 * - Spring组件模式，便于依赖注入
 * - 类型安全的转换，编译期错误检查
 * 
 * 🔄 转换路径：
 * LbHCcsAcctData ↔ LbHCcsAcctDo
 * 
 * <AUTHOR>
 * @version V4.0.1
 * @since 4.0.1 2025/1/21
 */
@Mapper(componentModel = "spring")
public interface LbHCcsAcctCnvs {

    /** 单例实例 */
    LbHCcsAcctCnvs INSTANCE = Mappers.getMapper(LbHCcsAcctCnvs.class);

    /**
     * 文件数据实体转换为数据库对象
     * 
     * @param data 文件数据实体
     * @return 数据库对象
     */
    LbHCcsAcctDo data2Do(LbHCcsAcctData data);

    /**
     * 数据库对象转换为文件数据实体
     * 
     * @param dataObject 数据库对象
     * @return 文件数据实体
     */
    LbHCcsAcctData do2Data(LbHCcsAcctDo dataObject);

    /**
     * 文件数据实体列表转换为数据库对象列表
     * 
     * @param dataList 文件数据实体列表
     * @return 数据库对象列表
     */
    List<LbHCcsAcctDo> dataListToDoList(List<LbHCcsAcctData> dataList);

    /**
     * 数据库对象列表转换为文件数据实体列表
     * 
     * @param doList 数据库对象列表
     * @return 文件数据实体列表
     */
    List<LbHCcsAcctData> doListToDataList(List<LbHCcsAcctDo> doList);
}