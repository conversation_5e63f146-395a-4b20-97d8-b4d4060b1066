package com.hsjry.core.limit.batch.dal.dao.mapper;

import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpCoPrtnLvlDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 额度中心-中间表-额度重算中对公客户中合作方层额度mapper
 *
 * <AUTHOR>
 * @date 2025-08-21 11:31:28
 */
public interface LbTReclcCorpCoPrtnLvlMapper extends CommonMapper<LbTReclcCorpCoPrtnLvlDo> {

    // ==================== 合作方层额度重算相关方法 ====================

    /**
     * 4.1.清空合作方层额度中间表
     */
    int truncateCoPartnerLevelLimit();

    /**
     * 4.2.插入合作方层额度客户编号和额度编号
     */
    int insertCoPartnerLevelLimit();

    /**
     * 4.3.更新合作方层额度中间表金额信息
     */
    int mergeCoPartnerLevelLimitAmount();

    /**
     * 4.4.更新额度实例金额信息
     */
    int mergeCoPartnerLevelLimitInstance();
}