package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度中心-中间表-额度重算中个人额度中其他额度Do
 *
 * <AUTHOR>
 * @date 2025-08-21 13:10:51
 */
@Table(name = "lb_t_reclc_indv_othr")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LbTReclcIndvOthrDo extends LbTReclcIndvOthrKeyDo implements Serializable {
    private static final long serialVersionUID = 1958517154135605249L;
    /** 模板节点编号 */
    @Column(name = "template_node_id")
    private String templateNodeId;
    /** 额度状态 */
    @Column(name = "limit_status")
    private String limitStatus;
    /** 总额度 */
    @Column(name = "total_amount")
    private java.math.BigDecimal totalAmount;
    /** 预占额度 */
    @Column(name = "pre_occupy_amount")
    private java.math.BigDecimal preOccupyAmount;
    /** 实占额度 */
    @Column(name = "real_occupy_amount")
    private java.math.BigDecimal realOccupyAmount;
    /** 总低风险额度 */
    @Column(name = "low_risk_amount")
    private java.math.BigDecimal lowRiskAmount;
    /** 预占低风险 */
    @Column(name = "pre_occupy_low_risk_amt")
    private java.math.BigDecimal preOccupyLowRiskAmt;
    /** 实占低风险 */
    @Column(name = "real_occupy_low_risk_amt")
    private java.math.BigDecimal realOccupyLowRiskAmt;
}
