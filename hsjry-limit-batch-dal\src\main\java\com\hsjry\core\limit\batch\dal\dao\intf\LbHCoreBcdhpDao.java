package com.hsjry.core.limit.batch.dal.dao.intf;

import com.hsjry.core.limit.batch.dal.dao.model.LbHCoreBcdhpDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbHCoreBcdhpQuery;

import java.util.List;

import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 核心系统-落地表-银行承兑汇票产品定义数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbHCoreBcdhpDao extends IBaseDao<LbHCoreBcdhpDo> {
    /**
     * 分页查询核心系统-落地表-银行承兑汇票产品定义
     *
     * @param lbHCoreBcdhpQuery 条件
     * @return PageInfo<LbHCoreBcdhpDo>
     */
    PageInfo<LbHCoreBcdhpDo> selectPage(LbHCoreBcdhpQuery lbHCoreBcdhpQuery, PageParam pageParam);

    /**
     * 根据key查询核心系统-落地表-银行承兑汇票产品定义
     *
     * @param faredm
     * @param cdxybh
     * @param bccppc
     * @param chupbh
     * @param dataDate
     * @return
     */
    LbHCoreBcdhpDo selectByKey(String faredm, String cdxybh, java.math.BigDecimal bccppc, String chupbh,
        String dataDate);

    /**
     * 根据key删除核心系统-落地表-银行承兑汇票产品定义
     *
     * @param faredm
     * @param cdxybh
     * @param bccppc
     * @param chupbh
     * @param dataDate
     * @return
     */
    int deleteByKey(String faredm, String cdxybh, java.math.BigDecimal bccppc, String chupbh, String dataDate);

    /**
     * 查询核心系统-落地表-银行承兑汇票产品定义信息
     *
     * @param lbHCoreBcdhpQuery 条件
     * @return List<LbHCoreBcdhpDo>
     */
    List<LbHCoreBcdhpDo> selectByExample(LbHCoreBcdhpQuery lbHCoreBcdhpQuery);

    /**
     * 新增核心系统-落地表-银行承兑汇票产品定义信息
     *
     * @param lbHCoreBcdhp 条件
     * @return int>
     */
    int insertBySelective(LbHCoreBcdhpDo lbHCoreBcdhp);

    /**
     * 修改核心系统-落地表-银行承兑汇票产品定义信息
     *
     * @param lbHCoreBcdhp
     * @return
     */
    int updateBySelective(LbHCoreBcdhpDo lbHCoreBcdhp);

    /**
     * 修改核心系统-落地表-银行承兑汇票产品定义信息
     *
     * @param lbHCoreBcdhp
     * @param lbHCoreBcdhpQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbHCoreBcdhpDo lbHCoreBcdhp, LbHCoreBcdhpQuery lbHCoreBcdhpQuery);

    /**
     * 清空核心系统-落地表-银行承兑汇票产品定义所有数据
     *
     * @return int
     */
    int deleteAll();

    int deleteByDataDate(String dataDate);

    /**
     * 批量插入银承汇票历史信息
     *
     * @param lbHCoreBcdhpList 银承汇票历史信息列表
     * @return int
     */
    int insertList(List<LbHCoreBcdhpDo> lbHCoreBcdhpList);
}
