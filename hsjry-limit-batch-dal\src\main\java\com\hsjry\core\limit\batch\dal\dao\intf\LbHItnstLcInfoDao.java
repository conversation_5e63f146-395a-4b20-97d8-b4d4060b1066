package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbHItnstLcInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbHItnstLcInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 国结系统-历史表-信用证信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-08-20 02:51:44
 */
public interface LbHItnstLcInfoDao extends IBaseDao<LbHItnstLcInfoDo> {
    /**
     * 分页查询国结系统-历史表-信用证信息
     *
     * @param lbHItnstLcInfoQuery 条件
     * @return PageInfo<LbHItnstLcInfoDo>
     */
    PageInfo<LbHItnstLcInfoDo> selectPage(LbHItnstLcInfoQuery lbHItnstLcInfoQuery, PageParam pageParam);

    /**
     * 根据key查询国结系统-历史表-信用证信息
     *
     * @param lcNo
     * @param dataDate
     * @return
     */
    LbHItnstLcInfoDo selectByKey(String lcNo, String dataDate);

    /**
     * 根据key删除国结系统-历史表-信用证信息
     *
     * @param lcNo
     * @param dataDate
     * @return
     */
    int deleteByKey(String lcNo, String dataDate);

    /**
     * 查询国结系统-历史表-信用证信息信息
     *
     * @param lbHItnstLcInfoQuery 条件
     * @return List<LbHItnstLcInfoDo>
     */
    List<LbHItnstLcInfoDo> selectByExample(LbHItnstLcInfoQuery lbHItnstLcInfoQuery);

    /**
     * 新增国结系统-历史表-信用证信息信息
     *
     * @param lbHItnstLcInfo 条件
     * @return int>
     */
    int insertBySelective(LbHItnstLcInfoDo lbHItnstLcInfo);

    /**
     * 修改国结系统-历史表-信用证信息信息
     *
     * @param lbHItnstLcInfo
     * @return
     */
    int updateBySelective(LbHItnstLcInfoDo lbHItnstLcInfo);

    /**
     * 修改国结系统-历史表-信用证信息信息
     *
     * @param lbHItnstLcInfo
     * @param lbHItnstLcInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbHItnstLcInfoDo lbHItnstLcInfo, LbHItnstLcInfoQuery lbHItnstLcInfoQuery);

    /**
     * 根据数据日期删除记录
     *
     * @param dataDate 数据日期
     * @return 删除的记录数
     */
    int deleteByDataDate(String dataDate);

    /**
     * 批量插入数据
     *
     * @param list 数据列表
     * @return 影响行数
     */
    int insertList(List<LbHItnstLcInfoDo> list);

}
