package com.hsjry.core.limit.batch.common.dto.file;

import java.io.Serializable;

import lombok.Builder;
import lombok.Data;

/**
 * 额度操作流水dto
 *
 * <AUTHOR>
 * @date 2025-07-09 12:31:05
 */
@Data
@Builder
public class LbCLimitOperateSerialDto implements Serializable {
    private static final long serialVersionUID = 1942924465449140227L;
    /** 操作金额编号 */
    private String operateAmountId;
    /** 客户编号 */
    private String custNo;
    /** 操作路径 */
    private String operatePath;
    /** 合同额度汇率重算标记 */
    private String contractRecalFlag;
    /** 汇率版本 */
    private java.math.BigDecimal exchangeRateVersion;
    /** 实体编号 */
    private String entityId;
    /** 备注 */
    private String remark;
    /** 失败原因 */
    private String failReason;
    /** 操作方向 */
    private String operateDirection;
    /** 额度编号 */
    private String custLimitId;
    /** 前置业务关联流水 */
    private String lastInboundSerialNo;
    /** 关联编号 */
    private String relationId;
    /** 操作低风险币种 */
    private String operateLowRiskCurrency;
    /** 操作低风险金额编号 */
    private String operateLowRiskAmtId;
    /** 操作低风险金额 */
    private java.math.BigDecimal operateLowRiskAmount;
    /** 操作金额币种 */
    private String operateAmountCurrency;
    /** 全局流水号 */
    private String globalSerialNo;
    /** 操作金额 */
    private java.math.BigDecimal operateAmount;
    /** 操作类型;EnumCustLimitOperatorType(001-发放额度,002-预占额度,003-预占取消,004-实占额度,005-实占取消,006-调整额度,007-恢复额度,008-恢复 */
    private String operateType;
    /** 所属组织编号 */
    private String ownOrganId;
    /** 操作人编号 */
    private String operatorId;
    /** 状态 */
    private String status;
    /** 操作流水编号 */
    private String closSerialNo;
    /** 更新时间 */
    private java.util.Date updateTime;
    /** 创建时间 */
    private java.util.Date createTime;
    /** 租户号 */
    private String tenantId;
    /** 前置业务时间 */
    private java.util.Date inboundSerialDatetime;
    /** 前置业务流水 */
    private String inboundSerialNo;
    /** 业务时间 */
    private java.util.Date bizDatetime;
    /** 渠道号 */
    private String channelNo;
    /** 业务流水号 */
    private String serialNo;
}
