// package com.hsjry.core.limit.batch.biz.job.sharding.biz.file;
//
// import java.io.File;
// import java.util.ArrayList;
// import java.util.List;
//
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Service;
//
// import com.hsjry.base.common.fs.service.FileProcessServiceFactory;
// import com.hsjry.base.common.fs.util.processor.FileUtils;
// import com.hsjry.base.common.job.dto.IEnumTrade;
// import com.hsjry.base.common.job.dto.JobInitDto;
// import com.hsjry.base.common.job.dto.JobShared;
// import com.hsjry.core.limit.batch.biz.PushCreditContractLimitFileBiz;
// import com.hsjry.core.limit.batch.biz.PushShardingPathService;
// import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
// import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
// import com.hsjry.core.limit.batch.biz.job.sharding.biz.limit.AbstractShardingPrepareBiz;
// import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
// import com.hsjry.core.limit.center.dal.dao.intf.CustLimitInfoBatchDao;
// import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoDo;
// import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
// import com.hsjry.core.limit.center.dal.dao.query.CustLimitInfoQuery;
// import com.hsjry.lang.common.stereotype.enums.EnumBool;
// import com.hsjry.lang.common.utils.CollectionUtil;
// import com.hsjry.lang.common.utils.GsonUtil;
// import com.hsjry.lang.common.utils.StringUtil;
//
// import lombok.extern.slf4j.Slf4j;
// /**
//  * 分片信贷合同维度文件下档
//  * <AUTHOR>
//  *
//  */
// @Service
// @Slf4j
// public class PushLimitContractFileJob extends AbstractShardingPrepareBiz<CustLimitInfoQuery>
//         implements JobCoreBusiness<LcCustLimitInfoDo>{
//     @Autowired
//     private CustLimitInfoBatchDao custLimitInfoBatchDao;
//     @Autowired
//     private PushCreditContractLimitFileBiz pushContractLimitFileBiz;
//     @Autowired
//     private PushShardingPathService pushShardingPathService;
//     @Autowired
//     protected FileProcessServiceFactory serviceFactory;
//
//     @Override
//     public Integer selectCountByCurrentGroupFromDb(CustLimitInfoQuery query) {
//         return custLimitInfoBatchDao.selectCountByCurrentGroup(query);
//     }
//
//     @Override
//     public IEnumTrade getJobTrade() {
//         return EnumJobTrade.PUSH_CONTRACT_LIMIT_FILE;
//     }
//
//     @Override
//     public ShardingResult<LcCustLimitInfoDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo, JobInitDto jobInitDto, JobShared jobShared) {
//         ShardingResult<LcCustLimitInfoDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
//         if (StringUtil.isBlank(jobShared.getExtParam())) {
//             return shardingResult;
//         }
//         Integer batchFixNum = jobInitDto.getFixNum();
//         CustLimitInfoQuery query = CustLimitInfoQuery.builder()
//                 .contractLimitFlag(EnumBool.YES.getCode())
//                 .offset(batchFixNum * (lcSliceBatchSerialDo.getBatchNum() - 1))
//                 .limit(jobShared.getLimit())
//                 .build();
//         log.info("=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");
//         List<LcCustLimitInfoDo> list = custLimitInfoBatchDao.selectShardList(query);
//         shardingResult.setShardingResultList(list);
//         return shardingResult;
//     }
//
//     @Override
//     public void execJobCoreBusiness(ShardingResult<LcCustLimitInfoDo> shardingResult) {
//         log.info("=========[{}]分片执行开始:[{}]===========", getJobTrade().getDescription(),shardingResult.getJobShared());
//         Integer busDate = shardingResult.getJobShared().getBusinessDate();
//         List<LcCustLimitInfoDo> shardingResultList = shardingResult.getShardingResultList();
//         //分片数据写入路径
//         String shardingFilePath = pushShardingPathService.getShardingFilePath(EnumJobTrade.PUSH_CONTRACT_LIMIT_FILE,busDate);
//         File file = new File(shardingFilePath);
//         if(!file.exists()){
//             file.mkdirs();
//         }
//         String batchNum = String.valueOf(shardingResult.getLcSliceBatchSerialDo().getBatchNum());
//         //分片数据文件名
//         String shardingFileName = pushShardingPathService.getShardingFileName(EnumJobTrade.PUSH_CONTRACT_LIMIT_FILE,busDate,batchNum);
//         String absShardingFilePath = shardingFilePath + File.separator + shardingFileName;
//         FileUtils.deleteIfExists(absShardingFilePath);
//
//         log.info("批次号={},开始执行......",batchNum);
//         if(CollectionUtil.isEmpty(shardingResultList)){
//             log.info("=========[{}]分片执行结束:" + batchNum + "数量为空===========",getJobTrade().getDescription());
//             //更新分片流水成功
//             normalUpdateSliceSerial(0, shardingResult.getLcSliceBatchSerialDo());
//             pushShardingPathService.writeFile(null,absShardingFilePath);
//             return;
//         }
//         //获取数据
//         List<String> result = pushContractLimitFileBiz.generateContractLimitData(shardingResultList);
//         //写分片文件
//         pushShardingPathService.writeFile(result,absShardingFilePath);
//         //更新分片流水成功
//         normalUpdateSliceSerial(result.size(), shardingResult.getLcSliceBatchSerialDo());
//         log.info("=========[{}]分片执行结束:[{}]数量为[{}]===========", getJobTrade().getDescription(),shardingResult.getJobShared()
//                 .getBatchNum(), result.size());
//     }
//
//     @Override
//     public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
//         log.info("====================== 接入业务{}分片逻辑 start ================================================",
//                 getJobTrade().getDescription());
//         List<JobShared> jobSharedList = new ArrayList<>();
//         //sql 批处理数量 暂定为分片数量，不放大
//         Integer batchFixNum = jobInitDto.getFixNum();
//         //当前分组的最大值，为下次 批处理的最小值
//         LcCustLimitInfoDo maxLimitInfoDo = new LcCustLimitInfoDo();
//         //构造查询条件 查询当前 分批处理的 排序 最大 对象
//         CustLimitInfoQuery query = CustLimitInfoQuery.builder()
//                 .contractLimitFlag(EnumBool.YES.getCode())
//                 .offset(batchFixNum - 1)
//                 .limit(1)
//                 .build();
//         //分片流水
//         int batchNum = 0;
//         while (maxLimitInfoDo != null) {
//             query.setCustLimitId(maxLimitInfoDo.getCustLimitId());
//             maxLimitInfoDo = custLimitInfoBatchDao.selectFirstOne(query);
//             //统计分片 数量
//             batchNum = countBatchNum(batchFixNum, query, maxLimitInfoDo, batchNum, jobInitDto, jobSharedList,
//                     query.getCustLimitId(), false);
//         }
//         log.info("====================== 接入业务{}分片逻辑 end ================================================",
//                 getJobTrade().getDescription());
//         return jobSharedList;
//     }
//
// }
