/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.sharding.biz.amtlimit;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Maps;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.limit.EnumAmtLimitRecordStatus;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.limit.AbstractShardingPrepareBiz;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.center.dal.dao.intf.AmtLimitRecordInfoBatchDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcAmtLimitRecordDailyDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcAmtLimitRecordDetailDao;
import com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitRecordDailyDo;
import com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitRecordDetailDo;
import com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitRecordInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.AmtLimitRecordInfoBatchQuery;
import com.hsjry.core.limit.center.dal.dao.query.LcAmtLimitRecordDailyQuery;
import com.hsjry.core.limit.center.dal.dao.query.LcAmtLimitRecordDetailQuery;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.DateUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.redis.service.lock.RedisHelpService;

import lombok.extern.slf4j.Slf4j;
/**
 * 日报生成
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/30 14:01
 */
@Service
@Slf4j
public class AmtLimitDailyReportImpl extends AbstractShardingPrepareBiz<AmtLimitRecordInfoBatchQuery>
    implements JobCoreBusiness<LcAmtLimitRecordInfoDo> {

    @Autowired
    private AmtLimitRecordInfoBatchDao amtLimitRecordInfoBatchDao;
    @Autowired
    private LcAmtLimitRecordDailyDao lcAmtLimitRecordDailyDao;
    @Autowired
    private RedisHelpService redisHelpService;
    @Autowired
    private LcAmtLimitRecordDetailDao lcAmtLimitRecordDetailDao;

    @Override
    public Integer selectCountByCurrentGroupFromDb(AmtLimitRecordInfoBatchQuery query) {
        return amtLimitRecordInfoBatchDao.selectCountByCurrentGroup(query);
    }

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.AMT_LIMIT_DAILY_REPORT;
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        log.info("====================== 接入业务{}分片逻辑 start ================================================",
            getJobTrade().getDescription());
        List<JobShared> jobSharedList = new ArrayList<>();
        //sql 批处理数量 暂定为分片数量，不放大
        Integer batchFixNum = jobInitDto.getFixNum();
        //当前分组的最大值，为下次 批处理的最小值
        LcAmtLimitRecordInfoDo maxRecordDo = new LcAmtLimitRecordInfoDo();

        //构造查询条件 查询当前 分批处理的 排序 最大 对象
        AmtLimitRecordInfoBatchQuery query = AmtLimitRecordInfoBatchQuery.builder()
            .recordStatus(EnumAmtLimitRecordStatus.EFFECTIVE.getCode())
            .offset(batchFixNum - 1)
            .limit(1)
            .build();
        //分片流水
        int batchNum = 0;
        while (maxRecordDo != null) {
            query.setRecordId(maxRecordDo.getRecordId());
            maxRecordDo = amtLimitRecordInfoBatchDao.selectFirstOne(query);
            //统计分片 数量
            batchNum = countBatchNum(batchFixNum, query, maxRecordDo, batchNum, jobInitDto, jobSharedList,
                query.getRecordId(), false);
        }
        log.info("======================= 接入业务{}分片逻辑 end ================================================",
            getJobTrade().getDescription());
        return jobSharedList;
    }

    @Override
    public ShardingResult<LcAmtLimitRecordInfoDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        ShardingResult<LcAmtLimitRecordInfoDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }
        //原始查询条件
        AmtLimitRecordInfoBatchQuery amtLimitRecordInfoBatchQuery = GsonUtil.json2Obj(jobShared.getExtParam(),
            AmtLimitRecordInfoBatchQuery.class);
        AmtLimitRecordInfoBatchQuery query = AmtLimitRecordInfoBatchQuery.builder()
            .offset(jobShared.getOffset())
            .limit(jobShared.getLimit())
            .recordStatus(EnumAmtLimitRecordStatus.EFFECTIVE.getCode())
            .recordId(amtLimitRecordInfoBatchQuery.getRecordId())
            .build();
        log.info("=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");
        List<LcAmtLimitRecordInfoDo> list = amtLimitRecordInfoBatchDao.selectShardList(query);
        shardingResult.setShardingResultList(list);
        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LcAmtLimitRecordInfoDo> shardingResult) {
        log.info("=========分片执行开始:[{}]===========", shardingResult.getJobShared()
            .getBatchNum());

        List<LcAmtLimitRecordInfoDo> lcAmtLimitRecordInfoDoList = shardingResult.getShardingResultList();
        if (CollectionUtils.isEmpty(lcAmtLimitRecordInfoDoList)) {
            log.info("=========分片执行结束:" + shardingResult.getJobShared()
                .getBatchNum() + "数量为空==========");
            return;
        }
        List<String> recordIdList = lcAmtLimitRecordInfoDoList.stream()
            .map(LcAmtLimitRecordInfoDo::getRecordId)
            .collect(Collectors.toList());
        List<LcAmtLimitRecordDailyDo> doList = lcAmtLimitRecordDailyDao.selectByExample(
            LcAmtLimitRecordDailyQuery.builder()
                .recordDate(Integer.valueOf(DateUtil.getDate(shardingResult.getJobBusinessDateTime(), DateUtil.DATE_FORMAT_2)))
                .recordIdList(recordIdList).build());
        Map<String, LcAmtLimitRecordDailyDo> recordMap = Maps.newHashMap();
        if (CollectionUtil.isNotEmpty(doList)) {
            recordMap.putAll(doList.stream()
                .collect(Collectors.toMap(LcAmtLimitRecordDailyDo::getRecordId, o -> o)));
        }
        for (LcAmtLimitRecordInfoDo infoDo : lcAmtLimitRecordInfoDoList) {
            LcAmtLimitRecordDailyDo dailyDo = recordMap.get(infoDo.getRecordId());
            if (null != dailyDo) {
                continue;
            }
            dailyHandle(infoDo, shardingResult.getJobBusinessDateTime());
        }
        //更新分片流水成功
        normalUpdateSliceSerial(lcAmtLimitRecordInfoDoList.size(), shardingResult.getLcSliceBatchSerialDo());
        log.info("=========分片执行结束:[{}]数量为[{}]===========", shardingResult.getJobShared()
            .getBatchNum(), lcAmtLimitRecordInfoDoList.size());
    }

    /**
     * 日报处理
     *
     * @param amtLimitRecordInfoDo
     * @param jobBusinessDateTime
     */
    private void dailyHandle(LcAmtLimitRecordInfoDo amtLimitRecordInfoDo, Date jobBusinessDateTime) {
        Integer dailyDay = Integer.valueOf(DateUtil.getDate(jobBusinessDateTime, DateUtil.DATE_FORMAT_2));
        String key =null;// LockKeyUtil.getLockKey(LimitCenterConstants.AMT_LIMIT_OPERATE, amtLimitRecordInfoDo.getRuleId());
        try {
            //记录加锁
            redisHelpService.lockAndWait(key);
            LcAmtLimitRecordDailyDo retry = lcAmtLimitRecordDailyDao.selectByKey(dailyDay,
                amtLimitRecordInfoDo.getRecordId());
            if (null != retry) {
                return;
            }
            List<LcAmtLimitRecordDetailDo> detailDoList = lcAmtLimitRecordDetailDao.selectByExample(
                LcAmtLimitRecordDetailQuery.builder()
                    .recordId(amtLimitRecordInfoDo.getRecordId())
                    .build());
            BigDecimal preAmount = BigDecimal.ZERO;
            BigDecimal realAmount = BigDecimal.ZERO;
            for (LcAmtLimitRecordDetailDo lcAmtLimitRecordDetailDo : detailDoList) {
                preAmount = preAmount.add(lcAmtLimitRecordDetailDo.getPreOccupyAmount());
                realAmount = realAmount.add(lcAmtLimitRecordDetailDo.getRealOccupyAmount());
            }
            LcAmtLimitRecordDailyDo recordDailyDo = new LcAmtLimitRecordDailyDo();
            recordDailyDo.setEffectiveEndTime(amtLimitRecordInfoDo.getEffectiveEndTime());
            recordDailyDo.setEffectiveStartTime(amtLimitRecordInfoDo.getEffectiveStartTime());
            recordDailyDo.setPreOccupyAmount(preAmount);
            recordDailyDo.setRealOccupyAmount(realAmount);
            recordDailyDo.setRuleId(amtLimitRecordInfoDo.getRuleId());
            recordDailyDo.setTotalAmount(amtLimitRecordInfoDo.getTotalAmount());
            recordDailyDo.setRecordDate(dailyDay);
            recordDailyDo.setRecordId(amtLimitRecordInfoDo.getRecordId());
            lcAmtLimitRecordDailyDao.insertBySelective(recordDailyDo);
        } finally {
            redisHelpService.unLock(key);
        }
    }
}
