---
description: 项目整体架构概览，包含各模块职责和依赖关系
alwaysApply: true
---
# Project Architecture

This is a multi-module Maven project for a batch processing application related to limits (`limit-batch`). The project follows a layered architecture.

## Module Overview

- **`pom.xml`**: The [main pom.xml](mdc:pom.xml) defines all the modules and dependencies for the project.

- **`hsjry-limit-batch-deploy`**: This is the deployment module. The main application entry point is likely in `LimitBatchDeployApplication.java`.
  - [LimitBatchDeployApplication.java](mdc:hsjry-limit-batch-deploy/src/main/java/com/hsjry/core/limit/batch/batch/deploy/LimitBatchDeployApplication.java) is the Spring Boot main class.

- **`hsjry-limit-batch-controller`**: Exposes HTTP endpoints. The main controller seems to be `TriggerBatchJobController`.
  - [TriggerBatchJobController.java](mdc:hsjry-limit-batch-controller/src/main/java/com/hsjry/core/limit/batch/controller/TriggerBatchJobController.java) handles triggering batch jobs.

- **`hsjry-limit-batch-facade`**: Defines the external API of the service. It contains DTOs and interfaces.
  - [ITriggerBatchJob.java](mdc:hsjry-limit-batch-facade/src/main/java/com/hsjry/core/limit/batch/facade/intf/trigger/ITriggerBatchJob.java) is the facade interface for triggering jobs.

- **`hsjry-limit-batch-biz` & `hsjry-limit-batch-biz-impl`**: These modules contain the core business logic.
  - `hsjry-limit-batch-biz`: Contains business logic interfaces.
  - `hsjry-limit-batch-biz-impl`: Contains the implementation of the business logic. A key entry point for job logic is `JobCoreBusinessFactory`.
    - [JobCoreBusinessFactory.java](mdc:hsjry-limit-batch-biz-impl/src/main/java/com/hsjry/core/limit/batch/biz/job/sharding/JobCoreBusinessFactory.java) seems to be a factory for creating job business logic.
    - Jobs are defined in `hsjry-limit-batch-biz-impl/src/main/java/com/hsjry/core/limit/batch/biz/job/job/`. For example, [AdjustAmtLimitPlanJob.java](mdc:hsjry-limit-batch-biz-impl/src/main/java/com/hsjry/core/limit/batch/biz/job/job/AdjustAmtLimitPlanJob.java).

- **`hsjry-limit-batch-core` & `hsjry-limit-batch-core-impl`**: These modules define core data structures and fundamental operations.
  - `hsjry-limit-batch-core`: Interfaces and Business Objects (BOs).
  - `hsjry-limit-batch-core-impl`: Implementations.

- **`hsjry-limit-batch-dal` & `hsjry-limit-batch-dal-impl`**: These modules handle data persistence.
  - `hsjry-limit-batch-dal`: Defines DAO interfaces and data models (DOs).
  - `hsjry-limit-batch-dal-impl`: Implements DAOs using MyBatis. The SQL mappings are in the `resources` directory.
    - Example DAO interface: [CustLimitInfoBatchDao.java](mdc:hsjry-limit-batch-dal/src/main/java/com/hsjry/core/limit/batch/dal/dao/intf/CustLimitInfoBatchDao.java)
    - Example MyBatis mapper XML: [CustLimitInfoBatchMapper.xml](mdc:hsjry-limit-batch-dal-impl/src/main/resources/com/hsjry/core/limit/batch/dal/dao/sqlmap/CustLimitInfoBatchMapper.xml)

- **`hsjry-limit-batch-common`**: Contains shared code like constants, enums, and utility classes used across different modules.
  - [LimitBatchConstants.java](mdc:hsjry-limit-batch-common/src/main/java/com/hsjry/core/limit/batch/common/constants/LimitBatchConstants.java)

- **`hsjry-limit-batch-test`**: Contains integration and unit tests.

