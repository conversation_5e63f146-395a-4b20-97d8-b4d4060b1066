package com.hsjry.core.limit.batch.biz.job.sharding.biz.limit;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.limit.EnumCustLimitStatus;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.center.dal.dao.intf.CustLimitInfoBatchDao;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.CustLimitInfoQuery;
import com.hsjry.lang.common.utils.DateUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 额度到期
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0 2023/3/22 14:21
 */
@Service
@Slf4j
public class ExpireCustLimitImpl extends AbstractShardingPrepareBiz<CustLimitInfoQuery>
    implements JobCoreBusiness<LcCustLimitInfoDo> {
    @Autowired
    private CustLimitInfoBatchDao custLimitInfoBatchDao;

    @Override
    public Integer selectCountByCurrentGroupFromDb(CustLimitInfoQuery query) {
        return custLimitInfoBatchDao.selectExpireCountByCurrentGroup(query);
    }

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.CUST_LIMIT_EXPIRE;
    }

    @Override
    public ShardingResult<LcCustLimitInfoDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        log.info("queryShardingResult...,jobShared={}", GsonUtil.obj2Json(jobShared));
        log.info("queryShardingResult...,jobInitDto={}", GsonUtil.obj2Json(jobInitDto));
        log.info("queryShardingResult...,lcSliceBatchSerialDo={}", GsonUtil.obj2Json(lcSliceBatchSerialDo));

        ShardingResult<LcCustLimitInfoDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }
        JSONObject inParam = JSON.parseObject(jobInitDto.getInPara());
        List<String> userIdList = JSONArray.parseArray(inParam.getString("userIdList"), String.class);
        //原始查询条件
        Integer batchFixNum = jobInitDto.getFixNum();
        CustLimitInfoQuery limitInfoQuery = GsonUtil.json2Obj(jobShared.getExtParam(), CustLimitInfoQuery.class);
        CustLimitInfoQuery query = CustLimitInfoQuery.builder()
            .offset(0)//19
            .limit(jobShared.getLimit())
            .limitObjectId(limitInfoQuery.getLimitObjectId())
            .limitObjectIdList(userIdList)
            .limitStatusList(limitInfoQuery.getLimitStatusList())
            .effectiveEndTime(limitInfoQuery.getEffectiveEndTime())
            .build();
        log.info("=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");
        List<LcCustLimitInfoDo> list = custLimitInfoBatchDao.selectExpireShardList(query);
        shardingResult.setShardingResultList(list);
        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LcCustLimitInfoDo> shardingResult) {
        List<LcCustLimitInfoDo> custLimitInfoDoList = shardingResult.getShardingResultList();
        if (CollectionUtils.isEmpty(custLimitInfoDoList)) {
            log.info("=========分片执行结束:" + shardingResult.getJobShared().getBatchNum() + "数量为空=========");
            return;
        }
        log.info("=========[{}]分片执行开始:" + shardingResult.getJobShared().getBatchNum() + "=========",
            getJobTrade().getDescription());
        //为了把同一客户数据分到一片，custLimitInfoDoList里实际只有limitObjectId,这里再去查询下每个客户的过期数据
        for (LcCustLimitInfoDo lcCustLimitInfoDo : custLimitInfoDoList) {
            log.info(
                "=========[{}]分片执行开始处理客户[{}]:" + shardingResult.getJobShared().getBatchNum() + "=========",
                getJobTrade().getDescription(), lcCustLimitInfoDo.getLimitObjectId());

        }
        //更新分片流水成功
        normalUpdateSliceSerial(custLimitInfoDoList.size(), shardingResult.getLcSliceBatchSerialDo());
        log.info("=========[{}]分片执行结束:" + shardingResult.getJobShared().getBatchNum() + "=========",
            getJobTrade().getDescription());
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        log.info("====================== 接入业务{}分片逻辑 start ================================================",
            getJobTrade().getDescription());
        List<JobShared> jobSharedList = new ArrayList<>();
        //sql 批处理数量 暂定为分片数量，不放大
        Integer batchFixNum = jobInitDto.getFixNum();
        //当前分组的最大值，为下次 批处理的最小值
        LcCustLimitInfoDo maxLimitInfoDo = new LcCustLimitInfoDo();
        //构造查询条件 查询当前 分批处理的 排序 最大 对象
        JSONObject inParam = JSON.parseObject(jobInitDto.getInPara());
        List<String> userIdList = JSONArray.parseArray(inParam.getString("userIdList"), String.class);
        CustLimitInfoQuery query = CustLimitInfoQuery.builder()
            .limitObjectIdList(userIdList)
            .limitStatusList(
                Lists.newArrayList(EnumCustLimitStatus.VALID.getCode(), EnumCustLimitStatus.FROZEN.getCode()))
            .effectiveEndTime(DateUtil.getDayEnd(
                DateUtil.getDate(String.valueOf(jobInitDto.getBusinessDate()), DateUtil.DATE_FORMAT_2)))
            .offset(batchFixNum - 1)
            .limit(1)
            .build();
        //分片流水
        int batchNum = 0;
        while (maxLimitInfoDo != null) {
            query.setLimitObjectId(maxLimitInfoDo.getLimitObjectId());
            maxLimitInfoDo = custLimitInfoBatchDao.selectExpireFirstOne(query);
            //统计分片 数量
            batchNum = countBatchNum(batchFixNum, query, maxLimitInfoDo, batchNum, jobInitDto, jobSharedList,
                query.getLimitObjectId(), false);
        }
        log.info("====================== 接入业务{}分片逻辑 end ================================================",
            getJobTrade().getDescription());
        return jobSharedList;
    }

}
