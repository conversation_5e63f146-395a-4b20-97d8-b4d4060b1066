/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.entity;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 国结系统-历史表-信用证信息文件数据实体
 * 用于文件同步过程中的数据传输
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/11 11:41
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LbHItnstLcInfoData implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 申请人客户号 */
    private String appNo;

    /** 信用证号 */
    private String lcNo;

    /** 信贷合同号 */
    private String contNo;

    /** 交易号 */
    private String tradeNo;

    /** 信用证金额 */
    private BigDecimal lcAmt;

    /** 上浮比例 */
    private BigDecimal lcAmtTolerUp;

    /** 下浮比例 */
    private BigDecimal lcAmtTolerDown;

    /** 最大开证金额 */
    private BigDecimal lcMaxAmt;

    /** 币种 */
    private String lcCurSign;

    /** 开证日期 */
    private String issueDate;

    /** 数据日期 */
    private String dataDate;
}
