package com.hsjry.core.limit.batch.biz.convert.copy;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.common.dto.file.LbCLimitAmtInfoDto;
import com.hsjry.core.limit.batch.dal.dao.model.LbCLimitAmtInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitAmtInfoDo;
import com.hsjry.lang.common.utils.DateUtil;
import com.hsjry.lang.common.utils.StringUtil;

/**
 * 额度实例金额信息转换类
 *
 * <AUTHOR>
 * @date 2025-07-09 12:31:05
 */
public class LbCLimitAmtInfoConverter {

    /**
     * DTO转DO
     *
     * @param dto DTO对象
     * @return DO对象
     */
    public static LbCLimitAmtInfoDo dtoToDo(LbCLimitAmtInfoDto dto) {
        return LbCLimitAmtInfoCnvs.INSTANCE.dtoToDo(dto);
    }

    /**
     * DO转DTO
     *
     * @param model DO对象
     * @return DTO对象
     */
    public static LbCLimitAmtInfoDto doToDto(LbCLimitAmtInfoDo model) {
        return LbCLimitAmtInfoCnvs.INSTANCE.do2Dto(model);
    }

    /**
     * DTO列表转DO列表
     *
     * @param dtoList DTO列表
     * @return DO列表
     */
    public List<LbCLimitAmtInfoDo> dtoListToDoList(List<LbCLimitAmtInfoDto> dtoList) {
        if (null == dtoList) {
            return Collections.emptyList();
        }
        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (dtoList.size() / 0.75f) + 1, 16);
        return dtoList.parallelStream().map(LbCLimitAmtInfoConverter::dtoToDo)//
            .collect(Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
    }

    /**
     * DO列表转DTO列表
     *
     * @param doList DO列表
     * @return DTO列表
     */
    public List<LbCLimitAmtInfoDto> doListToDtoList(List<LbCLimitAmtInfoDo> doList) {
        if (null == doList) {
            return Collections.emptyList();
        }
        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (doList.size() / 0.75f) + 1, 16);
        return doList.parallelStream().map(LbCLimitAmtInfoConverter::doToDto)//
            .collect(Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
    }

    /**
     * 从字符串数组创建DTO (根据CSV文件头字段顺序)
     *
     * @param fields 字段数组
     * @return DTO对象
     */
    public LbCLimitAmtInfoDto fieldsToDto(String[] fields) {
        if (fields == null || fields.length == 0) {
            return null;
        }

        LbCLimitAmtInfoDto dto = LbCLimitAmtInfoDto.builder().build();

        // 根据CSV文件头字段顺序映射（驼峰命名法）
        dto.setTenantId(trimToNull(fields[0]));
        dto.setCreateTime(parseDate(fields[1]));
        dto.setUpdateTime(parseDate(fields[2]));
        dto.setCustLimitId(trimToNull(fields[3]));
        dto.setTotalAmount(parseBigDecimal(fields[4]));
        dto.setPreOccupyAmount(parseBigDecimal(fields[5]));
        dto.setTmpForbidAmount(parseBigDecimal(fields[6]));
        dto.setRealOccupyAmount(parseBigDecimal(fields[7]));
        dto.setUsedAmount(parseBigDecimal(fields[8]));
        dto.setComprPreOccupyAmount(parseBigDecimal(fields[9]));
        dto.setComprRealOccupyAmount(parseBigDecimal(fields[10]));
        dto.setSubComprPreOccupyAmount(parseBigDecimal(fields[11]));
        dto.setSubComprRealOccupyAmount(parseBigDecimal(fields[12]));
        dto.setSharedPreOccupyAmount(parseBigDecimal(fields[13]));
        dto.setSharedRealOccupyAmount(parseBigDecimal(fields[14]));
        dto.setLowRiskAmount(parseBigDecimal(fields[15]));
        dto.setPreOccupyLowRiskAmt(parseBigDecimal(fields[16]));
        dto.setTmpForbidLowRiskAmt(parseBigDecimal(fields[17]));
        dto.setRealOccupyLowRiskAmt(parseBigDecimal(fields[18]));
        dto.setUsedLowRiskAmt(parseBigDecimal(fields[19]));
        dto.setComprPreOccupyLowRisk(parseBigDecimal(fields[20]));
        dto.setComprRealOccupyLowRisk(parseBigDecimal(fields[21]));
        dto.setSubComprPreOccupyLowRisk(parseBigDecimal(fields[22]));
        dto.setSubRealOccupyLowRisk(parseBigDecimal(fields[23]));
        dto.setSharedPreOccupyLowRiskAmt(parseBigDecimal(fields[24]));
        dto.setSharedRealOccupyLowRisk(parseBigDecimal(fields[25]));
        dto.setCurrency(trimToNull(fields[26]));
        dto.setLowRiskCurrency(trimToNull(fields[27]));
        dto.setSharedAmountLimit(parseBigDecimal(fields[28]));
        dto.setShareAmountLimit(parseBigDecimal(fields[29]));
        dto.setLowRiskAmountId(trimToNull(fields[30]));
        dto.setCustNo(trimToNull(fields[31]));

        return dto;
    }

    /**
     * 安全获取数组元素
     */
    private String safeGet(String[] fields, int index) {
        if (fields.length > index) {
            return trimToNull(fields[index]);
        }
        return null;
    }

    /**
     * 字符串去空格并转换为null
     */
    private String trimToNull(String str) {
        return StringUtil.trimToNull(str);
    }

    /**
     * 解析日期字符串
     */
    private Date parseDate(String dateStr) {
        if (StringUtil.isBlank(dateStr)) {
            return null;
        }
        try {
            // 尝试多种日期格式
            if (dateStr.length() == 8) {
                return DateUtil.parseDate(dateStr, "yyyyMMdd");
            } else if (dateStr.length() == 14) {
                return DateUtil.parseDate(dateStr, "yyyyMMddHHmmss");
            } else if (dateStr.contains("-")) {
                return DateUtil.parseDate(dateStr, "yyyy-MM-dd");
            } else if (dateStr.contains("/")) {
                return DateUtil.parseDate(dateStr, "yyyy/MM/dd");
            }
        } catch (Exception e) {
            // 日期解析失败，返回null
        }
        return null;
    }

    /**
     * 解析BigDecimal字符串
     */
    private BigDecimal parseBigDecimal(String numStr) {
        if (StringUtil.isBlank(numStr)) {
            return null;
        }
        try {
            return new BigDecimal(numStr.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * LcCustLimitAmtInfoDo转LbCLimitAmtInfoDo
     *
     * @param model 源DO对象
     * @return 目标DO对象
     */
    public static LbCLimitAmtInfoDo do2Copy(LcCustLimitAmtInfoDo model) {
        return LbCLimitAmtInfoCnvs.INSTANCE.do2Copy(model);
    }

    /**
     * LcCustLimitAmtInfoDo列表转LbCLimitAmtInfoDo列表
     *
     * @param doList 源DO列表
     * @return 目标DO列表
     */
    public static List<LbCLimitAmtInfoDo> doList2CopyList(List<LcCustLimitAmtInfoDo> doList) {
        if (null == doList) {
            return Collections.emptyList();
        }
        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (doList.size() / 0.75f) + 1, 16);
        return doList.parallelStream().map(LbCLimitAmtInfoConverter::do2Copy)//
            .collect(Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
    }

}