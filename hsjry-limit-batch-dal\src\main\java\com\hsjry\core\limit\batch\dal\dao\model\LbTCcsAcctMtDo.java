package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 信用卡-中间表-信用卡额度信息Do
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
@Table(name = "lb_t_ccs_acct_mt")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LbTCcsAcctMtDo extends LbTCcsAcctMtKeyDo implements Serializable {
    private static final long serialVersionUID = 1942415996337979414L;
    /** 利息余额(未出账单组成) */
    @Column(name = "bal_orint")
    private java.math.BigDecimal balOrint;
    /** 客户编号 */
    @Column(name = "cust_no")
    private String custNo;
    /** 可用余额 */
    @Column(name = "avl_bal")
    private java.math.BigDecimal avlBal;
    /** 分期付款已出帐单余额 */
    @Column(name = "stm_balmp")
    private java.math.BigDecimal stmBalmp;
    /** 分期付款未出帐单余额 */
    @Column(name = "bal_mp")
    private java.math.BigDecimal balMp;
    /** 帐单免息余额 */
    @Column(name = "stm_noint")
    private java.math.BigDecimal stmNoint;
    /** 帐单利息余额 */
    @Column(name = "stm_balori")
    private java.math.BigDecimal stmBalori;
    /** 帐单日记息余额 */
    @Column(name = "stm_balint")
    private java.math.BigDecimal stmBalint;
    /** 帐单消费余额 */
    @Column(name = "stm_balfre")
    private java.math.BigDecimal stmBalfre;
    /** 分期付款目前剩余本金 */
    @Column(name = "mp_rem_ppl")
    private java.math.BigDecimal mpRemPpl;
    /** 不记息余额(未出账单组成) */
    @Column(name = "bal_noint")
    private java.math.BigDecimal balNoint;
    /** 日记息余额(未出账单组成) */
    @Column(name = "bal_int")
    private java.math.BigDecimal balInt;
    /** 消费余额(未出账单组成) */
    @Column(name = "bal_free")
    private java.math.BigDecimal balFree;
    /** 复利余额 */
    @Column(name = "bal_cmpint")
    private java.math.BigDecimal balCmpint;
    /** 帐户信用额度 */
    @Column(name = "cred_limit")
    private Long credLimit;
    /** 帐户名称 */
    @Column(name = "acc_name1")
    private String accName1;
    /** 帐户拥有者证件号码 */
    @Column(name = "custr_nbr")
    private String custrNbr;
}
