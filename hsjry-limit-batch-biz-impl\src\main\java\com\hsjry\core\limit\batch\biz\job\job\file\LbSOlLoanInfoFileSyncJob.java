/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.job.file;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * 网贷借据信息文件同步处理任务
 * 负责S_OL_LOAN_INFO文件的批量同步处理
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/21 16:30
 */
@Slf4j
@Service("lbSOlLoanInfoFileSyncJob")
public class LbSOlLoanInfoFileSyncJob extends AbstractBaseBatchJob {

    public LbSOlLoanInfoFileSyncJob() {
        log.info("LbSOlLoanInfoFileSyncJob Bean正在创建...");
    }

    @Autowired
    @Qualifier("lbSOlLoanInfoFileSyncBizImpl")
    private BaseOrdinaryBiz lbSOlLoanInfoFileSyncBizImpl;

    @Override
    public BaseOrdinaryBiz getBaseOrdinaryBiz() {
        return lbSOlLoanInfoFileSyncBizImpl;
    }

    /**
     * 设置基础业务逻辑对象
     *
     * @param baseOrdinaryBiz 基础业务逻辑对象
     */
    public void setBaseOrdinaryBiz(BaseOrdinaryBiz baseOrdinaryBiz) {
        this.lbSOlLoanInfoFileSyncBizImpl = baseOrdinaryBiz;
    }
}
