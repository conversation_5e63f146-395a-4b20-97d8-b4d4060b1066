package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTGrpCustInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTGrpCustInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-集团客户信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbTGrpCustInfoDao extends IBaseDao<LbTGrpCustInfoDo> {
    /**
     * 分页查询额度中心-中间表-集团客户信息
     *
     * @param lbTGrpCustInfoQuery 条件
     * @return PageInfo<LbTGrpCustInfoDo>
     */
    PageInfo<LbTGrpCustInfoDo> selectPage(LbTGrpCustInfoQuery lbTGrpCustInfoQuery, PageParam pageParam);

    /**
     * 根据key查询额度中心-中间表-集团客户信息
     *
     * @param custNo
     * @return
     */
    LbTGrpCustInfoDo selectByKey(String custNo);

    /**
     * 根据key删除额度中心-中间表-集团客户信息
     *
     * @param custNo
     * @return
     */
    int deleteByKey(String custNo);

    /**
     * 查询额度中心-中间表-集团客户信息信息
     *
     * @param lbTGrpCustInfoQuery 条件
     * @return List<LbTGrpCustInfoDo>
     */
    List<LbTGrpCustInfoDo> selectByExample(LbTGrpCustInfoQuery lbTGrpCustInfoQuery);

    /**
     * 新增额度中心-中间表-集团客户信息信息
     *
     * @param lbTGrpCustInfo 条件
     * @return int>
     */
    int insertBySelective(LbTGrpCustInfoDo lbTGrpCustInfo);

    /**
     * 修改额度中心-中间表-集团客户信息信息
     *
     * @param lbTGrpCustInfo
     * @return
     */
    int updateBySelective(LbTGrpCustInfoDo lbTGrpCustInfo);

    /**
     * 修改额度中心-中间表-集团客户信息信息
     *
     * @param lbTGrpCustInfo
     * @param lbTGrpCustInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTGrpCustInfoDo lbTGrpCustInfo, LbTGrpCustInfoQuery lbTGrpCustInfoQuery);

    /**
     * 清空额度中心-中间表-集团客户信息表数据
     *
     * @return 影响行数
     */
    int truncateTable();

    /**
     * 从源表LC_CUST_LIMIT_OBJECT_INFO导入集团客户信息数据
     *
     * @return 影响行数
     */
    int insertFromSource();

    /**
     * 根据客户编号列表从源表LC_CUST_LIMIT_OBJECT_INFO导入集团客户信息数据
     *
     * @param userIdList 客户编号列表
     * @return 影响行数
     */
    int insertFromSource(List<String> userIdList);
}
