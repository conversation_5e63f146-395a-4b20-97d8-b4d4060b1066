import xml.etree.ElementTree as ET
import sys

def validate_xml(file_path):
    try:
        ET.parse(file_path)
        print(f"✓ {file_path} - XML格式正确")
        return True
    except ET.ParseError as e:
        print(f"✗ {file_path} - XML格式错误: {e}")
        return False
    except FileNotFoundError:
        print(f"✗ {file_path} - 文件未找到")
        return False

if __name__ == "__main__":
    xml_file = 'hsjry-limit-batch-dal-impl/src/main/resources/com/hsjry/core/limit/batch/dal/dao/sqlmap/LbTCcsAcctMtMapper.xml'
    validate_xml(xml_file)