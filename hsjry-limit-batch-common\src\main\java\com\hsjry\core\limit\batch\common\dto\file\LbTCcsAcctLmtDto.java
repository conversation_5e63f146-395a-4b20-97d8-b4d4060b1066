package com.hsjry.core.limit.batch.common.dto.file;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 信用卡额度信息DTO
 *
 * <AUTHOR>
 * @date 2025-07-09 12:31:05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LbTCcsAcctLmtDto {

    /** 账号 */
    private Integer xaccount;

    /** 银行代码 */
    private Integer bank;

    /** 账户信用额度 */
    private BigDecimal credLimit;

    /** 已用额度 */
    private BigDecimal usedLmt;

    /** 复利余额 */
    private BigDecimal balCmpint;

    /** 消费余额(未出账单组成) */
    private BigDecimal balFree;

    /** 日记息余额(未出账单组成) */
    private BigDecimal balInt;

    /** 不记息余额(未出账单组成) */
    private BigDecimal balNoint;

    /** 利息余额(未出账单组成) */
    private BigDecimal balOrint;

    /** 分期付款目前剩余本金 */
    private BigDecimal mpRemPpl;

    /** 账单消费余额 */
    private BigDecimal stmBalfre;

    /** 账单日记息余额 */
    private BigDecimal stmBalint;

    /** 账单利息余额 */
    private BigDecimal stmBalori;

    /** 账单免息余额 */
    private BigDecimal stmNoint;

    /** 分期付款未出账单余额 */
    private BigDecimal balMp;

    /** 分期付款已出账单余额 */
    private BigDecimal stmBalmp;

    /** 可用余额 */
    private BigDecimal avlBal;

    /** 客户编号 */
    private String custNo;

    /** 账户名称 */
    private String accName;

    /** 账户拥有者证件号码 */
    private String custrNbr;
} 