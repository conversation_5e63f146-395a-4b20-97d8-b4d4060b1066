/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.sharding.biz.file;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.limit.EnumLimitHandlerStatus;
import com.hsjry.core.limit.batch.biz.convert.file.LbHItnstLcInfoConverter;
import com.hsjry.core.limit.batch.biz.entity.FileLineData;
import com.hsjry.core.limit.batch.biz.entity.LbHItnstLcInfoData;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.AbstractFileBaseShardingPrepareBizImpl;
import com.hsjry.core.limit.batch.biz.utils.FileShardingUtils;
import com.hsjry.core.limit.batch.common.constants.CharsetConstants;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.common.enums.EnumLimitBatchErrorCode;
import com.hsjry.core.limit.batch.dal.dao.intf.LbHItnstLcInfoDao;
import com.hsjry.core.limit.batch.dal.dao.model.LbHItnstLcInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.sequence.SequenceTool;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 国结系统历史表信用证信息文件同步实现类
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/11 11:41
 */
@Slf4j
@Service("lbHItnstLcInfoFileSyncImpl")
@RequiredArgsConstructor
public class LbHItnstLcInfoFileSyncImpl extends AbstractFileBaseShardingPrepareBizImpl<LbHItnstLcInfoData> {

    /** 申请人客户号列数 */
    private static final int APP_NO_NUM = 1;
    /** 信用证号列数 */
    private static final int LC_NO_NUM = 2;
    /** 信贷合同号列数 */
    private static final int CONT_NO_NUM = 3;
    /** 交易号列数 */
    private static final int TRADE_NO_NUM = 4;
    /** 信用证金额列数 */
    private static final int LC_AMT_NUM = 5;
    /** 上浮比例列数 */
    private static final int LC_AMT_TOLER_UP_NUM = 6;
    /** 下浮比例列数 */
    private static final int LC_AMT_TOLER_DOWN_NUM = 7;
    /** 最大开证金额列数 */
    private static final int LC_MAX_AMT_NUM = 8;
    /** 币种列数 */
    private static final int LC_CUR_SIGN_NUM = 9;
    /** 开证日期列数 */
    private static final int ISSUE_DATE_NUM = 10;
    /** 最小字段数量 */
    private static final int MIN_FIELD_COUNT = 10;
    /** 分隔符 */
    private static final String FIELD_SEPARATOR = "\\|\\+\\|";
    /** 批处理大小 - 性能优化：从1000调整为10000，提升批量插入性能 */
    private static final int BATCH_SIZE = 10000;
    private final String SEQUENCE_NO = "SEQUENCE_NO";

    private final LbHItnstLcInfoDao lbHItnstLcInfoDao;
    @Value("${project.itnst.lc.info.filename:ITNST_LC_INFO_[DATE].dat}")
    private String fileName;
    @Value("${limit.batch.remoteFilePath:/app/bftfiles/cfs/recv/ebbs/}")
    private String remoteFilePathDefine;

    @Override
    public ShardingResult<LbHItnstLcInfoData> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行文件分片,", businessDate,
            jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info(prefixLog + "开始执行文件分片处理");

        ShardingResult<LbHItnstLcInfoData> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }

        JSONObject inParam = JSON.parseObject(jobInitDto.getInPara());
        try {
            // 读取文件分片数据
            FileLineData fileLineData = GsonUtil.json2Obj(jobShared.getExtParam(), FileLineData.class);
            log.info(prefixLog + "开始读取文件数据分片[{}]", GsonUtil.objToStrForLog(fileLineData));
            List<String> originData = FileShardingUtils.readFileSharedData(jobShared, skipFirst(),
                CharsetConstants.GBK);

            // 使用并行流处理数据，提升性能
            List<LbHItnstLcInfoData> fileDataList = processOriginDataParallel(originData, prefixLog);

            log.info(prefixLog + "读取文件数据分片总量[{}]", fileDataList.size());
            shardingResult.setShardingResultList(fileDataList);
        } catch (Exception e) {
            log.error(prefixLog + "文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }

        jobShared.setBatchSerialNo(inParam.getString(SEQUENCE_NO));
        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LbHItnstLcInfoData> shardingResult) {
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();
        List<LbHItnstLcInfoData> dataList = shardingResult.getShardingResultList();

        if (CollectionUtil.isEmpty(dataList)) {
            log.info(prefixLog + "国结信用证数据文件处理:文件分片数据为空,执行中断。");
            return;
        }

        log.info(prefixLog + "国结信用证数据文件处理:开始执行分片数据操作,数据量:[{}]", dataList.size());
        // 设置数据日期
        String dataDateStr = String.valueOf(businessDate);
        dataList.forEach(data -> data.setDataDate(dataDateStr));
        // 检查是否是第一个分片，如果是则清空表
        if (sliceBatchSerialDo.getBatchNum() == 1) {
            log.info(prefixLog + "第一个分片,清空目标表 lb_h_itnst_lc_info");
            lbHItnstLcInfoDao.deleteByDataDate(dataDateStr);
        }

        // 确保分片流水对象的状态字段不为null
        if (sliceBatchSerialDo.getSharedStatus() == null) {
            log.debug(prefixLog + "分片流水状态为null,设置为处理中状态");
            sliceBatchSerialDo.setSharedStatus(EnumLimitHandlerStatus.IN_HANDLE.getCode());
        }

        // 使用并行流进行数据转换和验证
        List<LbHItnstLcInfoDo> insertList = dataList.parallelStream().map(LbHItnstLcInfoConverter::data2Do).filter(
            this::validateData).collect(Collectors.toCollection(ArrayList::new));

        if (CollectionUtil.isNotEmpty(insertList)) {
            // 批量插入数据
            processBatchInsert(insertList, prefixLog);
            log.info(prefixLog + "数据插入完成,总插入数量:[{}]", insertList.size());
        } else {
            log.warn(prefixLog + "没有有效数据需要插入");
        }
    }

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.H_ITNST_LC_INFO_FILE_SYNC;
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        JSONObject param = JSON.parseObject(jobInitDto.getInPara());
        //设置流水ID,解决多次从redis获取唯一值的性能问题
        param.put(SEQUENCE_NO, SequenceTool.nextId());
        //更新jobInitDto的inpara参数
        jobInitDto.setInPara(param.toJSONString());

        // 确保fixNum被正确设置
        if (jobInitDto.getFixNum() == null) {
            log.warn(prefixLog + "fixNum为null,设置默认值50000");
            jobInitDto.setFixNum(50000);  // 性能优化：从1000调整为50000
        }

        List<JobShared> sharedList = Lists.newArrayList();
        log.info(prefixLog + "[{}]文件处理", jobTradeDesc);
        String localFilePath = FileShardingUtils.getLocalFilePath(jobInitDto.getBusinessDate(),
            remoteFilePathDefine + FileShardingUtils.ACCT_DATE_CODE_MARK + File.separator);
        Integer acctDate = jobInitDto.getBusinessDate();
        String localFileName = fileName.replace(FileShardingUtils.FILE_DATE_CODE_MARK, String.valueOf(acctDate));
        String actualLocalFilePath = localFilePath.replace(FileShardingUtils.ACCT_DATE_CODE_MARK,
            String.valueOf(acctDate));

        log.info(prefixLog + "转换后的本地文件路径: [{}]", actualLocalFilePath);

        try {
            String fileAttr = FIELD_SEPARATOR;
            String filePath = actualLocalFilePath + localFileName;
            log.info("实际查找的文件路径: [{}]", filePath);

            // 检查目录是否存在
            File directory = new File(actualLocalFilePath);
            if (!directory.exists()) {
                log.info(prefixLog + "目录[{}]不存在,尝试创建", actualLocalFilePath);
                directory.mkdirs();
            } else {
                log.info(prefixLog + "目录[{}]已存在", actualLocalFilePath);
                // 列出目录中的文件
                File[] files = directory.listFiles();
                if (files != null && files.length > 0) {
                    log.info(prefixLog + "目录[{}]中的文件列表:", actualLocalFilePath);
                    for (File f : files) {
                        log.info(prefixLog + " - {}", f.getName());
                    }
                } else {
                    log.info(prefixLog + "目录[{}]为空", actualLocalFilePath);
                }
            }

            File localFile = new File(filePath);
            log.info(prefixLog + "检查文件[{}]是否存在: {}", filePath, localFile.exists());

            //判断本地文件是否存在，不存在则直接报错
            if (!localFile.exists()) {
                log.error(prefixLog + "本地文件[{}]不存在,请确认文件路径是否正确", filePath);
                String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
                String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
                throw new HsjryBizException(errorCode, errorMsg);
            }

            // 文件存在，直接进行分片处理
            log.info(prefixLog + "[{}]开始[{}]文件数据分片处理", jobTradeDesc, filePath);
            log.info(prefixLog + "分片参数: fixNum=[{}], fileAttr=[{}], skipFirst=[{}]", jobInitDto.getFixNum(),
                fileAttr, skipFirst());

            List<JobShared> jobShareds = FileShardingUtils.getFileSharedData(jobInitDto, localFile, fileAttr,
                skipFirst());
            log.info(prefixLog + "[{}]结束[{}]文件数据分片当前分片数[{}]", jobTradeDesc, filePath, jobShareds.size());
            sharedList.addAll(jobShareds);
        } catch (Exception e) {
            log.error(prefixLog + "文件分片处理错误", e);
            String errorCode = EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getCode();
            String errorMsg = prefixLog + EnumLimitBatchErrorCode.FILE_SHARDING_HANDLE_ERROR.getDescription();
            log.error(errorMsg);
            throw new HsjryBizException(errorCode, errorMsg);
        }
        return sharedList;
    }

    private boolean skipFirst() {
        return true; // 跳过标题行
    }

    /**
     * 并行处理原始数据
     * 使用并行流提升数据处理性能，同时保证线程安全
     *
     * @param originData 原始数据列表
     * @param prefixLog 日志前缀
     * @return 处理后的数据列表
     */
    private List<LbHItnstLcInfoData> processOriginDataParallel(List<String> originData, String prefixLog) {
        AtomicInteger lineNumber = new AtomicInteger(0);
        return originData.parallelStream().map(line -> {
            int currentLine = lineNumber.incrementAndGet();
            try {
                return parseLineToData(line, currentLine);
            } catch (Exception e) {
                log.error(prefixLog + "处理第[{}]行数据失败: [{}]", currentLine, line, e);
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 解析单行数据
     * 提取单行数据解析逻辑，提高代码复用性和可维护性
     *
     * @return 解析后的数据对象
     */
    private LbHItnstLcInfoData parseLineToData(String line, int lineNumber) {
        if (StringUtil.isBlank(line)) {
            return null;
        }

        String[] fields = line.split(FIELD_SEPARATOR);
        if (fields.length < MIN_FIELD_COUNT) {
            log.warn("第[{}]行数据字段数量不足,需要[{}]个字段,实际[{}]个: [{}]", lineNumber, MIN_FIELD_COUNT,
                fields.length, line);
            return null;
        }
        try {
            return LbHItnstLcInfoData.builder()
                .appNo(getFieldValue(fields, APP_NO_NUM - 1))
                .lcNo(getFieldValue(fields, LC_NO_NUM - 1))
                .contNo(getFieldValue(fields, CONT_NO_NUM - 1))
                .tradeNo(getFieldValue(fields, TRADE_NO_NUM - 1))
                .lcAmt(parseBigDecimal(getFieldValue(fields, LC_AMT_NUM - 1)))
                .lcAmtTolerUp(parseBigDecimal(getFieldValue(fields, LC_AMT_TOLER_UP_NUM - 1)))
                .lcAmtTolerDown(parseBigDecimal(getFieldValue(fields, LC_AMT_TOLER_DOWN_NUM - 1)))
                .lcMaxAmt(parseBigDecimal(getFieldValue(fields, LC_MAX_AMT_NUM - 1)))
                .lcCurSign(getFieldValue(fields, LC_CUR_SIGN_NUM - 1))
                .issueDate(getFieldValue(fields, ISSUE_DATE_NUM - 1))
                .build();
        } catch (Exception e) {
            log.error("解析第[{}]行数据失败: [{}]", lineNumber, line, e);
            return null;
        }
    }

    /**
     * 批量插入数据
     */
    private void processBatchInsert(List<LbHItnstLcInfoDo> insertList, String prefixLog) {
        List<List<LbHItnstLcInfoDo>> batches = Lists.partition(insertList, BATCH_SIZE);
        for (List<LbHItnstLcInfoDo> batch : batches) {
            try {
                lbHItnstLcInfoDao.insertList(batch);
                log.debug(prefixLog + "批量插入[{}]条数据成功", batch.size());
            } catch (Exception e) {
                log.error(prefixLog + "批量插入[{}]条数据失败", batch.size(), e);
                throw e;
            }
        }
    }

    /**
     * 获取字段值
     *
     * @param fields 字段数组
     * @param index 索引
     * @return 字段值
     */
    private String getFieldValue(String[] fields, int index) {
        return index < fields.length ? StringUtil.trim(fields[index - 1]) : "";
    }

    /**
     * 解析BigDecimal
     *
     * @param value 字符串值
     * @return BigDecimal对象
     */
    private BigDecimal parseBigDecimal(String value) {
        if (StringUtil.isEmpty(value)) {
            return null;
        }
        try {
            return new BigDecimal(value);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 验证数据
     *
     * @param data DO对象
     * @return 是否有效
     */
    private boolean validateData(LbHItnstLcInfoDo data) {
        return Objects.nonNull(data) && StringUtil.isNotEmpty(data.getLcNo()) //
            && StringUtil.isNotEmpty(data.getDataDate());
    }
}
