---
description: 批处理作业开发指南，包含各种作业类型和触发机制
alwaysApply: true
---
# 批处理作业指南

## 作业类型概览

项目中定义了多种批处理作业，位于 `hsjry-limit-batch-biz-impl/src/main/java/com/hsjry/core/limit/batch/biz/job/job/` 目录下：

### 限额相关作业
- [AdjustAmtLimitPlanJob.java](mdc:hsjry-limit-batch-biz-impl/src/main/java/com/hsjry/core/limit/batch/biz/job/job/AdjustAmtLimitPlanJob.java) - 调整金额限额计划作业
- [ValidAmtLimitJob.java](mdc:hsjry-limit-batch-biz-impl/src/main/java/com/hsjry/core/limit/batch/biz/job/job/ValidAmtLimitJob.java) - 验证金额限额作业
- [DisableRemindAmtLimitRuleJob.java](mdc:hsjry-limit-batch-biz-impl/src/main/java/com/hsjry/core/limit/batch/biz/job/job/DisableRemindAmtLimitRuleJob.java) - 禁用提醒金额限额规则作业

### 推送作业
- [PushCustomerLimitJob.java](mdc:hsjry-limit-batch-biz-impl/src/main/java/com/hsjry/core/limit/batch/biz/job/job/PushCustomerLimitJob.java) - 推送客户限额作业
- [PushContractLimitJob.java](mdc:hsjry-limit-batch-biz-impl/src/main/java/com/hsjry/core/limit/batch/biz/job/job/PushContractLimitJob.java) - 推送合同限额作业

### 统计作业
- [StatisticsOrganLimitJob.java](mdc:hsjry-limit-batch-biz-impl/src/main/java/com/hsjry/core/limit/batch/biz/job/job/StatisticsOrganLimitJob.java) - 统计机构限额作业

### 文件处理作业
- [MergeCreditContractFileLocalJob.java](mdc:hsjry-limit-batch-biz-impl/src/main/java/com/hsjry/core/limit/batch/biz/job/job/MergeCreditContractFileLocalJob.java) - 合并信贷合同文件（本地）作业
- [MergeCreditContractFileRemoteJob.java](mdc:hsjry-limit-batch-biz-impl/src/main/java/com/hsjry/core/limit/batch/biz/job/job/MergeCreditContractFileRemoteJob.java) - 合并信贷合同文件（远程）作业
- [MergeFileLocalJob.java](mdc:hsjry-limit-batch-biz-impl/src/main/java/com/hsjry/core/limit/batch/biz/job/job/MergeFileLocalJob.java) - 合并文件（本地）作业
- [MergeFileRemoteJob.java](mdc:hsjry-limit-batch-biz-impl/src/main/java/com/hsjry/core/limit/batch/biz/job/job/MergeFileRemoteJob.java) - 合并文件（远程）作业
- [InvoiceFileDownloadJob.java](mdc:hsjry-limit-batch-biz-impl/src/main/java/com/hsjry/core/limit/batch/biz/job/job/InvoiceFileDownloadJob.java) - 发票文件下载作业

### 其他作业
- [BigDataSupplyJob.java](mdc:hsjry-limit-batch-biz-impl/src/main/java/com/hsjry/core/limit/batch/biz/job/job/BigDataSupplyJob.java) - 大数据补充作业
- [ExchangeLimitJob.java](mdc:hsjry-limit-batch-biz-impl/src/main/java/com/hsjry/core/limit/batch/biz/job/job/ExchangeLimitJob.java) - 汇率限额作业
- [ClearReCalPrepareJob.java](mdc:hsjry-limit-batch-biz-impl/src/main/java/com/hsjry/core/limit/batch/biz/job/job/ClearReCalPrepareJob.java) - 清除重新计算准备作业

## 作业触发

作业通过 [TriggerBatchJobController.java](mdc:hsjry-limit-batch-controller/src/main/java/com/hsjry/core/limit/batch/controller/TriggerBatchJobController.java) 触发，使用 [TriggerJobDto.java](mdc:hsjry-limit-batch-facade/src/main/java/com/hsjry/core/limit/batch/facade/dto/trigger/TriggerJobDto.java) 作为参数。

## 作业工厂

[JobCoreBusinessFactory.java](mdc:hsjry-limit-batch-biz-impl/src/main/java/com/hsjry/core/limit/batch/biz/job/sharding/JobCoreBusinessFactory.java) 是创建作业业务逻辑的工厂类。

