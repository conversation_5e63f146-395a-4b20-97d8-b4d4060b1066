package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbSOlProdLmtInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSOlProdLmtInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 网贷系统-落地表-产品额度信息（记录客户产品额度信息）数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbSOlProdLmtInfoDao extends IBaseDao<LbSOlProdLmtInfoDo> {
    /**
     * 分页查询网贷系统-落地表-产品额度信息（记录客户产品额度信息）
     *
     * @param lbSOlProdLmtInfoQuery 条件
     * @return PageInfo<LbSOlProdLmtInfoDo>
     */
    PageInfo<LbSOlProdLmtInfoDo> selectPage(LbSOlProdLmtInfoQuery lbSOlProdLmtInfoQuery, PageParam pageParam);

    /**
     * 根据key查询网贷系统-落地表-产品额度信息（记录客户产品额度信息）
     *
     * @param creditLimitId
     * @return
     */
    LbSOlProdLmtInfoDo selectByKey(String creditLimitId);

    /**
     * 根据key删除网贷系统-落地表-产品额度信息（记录客户产品额度信息）
     *
     * @param creditLimitId
     * @return
     */
    int deleteByKey(String creditLimitId);

    /**
     * 查询网贷系统-落地表-产品额度信息（记录客户产品额度信息）信息
     *
     * @param lbSOlProdLmtInfoQuery 条件
     * @return List<LbSOlProdLmtInfoDo>
     */
    List<LbSOlProdLmtInfoDo> selectByExample(LbSOlProdLmtInfoQuery lbSOlProdLmtInfoQuery);

    /**
     * 新增网贷系统-落地表-产品额度信息（记录客户产品额度信息）信息
     *
     * @param lbSOlProdLmtInfo 条件
     * @return int>
     */
    int insertBySelective(LbSOlProdLmtInfoDo lbSOlProdLmtInfo);

    /**
     * 修改网贷系统-落地表-产品额度信息（记录客户产品额度信息）信息
     *
     * @param lbSOlProdLmtInfo
     * @return
     */
    int updateBySelective(LbSOlProdLmtInfoDo lbSOlProdLmtInfo);

    /**
     * 修改网贷系统-落地表-产品额度信息（记录客户产品额度信息）信息
     *
     * @param lbSOlProdLmtInfo
     * @param lbSOlProdLmtInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbSOlProdLmtInfoDo lbSOlProdLmtInfo, LbSOlProdLmtInfoQuery lbSOlProdLmtInfoQuery);

    /**
     * 批量插入网贷系统-落地表-产品额度信息
     *
     * @param lbSOlProdLmtInfoList 批量数据
     * @return int
     */
    @Override
    int insertList(List<LbSOlProdLmtInfoDo> lbSOlProdLmtInfoList);

    /**
     * 清空网贷系统-落地表-产品额度信息所有数据
     *
     * @return int
     */
    int deleteAll();

    /**
     * 获取第一个对象，用于分片查询
     * 根据复合主键排序，获取指定偏移量的第一条记录
     *
     * @param query 查询条件
     * @return 第一条记录，如果没有则返回null
     */
    LbSOlProdLmtInfoDo selectFirstOne(LbSOlProdLmtInfoQuery query);

    /**
     * 查询当前分片主键范围内的数据总数
     * 根据复合主键范围统计当前分片的数据量
     *
     * @param query 查询条件，需包含主键范围
     * @return 当前分片的数据量
     */
    Integer selectCountByCurrentGroup(LbSOlProdLmtInfoQuery query);

    /**
     * 查询分片数据列表
     * 支持offset/limit分页查询
     *
     * @param query 查询条件，包含offset和limit
     * @return 分片数据列表
     */
    List<LbSOlProdLmtInfoDo> selectShardList(LbSOlProdLmtInfoQuery query);
}
