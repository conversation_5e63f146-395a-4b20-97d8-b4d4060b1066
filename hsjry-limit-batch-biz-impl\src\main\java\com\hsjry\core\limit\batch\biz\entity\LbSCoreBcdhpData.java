/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.entity;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 银行承兑汇票核心业务数据实体
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/22 10:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LbSCoreBcdhpData {

    /** 法人代码 */
    private String faredm;

    /** 银承协议编号 */
    private String cdxybh;

    /** 本次出票批次 */
    private BigDecimal bccppc;

    /** 本次出票编号 */
    private String chupbh;

    /** 签发机构号 */
    private String yngyjg;

    /** 申请机构号 */
    private String zhngjg;

    /** 银承合同编号 */
    private String htngbh;

    /** 协议总票面金额 */
    private BigDecimal zongje;

    /** 凭证种类 */
    private String pngzzl;

    /** 票据轮冠字 */
    private String pjlugz;

    /** 票据号码 */
    private String piojhm;

    /** 币种 */
    private String huobdh;

    /** 票面金额 */
    private BigDecimal piomje;

    /** 汇票金额 */
    private BigDecimal huipje;

    /** 备款金额 */
    private BigDecimal beikje;

    /** 签发行联行行号 */
    private String qfhlhh;

    /** 签发行联行行名 */
    private String qfhlhm;

    /** 出票人客户号 */
    private String kehhao;

    /** 出票人帐号 */
    private String chprzh;

    /** 出票人全称 */
    private String chprqc;

    /** 收款人客户号 */
    private String skrkhh;

    /** 收款人帐号 */
    private String shkrzh;

    /** 收款人户名 */
    private String shkrxm;

    /** 收款行行号 */
    private String skhhao;

    /** 收款行行名 */
    private String shkhhm;

    /** 是否全额保证金 */
    private String shfobz;

    /** 额度层次 */
    private String edceng;

    /** 银承汇票签发方式 */
    private String ycqffs;

    /** 交易日期 */
    private String jioyrq;

    /** 到期日期 */
    private String daoqrq;

    /** 签发日期 */
    private String qnfarq;

    /** 承兑日期 */
    private String cduirq;

    /** 备款日期 */
    private String ruzhrq;

    /** 垫款标志 */
    private String dnknbz;

    /** 垫款借据编号 */
    private String jiejuh;

    /** 垫款金额 */
    private BigDecimal zhdkje;

    /** 资金去向 */
    private String zijnqx;

    /** 资金转入账号 */
    private String dczrzh;

    /** 持票人账号 */
    private String skzhao;

    /** 持票人名称 */
    private String chipmc;

    /** 持票人开户行行号 */
    private String kaihhh;

    /** 持票人开户行行名 */
    private String kaihhm;

    /** 未用退回日期 */
    private String sxiorq;

    /** 五级分类日期 */
    private String wjflrq;

    /** 五级贷款分类 */
    private String wjdkfl;

    /** 承兑汇票状态 */
    private String ychpzt;

    /** 票据状态 */
    private String piojzt;

    /** 是否电子票据 */
    private String sfdzpj;

    /** 明细序号 */
    private BigDecimal mxxhao;

    /** 挂失日期 */
    private String gsriqi;

    /** 解挂日期 */
    private String jgriqi;

    /** 备注 */
    private String remark;

    /** 签发行联行地址 */
    private String beizxx;

    /** 开户机构 */
    private String kaihjg;

    /** 开户柜员 */
    private String kaihgy;

    /** 维护柜员 */
    private String weihgy;

    /** 维护日期 */
    private String weihrq;

    /** 销户柜员 */
    private String xiohgy;

    /** 销户日期 */
    private String xiohrq;

    /** 维护机构 */
    private String weihjg;

    /** 维护时间 */
    private String weihsj;

    /** 记录状态 */
    private String shjnch;

    /** 记录状态 */
    private String jiluzt;
} 