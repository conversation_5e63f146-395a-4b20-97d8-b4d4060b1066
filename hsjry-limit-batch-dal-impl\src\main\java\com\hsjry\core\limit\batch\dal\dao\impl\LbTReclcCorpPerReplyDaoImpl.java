package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.core.limit.batch.dal.dao.intf.LbTReclcCorpPerReplyDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTReclcCorpPerReplyMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpPerReplyDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpPerReplyExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpPerReplyKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcCorpPerReplyQuery;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中对公客户中单笔单批额度数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-08-21 11:31:28
 */
@Repository
public class LbTReclcCorpPerReplyDaoImpl extends AbstractBaseDaoImpl<LbTReclcCorpPerReplyDo, LbTReclcCorpPerReplyMapper>
    implements LbTReclcCorpPerReplyDao {
    /**
     * 分页查询
     *
     * @param lbTReclcCorpPerReply 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTReclcCorpPerReplyDo> selectPage(LbTReclcCorpPerReplyQuery lbTReclcCorpPerReply,
        PageParam pageParam) {
        LbTReclcCorpPerReplyExample example = buildExample(lbTReclcCorpPerReply);
        return PageHelper.<LbTReclcCorpPerReplyDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询额度中心-中间表-额度重算中对公客户中单笔单批额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public LbTReclcCorpPerReplyDo selectByKey(String custNo, String custLimitId) {
        LbTReclcCorpPerReplyKeyDo lbTReclcCorpPerReplyKeyDo = new LbTReclcCorpPerReplyKeyDo();
        lbTReclcCorpPerReplyKeyDo.setCustNo(custNo);
        lbTReclcCorpPerReplyKeyDo.setCustLimitId(custLimitId);
        return getMapper().selectByPrimaryKey(lbTReclcCorpPerReplyKeyDo);
    }

    /**
     * 根据key删除额度中心-中间表-额度重算中对公客户中单笔单批额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public int deleteByKey(String custNo, String custLimitId) {
        LbTReclcCorpPerReplyKeyDo lbTReclcCorpPerReplyKeyDo = new LbTReclcCorpPerReplyKeyDo();
        lbTReclcCorpPerReplyKeyDo.setCustNo(custNo);
        lbTReclcCorpPerReplyKeyDo.setCustLimitId(custLimitId);
        return getMapper().deleteByPrimaryKey(lbTReclcCorpPerReplyKeyDo);
    }

    /**
     * 查询额度中心-中间表-额度重算中对公客户中单笔单批额度信息
     *
     * @param lbTReclcCorpPerReply 条件
     * @return List<LbTReclcCorpPerReplyDo>
     */
    @Override
    public List<LbTReclcCorpPerReplyDo> selectByExample(LbTReclcCorpPerReplyQuery lbTReclcCorpPerReply) {
        return getMapper().selectByExample(buildExample(lbTReclcCorpPerReply));
    }

    /**
     * 新增额度中心-中间表-额度重算中对公客户中单笔单批额度信息
     *
     * @param lbTReclcCorpPerReply 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTReclcCorpPerReplyDo lbTReclcCorpPerReply) {
        if (lbTReclcCorpPerReply == null) {
            return -1;
        }

        return getMapper().insertSelective(lbTReclcCorpPerReply);
    }

    /**
     * 修改额度中心-中间表-额度重算中对公客户中单笔单批额度信息
     *
     * @param lbTReclcCorpPerReply
     * @return
     */
    @Override
    public int updateBySelective(LbTReclcCorpPerReplyDo lbTReclcCorpPerReply) {
        if (lbTReclcCorpPerReply == null) {
            return -1;
        }
        return getMapper().updateByPrimaryKeySelective(lbTReclcCorpPerReply);
    }

    @Override
    public int updateBySelectiveByExample(LbTReclcCorpPerReplyDo lbTReclcCorpPerReply,
        LbTReclcCorpPerReplyQuery lbTReclcCorpPerReplyQuery) {
        return getMapper().updateByExampleSelective(lbTReclcCorpPerReply, buildExample(lbTReclcCorpPerReplyQuery));
    }

    /**
     * 构建额度中心-中间表-额度重算中对公客户中单笔单批额度Example信息
     *
     * @param lbTReclcCorpPerReply
     * @return
     */
    public LbTReclcCorpPerReplyExample buildExample(LbTReclcCorpPerReplyQuery lbTReclcCorpPerReply) {
        LbTReclcCorpPerReplyExample example = new LbTReclcCorpPerReplyExample();
        LbTReclcCorpPerReplyExample.Criteria criteria = example.createCriteria();
        if (lbTReclcCorpPerReply != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbTReclcCorpPerReply.getCustNo())) {
                criteria.andCustNoEqualTo(lbTReclcCorpPerReply.getCustNo());
            }
            if (StringUtil.isNotEmpty(lbTReclcCorpPerReply.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lbTReclcCorpPerReply.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lbTReclcCorpPerReply.getTemplateNodeId())) {
                criteria.andTemplateNodeIdEqualTo(lbTReclcCorpPerReply.getTemplateNodeId());
            }
            if (StringUtil.isNotEmpty(lbTReclcCorpPerReply.getLimitStatus())) {
                criteria.andLimitStatusEqualTo(lbTReclcCorpPerReply.getLimitStatus());
            }
            if (null != lbTReclcCorpPerReply.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbTReclcCorpPerReply.getTotalAmount());
            }
            if (null != lbTReclcCorpPerReply.getPreOccupyAmount()) {
                criteria.andPreOccupyAmountEqualTo(lbTReclcCorpPerReply.getPreOccupyAmount());
            }
            if (null != lbTReclcCorpPerReply.getRealOccupyAmount()) {
                criteria.andRealOccupyAmountEqualTo(lbTReclcCorpPerReply.getRealOccupyAmount());
            }
            if (null != lbTReclcCorpPerReply.getLowRiskAmount()) {
                criteria.andLowRiskAmountEqualTo(lbTReclcCorpPerReply.getLowRiskAmount());
            }
            if (null != lbTReclcCorpPerReply.getPreOccupyLowRiskAmt()) {
                criteria.andPreOccupyLowRiskAmtEqualTo(lbTReclcCorpPerReply.getPreOccupyLowRiskAmt());
            }
            if (null != lbTReclcCorpPerReply.getRealOccupyLowRiskAmt()) {
                criteria.andRealOccupyLowRiskAmtEqualTo(lbTReclcCorpPerReply.getRealOccupyLowRiskAmt());
            }
        }
        buildExampleExt(lbTReclcCorpPerReply, criteria);
        return example;
    }

    /**
     * 构建额度中心-中间表-额度重算中对公客户中单笔单批额度ExampleExt方法
     *
     * @param lbTReclcCorpPerReply
     * @return
     */
    public void buildExampleExt(LbTReclcCorpPerReplyQuery lbTReclcCorpPerReply,
        LbTReclcCorpPerReplyExample.Criteria criteria) {

        //自定义实现
    }

    // ==================== 单笔单批额度重算相关方法实现 ====================

    /**
     * 3.1.清空单笔单批额度中间表
     */
    @Override
    public int truncateSingleBatchLimit() {
        return getMapper().truncateSingleBatchLimit();
    }

    /**
     * 3.2.插入单笔单批额度客户编号和额度编号
     */
    @Override
    public int insertSingleBatchLimit() {
        return getMapper().insertSingleBatchLimit();
    }

    /**
     * 3.3.更新单笔单批额度中间表金额信息
     */
    @Override
    public int mergeSingleBatchLimitAmount() {
        return getMapper().mergeSingleBatchLimitAmount();
    }

    /**
     * 3.4.更新额度实例金额信息
     */
    @Override
    public int mergeSingleBatchLimitInstance() {
        return getMapper().mergeSingleBatchLimitInstance();
    }

}
