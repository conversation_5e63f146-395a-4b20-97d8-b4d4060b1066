package com.hsjry.core.limit.batch.core.bo;

import java.io.Serializable;

import com.hsjry.lang.mybatis.pagehelper.PageParam;

import lombok.Builder;
import lombok.Data;

/**
 * 国结系统-落地表-信用证信息
 *
 * <AUTHOR>
 * @date 2025-08-20 02:51:44
 */
@Data
@Builder
public class LbSItnstLcInfoBo implements Serializable {
    private static final long serialVersionUID = 1957998959763062785L;
    /** 申请人客户号 */
    private String appNo;
    /** 信用证号 */
    private String lcNo;
    /** 信贷合同号 */
    private String contNo;
    /** 交易号 */
    private String tradeNo;
    /** 信用证金额 */
    private java.math.BigDecimal lcAmt;
    /** 上浮比例 */
    private java.math.BigDecimal lcAmtTolerUp;
    /** 下浮比例 */
    private java.math.BigDecimal lcAmtTolerDown;
    /** 最大开证金额 */
    private java.math.BigDecimal lcMaxAmt;
    /** 币种 */
    private String lcCurSign;
    /** 开证日期 */
    private String issueDate;
}
