package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbSItnstLcInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSItnstLcInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 国结系统-落地表-信用证信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-08-20 02:51:44
 */
public interface LbSItnstLcInfoDao extends IBaseDao<LbSItnstLcInfoDo> {
    /**
     * 分页查询国结系统-落地表-信用证信息
     *
     * @param lbSItnstLcInfoQuery 条件
     * @return PageInfo<LbSItnstLcInfoDo>
     */
    PageInfo<LbSItnstLcInfoDo> selectPage(LbSItnstLcInfoQuery lbSItnstLcInfoQuery, PageParam pageParam);

    /**
     * 根据key查询国结系统-落地表-信用证信息
     *
     * @param lcNo
     * @return
     */
    LbSItnstLcInfoDo selectByKey(String lcNo);

    /**
     * 根据key删除国结系统-落地表-信用证信息
     *
     * @param lcNo
     * @return
     */
    int deleteByKey(String lcNo);

    /**
     * 查询国结系统-落地表-信用证信息信息
     *
     * @param lbSItnstLcInfoQuery 条件
     * @return List<LbSItnstLcInfoDo>
     */
    List<LbSItnstLcInfoDo> selectByExample(LbSItnstLcInfoQuery lbSItnstLcInfoQuery);

    /**
     * 新增国结系统-落地表-信用证信息信息
     *
     * @param lbSItnstLcInfo 条件
     * @return int>
     */
    int insertBySelective(LbSItnstLcInfoDo lbSItnstLcInfo);

    /**
     * 修改国结系统-落地表-信用证信息信息
     *
     * @param lbSItnstLcInfo
     * @return
     */
    int updateBySelective(LbSItnstLcInfoDo lbSItnstLcInfo);

    /**
     * 修改国结系统-落地表-信用证信息信息
     *
     * @param lbSItnstLcInfo
     * @param lbSItnstLcInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbSItnstLcInfoDo lbSItnstLcInfo, LbSItnstLcInfoQuery lbSItnstLcInfoQuery);

    /**
     * 批量插入国结系统信用证信息表-落地信息
     *
     * @param lbSItnstLcInfoList 批量数据
     * @return int
     */
    @Override
    int insertList(List<LbSItnstLcInfoDo> lbSItnstLcInfoList);

    /**
     * 清空国结系统信用证信息表-落地所有数据
     *
     * @return int
     */
    int deleteAll();

    /**
     * 查询分片数据
     *
     * @param query 查询条件
     * @return List<LbSItnstLcInfoDo>
     */
    List<LbSItnstLcInfoDo> selectShardingData(LbSItnstLcInfoQuery query);
}
