package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbSElcblIbnkLmtSynzDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSElcblIbnkLmtSynzQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 电票系统-落地表-同业客户额度同步数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-17 07:30:04
 */
public interface LbSElcblIbnkLmtSynzDao extends IBaseDao<LbSElcblIbnkLmtSynzDo> {
    /**
     * 分页查询电票系统-落地表-同业客户额度同步
     *
     * @param lbSElcblIbnkLmtSynzQuery 条件
     * @return PageInfo<LbSElcblIbnkLmtSynzDo>
     */
    PageInfo<LbSElcblIbnkLmtSynzDo> selectPage(LbSElcblIbnkLmtSynzQuery lbSElcblIbnkLmtSynzQuery, PageParam pageParam);

    /**
     * 根据key查询电票系统-落地表-同业客户额度同步
     *
     * @param id 主键ID
     * @return
     */
    LbSElcblIbnkLmtSynzDo selectByKey(String id);

    /**
     * 根据key删除电票系统-落地表-同业客户额度同步
     *
     * @param id 主键ID
     * @return
     */
    int deleteByKey(String id);

    /**
     * 查询电票系统-落地表-同业客户额度同步信息
     *
     * @param lbSElcblIbnkLmtSynzQuery 条件
     * @return List<LbSElcblIbnkLmtSynzDo>
     */
    List<LbSElcblIbnkLmtSynzDo> selectByExample(LbSElcblIbnkLmtSynzQuery lbSElcblIbnkLmtSynzQuery);

    /**
     * 新增电票系统-落地表-同业客户额度同步信息
     *
     * @param lbSElcblIbnkLmtSynz 条件
     * @return int>
     */
    int insertBySelective(LbSElcblIbnkLmtSynzDo lbSElcblIbnkLmtSynz);

    /**
     * 修改电票系统-落地表-同业客户额度同步信息
     *
     * @param lbSElcblIbnkLmtSynz
     * @return
     */
    int updateBySelective(LbSElcblIbnkLmtSynzDo lbSElcblIbnkLmtSynz);

    /**
     * 修改电票系统-落地表-同业客户额度同步信息
     *
     * @param lbSElcblIbnkLmtSynz
     * @param lbSElcblIbnkLmtSynzQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbSElcblIbnkLmtSynzDo lbSElcblIbnkLmtSynz,
        LbSElcblIbnkLmtSynzQuery lbSElcblIbnkLmtSynzQuery);

    /**
     * 批量插入电票系统-落地表-同业客户额度同步信息
     *
     * @param lbSElcblIbnkLmtSynzList 批量数据
     * @return int
     */
    @Override
    int insertList(List<LbSElcblIbnkLmtSynzDo> lbSElcblIbnkLmtSynzList);

    /**
     * 清空电票系统-落地表-同业客户额度同步所有数据
     *
     * @return int
     */
    int deleteAll();

    /**
     * 获取第一个对象，用于分片查询
     * 根据ibnkUserId主键排序，获取指定偏移量的第一条记录
     *
     * @param query 查询条件
     * @return 第一条记录，如果没有则返回null
     */
    LbSElcblIbnkLmtSynzDo selectFirstOne(LbSElcblIbnkLmtSynzQuery query);

    /**
     * 查询当前分片主键范围内的数据总数
     * 根据ibnkUserId主键范围统计当前分片的数据量
     *
     * @param query 查询条件，需包含ibnkUserId范围
     * @return 当前分片的数据量
     */
    Integer selectCountByCurrentGroup(LbSElcblIbnkLmtSynzQuery query);

    /**
     * 查询分片数据列表
     * 支持offset/limit分页查询
     *
     * @param query 查询条件，包含offset和limit
     * @return 分片数据列表
     */
    List<LbSElcblIbnkLmtSynzDo> selectShardList(LbSElcblIbnkLmtSynzQuery query);
}
