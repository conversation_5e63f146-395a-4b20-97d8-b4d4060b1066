-- 电票系统同业客户额度同步表数据质量检查脚本
-- 用于排查 LB_T_ELCBL_IBNK_PROD_LMT_INFO_PROCESS 作业无限循环问题

-- 1. 检查重复的 ibnk_user_id（这是导致无限循环的主要原因）
SELECT '重复ibnk_user_id检查' as check_type, 
       ibnk_user_id, 
       COUNT(*) as duplicate_count,
       MIN(id) as min_id,
       MAX(id) as max_id
FROM lb_s_elcbl_ibnk_lmt_synz 
GROUP BY ibnk_user_id 
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC;

-- 2. 检查 NULL 值
SELECT '空值检查' as check_type,
       COUNT(*) as total_records,
       COUNT(CASE WHEN ibnk_user_id IS NULL THEN 1 END) as null_ibnk_user_id,
       COUNT(CASE WHEN ibnk_user_certificate_kind IS NULL THEN 1 END) as null_certificate_kind,
       COUNT(CASE WHEN ibnk_user_certificate_no IS NULL THEN 1 END) as null_certificate_no,
       COUNT(CASE WHEN core_inst_no IS NULL THEN 1 END) as null_core_inst_no
FROM lb_s_elcbl_ibnk_lmt_synz;

-- 3. 检查数据总量和分布
SELECT '数据分布检查' as check_type,
       COUNT(*) as total_count,
       COUNT(DISTINCT ibnk_user_id) as unique_ibnk_user_id_count,
       COUNT(DISTINCT core_inst_no) as unique_core_inst_no_count,
       MIN(ibnk_user_id) as min_ibnk_user_id,
       MAX(ibnk_user_id) as max_ibnk_user_id
FROM lb_s_elcbl_ibnk_lmt_synz;

-- 4. 检查 ibnk_user_id 的数据格式和长度
SELECT '数据格式检查' as check_type,
       LENGTH(ibnk_user_id) as id_length,
       COUNT(*) as count,
       MIN(ibnk_user_id) as sample_min,
       MAX(ibnk_user_id) as sample_max
FROM lb_s_elcbl_ibnk_lmt_synz 
WHERE ibnk_user_id IS NOT NULL
GROUP BY LENGTH(ibnk_user_id)
ORDER BY id_length;

-- 5. 检查可能导致排序问题的特殊字符
SELECT '特殊字符检查' as check_type,
       ibnk_user_id,
       LENGTH(ibnk_user_id) as id_length,
       ASCII(SUBSTR(ibnk_user_id, 1, 1)) as first_char_ascii,
       core_inst_no
FROM lb_s_elcbl_ibnk_lmt_synz 
WHERE ibnk_user_id IS NOT NULL
  AND (ASCII(SUBSTR(ibnk_user_id, 1, 1)) < 32 
       OR ASCII(SUBSTR(ibnk_user_id, 1, 1)) > 126
       OR INSTR(ibnk_user_id, CHR(0)) > 0)
ORDER BY ibnk_user_id;

-- 6. 模拟分片查询，检查是否会出现无限循环
-- 这个查询模拟了原始的分片逻辑
WITH test_pagination AS (
  SELECT ibnk_user_id, 
         ROW_NUMBER() OVER (ORDER BY ibnk_user_id ASC, id ASC) as rn
  FROM lb_s_elcbl_ibnk_lmt_synz
  WHERE ibnk_user_id IS NOT NULL
)
SELECT '分页测试' as check_type,
       COUNT(*) as total_pages_needed,
       MIN(ibnk_user_id) as first_id,
       MAX(ibnk_user_id) as last_id
FROM test_pagination;

-- 7. 检查最近的数据更新时间
SELECT '数据时效性检查' as check_type,
       MIN(create_time) as earliest_create_time,
       MAX(create_time) as latest_create_time,
       MIN(update_time) as earliest_update_time,
       MAX(update_time) as latest_update_time,
       COUNT(*) as total_records
FROM lb_s_elcbl_ibnk_lmt_synz;

-- 8. 如果发现重复数据，提供清理建议（仅查询，不执行删除）
SELECT '重复数据清理建议' as check_type,
       'DELETE FROM lb_s_elcbl_ibnk_lmt_synz WHERE id = ''' || id || ''';' as cleanup_sql
FROM (
    SELECT id, ibnk_user_id,
           ROW_NUMBER() OVER (PARTITION BY ibnk_user_id ORDER BY create_time DESC, id DESC) as rn
    FROM lb_s_elcbl_ibnk_lmt_synz
) ranked
WHERE rn > 1  -- 保留每个 ibnk_user_id 的最新记录
ORDER BY ibnk_user_id, id;
