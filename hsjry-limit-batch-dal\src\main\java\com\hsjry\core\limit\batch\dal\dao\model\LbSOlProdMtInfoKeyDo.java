package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度产品信息主键
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
@Table(name = "lb_s_ol_prod_mt_info")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LbSOlProdMtInfoKeyDo implements Serializable {

    private static final long serialVersionUID = 1942415996337979395L;
    /** 额度编号：系统生成的唯一标识 */
    @Id
    @Column(name = "credit_limit_id")
    private String creditLimitId;
    /** 租户号：多租户系统中的租户标识 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
}