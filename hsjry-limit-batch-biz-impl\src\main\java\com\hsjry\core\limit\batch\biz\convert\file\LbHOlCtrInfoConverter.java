/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.convert.file;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.hsjry.core.limit.batch.biz.entity.LbHOlCtrInfoData;
import com.hsjry.core.limit.batch.dal.dao.model.LbHOlCtrInfoDo;

import lombok.extern.slf4j.Slf4j;

/**
 * 网贷系统-历史表-合同信息转换器
 * 提供高性能的数据转换功能，支持批量处理和内存优化
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/22 16:00
 */
@Slf4j
@Component
public class LbHOlCtrInfoConverter {

    /** 默认初始容量 */
    private static final int DEFAULT_INITIAL_CAPACITY = 16;
    /** 负载因子 */
    private static final float LOAD_FACTOR = 0.75f;
    /** 批处理大小 */
    private static final int BATCH_SIZE = 1000;

    /**
     * Data转DO（高性能版本）
     *
     * @param data 源数据对象
     * @return 目标DO对象
     */
    public static LbHOlCtrInfoDo data2Do(LbHOlCtrInfoData data) {
        return LbHOlCtrInfoCnvs.INSTANCE.data2Do(data);
    }

    /**
     * DO转Data（高性能版本）
     *
     * @param dataObject DO对象
     * @return 目标Data对象
     */
    public static LbHOlCtrInfoData do2Data(LbHOlCtrInfoDo dataObject) {
        return LbHOlCtrInfoCnvs.INSTANCE.do2Data(dataObject);
    }

    /**
     * Data列表转DO列表（高性能版本）
     * 使用并行流和预估容量优化性能
     *
     * @param dataList 源数据列表
     * @return 目标DO列表
     */
    public static List<LbHOlCtrInfoDo> dataListToDoList(List<LbHOlCtrInfoData> dataList) {
        if (Objects.isNull(dataList)) {
            return Collections.emptyList();
        }
        
        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (dataList.size() / LOAD_FACTOR) + 1, DEFAULT_INITIAL_CAPACITY);
        // 使用并行流提升性能
        return dataList.parallelStream()
                .filter(Objects::nonNull)
                .map(LbHOlCtrInfoConverter::data2Do)
                .collect(Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
    }

    /**
     * DO列表转Data列表（高性能版本）
     * 反向批量转换
     *
     * @param doList DO对象列表
     * @return Data对象列表
     */
    public static List<LbHOlCtrInfoData> doListToDataList(List<LbHOlCtrInfoDo> doList) {
        if (Objects.isNull(doList)) {
            return Collections.emptyList();
        }
        
        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (doList.size() / LOAD_FACTOR) + 1, DEFAULT_INITIAL_CAPACITY);
        return doList.parallelStream()
                .filter(Objects::nonNull)
                .map(LbHOlCtrInfoConverter::do2Data)
                .collect(Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
    }

    /**
     * 批量转换，支持大数据量处理
     * 按批次处理，避免内存溢出
     *
     * @param dataList 源数据列表
     * @return 目标DO列表
     */
    public static List<LbHOlCtrInfoDo> batchConvertData2Do(List<LbHOlCtrInfoData> dataList) {
        if (Objects.isNull(dataList) || dataList.isEmpty()) {
            return Collections.emptyList();
        }

        if (dataList.size() <= BATCH_SIZE) {
            return dataListToDoList(dataList);
        }

        List<LbHOlCtrInfoDo> result = Lists.newArrayListWithCapacity(dataList.size());
        for (int i = 0; i < dataList.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, dataList.size());
            List<LbHOlCtrInfoData> batch = dataList.subList(i, endIndex);
            result.addAll(dataListToDoList(batch));
        }
        
        return result;
    }
} 