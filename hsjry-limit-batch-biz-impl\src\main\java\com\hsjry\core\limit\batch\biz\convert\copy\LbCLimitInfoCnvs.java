package com.hsjry.core.limit.batch.biz.convert.copy;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.hsjry.core.limit.batch.common.dto.file.LbCLimitInfoDto;
import com.hsjry.core.limit.batch.dal.dao.model.LbCLimitInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoDo;

/**
 * 额度实例信息转换器
 *
 * <AUTHOR>
 * @version V3.0.5
 * @since 2025/7/510:32
 */
@Mapper(componentModel = "spring")
public interface LbCLimitInfoCnvs  {
    LbCLimitInfoCnvs INSTANCE = Mappers.getMapper(LbCLimitInfoCnvs.class);

    LbCLimitInfoDo do2Copy(LcCustLimitInfoDo model);

    LbCLimitInfoDo dtoToDo(LbCLimitInfoDto dto);

    LbCLimitInfoDto do2Dto(LbCLimitInfoDo model);
}
