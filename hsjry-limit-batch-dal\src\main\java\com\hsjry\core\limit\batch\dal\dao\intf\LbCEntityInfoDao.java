package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbCEntityInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbCEntityInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 实体信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-03 10:03:12
 */
public interface LbCEntityInfoDao extends IBaseDao<LbCEntityInfoDo> {
    /**
     * 分页查询实体信息
     *
     * @param lbCEntityInfoQuery 条件
     * @return PageInfo<LbCEntityInfoDo>
     */
    PageInfo<LbCEntityInfoDo> selectPage(LbCEntityInfoQuery lbCEntityInfoQuery, PageParam pageParam);

    /**
     * 根据key查询实体信息
     *
     * @param entityId
     * @return
     */
    LbCEntityInfoDo selectByKey(String entityId);

    /**
     * 根据key删除实体信息
     *
     * @param entityId
     * @return
     */
    int deleteByKey(String entityId);

    /**
     * 查询实体信息信息
     *
     * @param lbCEntityInfoQuery 条件
     * @return List<LbCEntityInfoDo>
     */
    List<LbCEntityInfoDo> selectByExample(LbCEntityInfoQuery lbCEntityInfoQuery);

    /**
     * 新增实体信息信息
     *
     * @param lbCEntityInfo 条件
     * @return int>
     */
    int insertBySelective(LbCEntityInfoDo lbCEntityInfo);

    /**
     * 修改实体信息信息
     *
     * @param lbCEntityInfo
     * @return
     */
    int updateBySelective(LbCEntityInfoDo lbCEntityInfo);

    /**
     * 修改实体信息信息
     *
     * @param lbCEntityInfo
     * @param lbCEntityInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbCEntityInfoDo lbCEntityInfo, LbCEntityInfoQuery lbCEntityInfoQuery);

    /**
     * 批量插入实体信息
     *
     * @param lbCEntityInfoList 批量数据
     * @return int
     */
    int insertList(List<LbCEntityInfoDo> lbCEntityInfoList);

    /**
     * 清空实体信息表所有数据
     * 用于备份同步作业的全量数据刷新
     *
     * @return 删除的记录数
     */
    int deleteAll();
}
