<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.batch.dal.dao.mapper.LbSElcblDsctBalInfoMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.batch.dal.dao.model.LbSElcblDsctBalInfoDo">
        <result property="orgNo" column="org_no" jdbcType="VARCHAR"/> <!-- 法人行机构编号 -->
        <result property="dicCno" column="dic_cno" jdbcType="VARCHAR"/> <!-- 贴现编号(核心借据号) -->
        <result property="billNo" column="bill_no" jdbcType="VARCHAR"/> <!-- 票据编号 -->
        <result property="billRangeStart" column="bill_range_start" jdbcType="VARCHAR"/> <!-- 子票区间起始 -->
        <result property="billRangeEnd" column="bill_range_end" jdbcType="VARCHAR"/> <!-- 子票区间截止 -->
        <result property="userId" column="user_id" jdbcType="VARCHAR"/> <!-- 贴现客户编号(对公客户编号) -->
        <result property="userName" column="user_name" jdbcType="VARCHAR"/> <!-- 贴现客户名称(对公客户名称) -->
        <result property="currency" column="currency" jdbcType="VARCHAR"/> <!-- 票据币种 -->
        <result property="discountAmt" column="discount_amt" jdbcType="DECIMAL"/> <!-- 贴现金额(票面金额) -->
        <result property="discountBal" column="discount_bal" jdbcType="DECIMAL"/> <!-- 贴现余额 -->
        <result property="startDate" column="start_date" jdbcType="VARCHAR"/> <!-- 贴现起始日期 -->
        <result property="endDate" column="end_date" jdbcType="VARCHAR"/> <!-- 贴现到期日期 -->
        <result property="createTime" column="create_time" jdbcType="VARCHAR"/> <!-- 创建时间 -->
        <result property="updateTime" column="update_time" jdbcType="VARCHAR"/> <!-- 更新时间 -->
    </resultMap>
    <sql id="Base_Column_List">
        org_no
        , dic_cno
                , bill_no
                , bill_range_start
                , bill_range_end
                , user_id
                , user_name
                , currency
                , discount_amt
                , discount_bal
                , start_date
                , end_date
                , create_time
                , update_time
    </sql>
    <!-- 批量插入贴现余额信息 -->
    <insert id="insertList" parameterType="java.util.List">
        INSERT ALL
        <foreach collection="list" item="item" separator="">
            INTO lb_s_elcbl_dsct_bal_info (
            org_no, dic_cno, bill_no, bill_range_start, bill_range_end, user_id, user_name, currency, discount_amt,
            discount_bal, start_date, end_date, create_time, update_time
            ) VALUES (
            #{item.orgNo, jdbcType=VARCHAR},
            #{item.dicCno, jdbcType=VARCHAR},
            #{item.billNo, jdbcType=VARCHAR},
            #{item.billRangeStart, jdbcType=VARCHAR},
            #{item.billRangeEnd, jdbcType=VARCHAR},
            #{item.userId, jdbcType=VARCHAR},
            #{item.userName, jdbcType=VARCHAR},
            #{item.currency, jdbcType=VARCHAR},
            #{item.discountAmt, jdbcType=DECIMAL},
            #{item.discountBal, jdbcType=DECIMAL},
            #{item.startDate, jdbcType=VARCHAR},
            #{item.endDate, jdbcType=VARCHAR},
            #{item.createTime, jdbcType=VARCHAR},
            #{item.updateTime, jdbcType=VARCHAR}
            )
        </foreach>
        SELECT * FROM DUAL
    </insert>

    <!-- 清空贴现余额信息表所有数据 -->
    <delete id="deleteAll">
        TRUNCATE TABLE lb_s_elcbl_dsct_bal_info
    </delete>

    <!-- 获取第一个对象，用于分片查询 -->
    <select id="selectFirstOne" resultMap="BaseResultMap" parameterType="com.hsjry.core.limit.batch.dal.dao.query.LbSElcblDsctBalInfoQuery">
        SELECT * FROM (
            SELECT
            <include refid="Base_Column_List"/>
            FROM lb_s_elcbl_dsct_bal_info
            <where>
                <if test="query != null and query.dicCno != null and query.dicCno != ''">
                    AND dic_cno &gt; #{query.dicCno,jdbcType=VARCHAR}
                </if>
                <if test="query != null and query.billNo != null and query.billNo != ''">
                    AND bill_no &gt; #{query.billNo,jdbcType=VARCHAR}
                </if>
                <if test="query != null and query.userId != null and query.userId != ''">
                    AND user_id &gt; #{query.userId,jdbcType=VARCHAR}
                </if>
                <if test="query != null and query.orgNo != null and query.orgNo != ''">
                    AND org_no = #{query.orgNo,jdbcType=VARCHAR}
                </if>
                <if test="query != null and query.billRangeStart != null and query.billRangeStart != ''">
                    AND bill_range_start = #{query.billRangeStart,jdbcType=VARCHAR}
                </if>
                <if test="query != null and query.billRangeEnd != null and query.billRangeEnd != ''">
                    AND bill_range_end = #{query.billRangeEnd,jdbcType=VARCHAR}
                </if>
                <if test="query != null and query.userName != null and query.userName != ''">
                    AND user_name = #{query.userName,jdbcType=VARCHAR}
                </if>
                <if test="query != null and query.currency != null and query.currency != ''">
                    AND currency = #{query.currency,jdbcType=VARCHAR}
                </if>
                <if test="query != null and query.discountAmt != null">
                    AND discount_amt = #{query.discountAmt,jdbcType=DECIMAL}
                </if>
                <if test="query != null and query.discountBal != null">
                    AND discount_bal = #{query.discountBal,jdbcType=DECIMAL}
                </if>
                <if test="query != null and query.startDate != null and query.startDate != ''">
                    AND start_date = #{query.startDate,jdbcType=VARCHAR}
                </if>
                <if test="query != null and query.endDate != null and query.endDate != ''">
                    AND end_date = #{query.endDate,jdbcType=VARCHAR}
                </if>
            </where>
            ORDER BY dic_cno ASC, bill_no ASC, user_id ASC
        ) WHERE ROWNUM = 1
    </select>

    <!-- 查询当前分片主键范围内的数据总数 -->
    <select id="selectCountByCurrentGroup" resultType="java.lang.Integer" parameterType="com.hsjry.core.limit.batch.dal.dao.query.LbSElcblDsctBalInfoQuery">
        SELECT COUNT(1)
        FROM lb_s_elcbl_dsct_bal_info
        <where>
            <if test="query != null and query.dicCno != null and query.dicCno != ''">
                AND dic_cno &gt; #{query.dicCno,jdbcType=VARCHAR}
            </if>
            <if test="query != null and query.billNo != null and query.billNo != ''">
                AND bill_no &gt; #{query.billNo,jdbcType=VARCHAR}
            </if>
            <if test="query != null and query.userId != null and query.userId != ''">
                AND user_id &gt; #{query.userId,jdbcType=VARCHAR}
            </if>
            <if test="query != null and query.orgNo != null and query.orgNo != ''">
                AND org_no = #{query.orgNo,jdbcType=VARCHAR}
            </if>
            <if test="query != null and query.billRangeStart != null and query.billRangeStart != ''">
                AND bill_range_start = #{query.billRangeStart,jdbcType=VARCHAR}
            </if>
            <if test="query != null and query.billRangeEnd != null and query.billRangeEnd != ''">
                AND bill_range_end = #{query.billRangeEnd,jdbcType=VARCHAR}
            </if>
            <if test="query != null and query.userName != null and query.userName != ''">
                AND user_name = #{query.userName,jdbcType=VARCHAR}
            </if>
            <if test="query != null and query.currency != null and query.currency != ''">
                AND currency = #{query.currency,jdbcType=VARCHAR}
            </if>
            <if test="query != null and query.discountAmt != null">
                AND discount_amt = #{query.discountAmt,jdbcType=DECIMAL}
            </if>
            <if test="query != null and query.discountBal != null">
                AND discount_bal = #{query.discountBal,jdbcType=DECIMAL}
            </if>
            <if test="query != null and query.startDate != null and query.startDate != ''">
                AND start_date = #{query.startDate,jdbcType=VARCHAR}
            </if>
            <if test="query != null and query.endDate != null and query.endDate != ''">
                AND end_date = #{query.endDate,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!-- 查询分片数据列表，支持offset/limit分页查询 -->
    <select id="selectShardList" resultMap="BaseResultMap" parameterType="com.hsjry.core.limit.batch.dal.dao.query.LbSElcblDsctBalInfoQuery">
        SELECT * FROM (
            SELECT ROWNUM rn,
            <include refid="Base_Column_List"/>
            FROM (
                SELECT
                <include refid="Base_Column_List"/>
                FROM lb_s_elcbl_dsct_bal_info
                <where>
                    <if test="query != null and query.dicCno != null and query.dicCno != ''">
                        AND dic_cno = #{query.dicCno,jdbcType=VARCHAR}
                    </if>
                    <if test="query != null and query.billNo != null and query.billNo != ''">
                        AND bill_no = #{query.billNo,jdbcType=VARCHAR}
                    </if>
                    <if test="query != null and query.userId != null and query.userId != ''">
                        AND user_id = #{query.userId,jdbcType=VARCHAR}
                    </if>
                    <if test="query != null and query.orgNo != null and query.orgNo != ''">
                        AND org_no = #{query.orgNo,jdbcType=VARCHAR}
                    </if>
                    <if test="query != null and query.billRangeStart != null and query.billRangeStart != ''">
                        AND bill_range_start = #{query.billRangeStart,jdbcType=VARCHAR}
                    </if>
                    <if test="query != null and query.billRangeEnd != null and query.billRangeEnd != ''">
                        AND bill_range_end = #{query.billRangeEnd,jdbcType=VARCHAR}
                    </if>
                    <if test="query != null and query.userName != null and query.userName != ''">
                        AND user_name = #{query.userName,jdbcType=VARCHAR}
                    </if>
                    <if test="query != null and query.currency != null and query.currency != ''">
                        AND currency = #{query.currency,jdbcType=VARCHAR}
                    </if>
                    <if test="query != null and query.discountAmt != null">
                        AND discount_amt = #{query.discountAmt,jdbcType=DECIMAL}
                    </if>
                    <if test="query != null and query.discountBal != null">
                        AND discount_bal = #{query.discountBal,jdbcType=DECIMAL}
                    </if>
                    <if test="query != null and query.startDate != null and query.startDate != ''">
                        AND start_date = #{query.startDate,jdbcType=VARCHAR}
                    </if>
                    <if test="query != null and query.endDate != null and query.endDate != ''">
                        AND end_date = #{query.endDate,jdbcType=VARCHAR}
                    </if>
                </where>
                ORDER BY dic_cno ASC, bill_no ASC, user_id ASC
            )
        ) WHERE rn &gt; #{query.offset,jdbcType=INTEGER}
        <if test="query != null and query.limit != null and query.limit > 0">
            AND rn &lt;= #{query.offset,jdbcType=INTEGER} + #{query.limit,jdbcType=INTEGER}
        </if>
    </select>

    <!-- 根据条件统计记录数 -->
    <select id="countByExample" resultType="java.lang.Long" parameterType="com.hsjry.core.limit.batch.dal.dao.model.LbSElcblDsctBalInfoExample">
        SELECT COUNT(*)
        FROM lb_s_elcbl_dsct_bal_info
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>

    <!-- ========== Example条件子句 ========== -->

    <!-- Example查询条件子句 -->
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="OR">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="AND" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    AND ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    AND ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    AND ${criterion.condition} #{criterion.value} AND #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    AND ${criterion.condition}
                                    <foreach collection="criterion.value" item="listItem" open="(" close=")" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>

</mapper>