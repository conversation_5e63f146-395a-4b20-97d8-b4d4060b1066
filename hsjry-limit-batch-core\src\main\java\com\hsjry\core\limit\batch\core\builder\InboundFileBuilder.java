/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.core.builder;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.hsjry.base.common.job.biz.file.FileBuilder;
import com.hsjry.base.common.job.dto.BaseFile;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.core.bo.InboundSerialFileBo;
import com.hsjry.core.limit.center.dal.dao.model.LcInboundSerialDo;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/4/12 16:46
 */
@Component
public class InboundFileBuilder implements FileBuilder<LcInboundSerialDo> {
    @Override
    public List<? extends BaseFile> buildFileBo(List<LcInboundSerialDo> dataList, Date accountDate,
        Map<String, BigDecimal> totalMap) {
        return dataList.stream()
            //构建文件类
            .map(inboundSerialDo -> InboundSerialFileBo.builder()
                .inboundSerial(inboundSerialDo.getInboundSerialNo())
                .serialNo(inboundSerialDo.getCisSerialNo())
                .tenantId(inboundSerialDo.getTenantId())
                .tradeCode(inboundSerialDo.getTradeCode())
                .tradeStatus(inboundSerialDo.getTradeStatus())
                .build())
            .collect(Collectors.toList());
    }

    @Override
    public IEnumTrade getTradeCode() {
        return EnumJobTrade.INBOUND_FILE;
    }
}
