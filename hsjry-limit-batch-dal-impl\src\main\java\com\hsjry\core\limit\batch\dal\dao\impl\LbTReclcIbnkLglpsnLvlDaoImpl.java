package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.core.limit.batch.dal.dao.intf.LbTReclcIbnkLglpsnLvlDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTReclcIbnkLglpsnLvlMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIbnkLglpsnLvlDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIbnkLglpsnLvlExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIbnkLglpsnLvlKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcIbnkLglpsnLvlQuery;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中同业客户中法人综合授信额度数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-08-21 12:16:19
 */
@Repository
public class LbTReclcIbnkLglpsnLvlDaoImpl
    extends AbstractBaseDaoImpl<LbTReclcIbnkLglpsnLvlDo, LbTReclcIbnkLglpsnLvlMapper>
    implements LbTReclcIbnkLglpsnLvlDao {
    /**
     * 分页查询
     *
     * @param lbTReclcIbnkLglpsnLvl 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTReclcIbnkLglpsnLvlDo> selectPage(LbTReclcIbnkLglpsnLvlQuery lbTReclcIbnkLglpsnLvl,
        PageParam pageParam) {
        LbTReclcIbnkLglpsnLvlExample example = buildExample(lbTReclcIbnkLglpsnLvl);
        return PageHelper.<LbTReclcIbnkLglpsnLvlDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询额度中心-中间表-额度重算中同业客户中法人综合授信额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public LbTReclcIbnkLglpsnLvlDo selectByKey(String custNo, String custLimitId) {
        LbTReclcIbnkLglpsnLvlKeyDo lbTReclcIbnkLglpsnLvlKeyDo = new LbTReclcIbnkLglpsnLvlKeyDo();
        lbTReclcIbnkLglpsnLvlKeyDo.setCustNo(custNo);
        lbTReclcIbnkLglpsnLvlKeyDo.setCustLimitId(custLimitId);
        return getMapper().selectByPrimaryKey(lbTReclcIbnkLglpsnLvlKeyDo);
    }

    /**
     * 根据key删除额度中心-中间表-额度重算中同业客户中法人综合授信额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public int deleteByKey(String custNo, String custLimitId) {
        LbTReclcIbnkLglpsnLvlKeyDo lbTReclcIbnkLglpsnLvlKeyDo = new LbTReclcIbnkLglpsnLvlKeyDo();
        lbTReclcIbnkLglpsnLvlKeyDo.setCustNo(custNo);
        lbTReclcIbnkLglpsnLvlKeyDo.setCustLimitId(custLimitId);
        return getMapper().deleteByPrimaryKey(lbTReclcIbnkLglpsnLvlKeyDo);
    }

    /**
     * 查询额度中心-中间表-额度重算中同业客户中法人综合授信额度信息
     *
     * @param lbTReclcIbnkLglpsnLvl 条件
     * @return List<LbTReclcIbnkLglpsnLvlDo>
     */
    @Override
    public List<LbTReclcIbnkLglpsnLvlDo> selectByExample(LbTReclcIbnkLglpsnLvlQuery lbTReclcIbnkLglpsnLvl) {
        return getMapper().selectByExample(buildExample(lbTReclcIbnkLglpsnLvl));
    }

    /**
     * 新增额度中心-中间表-额度重算中同业客户中法人综合授信额度信息
     *
     * @param lbTReclcIbnkLglpsnLvl 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTReclcIbnkLglpsnLvlDo lbTReclcIbnkLglpsnLvl) {
        if (lbTReclcIbnkLglpsnLvl == null) {
            return -1;
        }

        return getMapper().insertSelective(lbTReclcIbnkLglpsnLvl);
    }

    /**
     * 修改额度中心-中间表-额度重算中同业客户中法人综合授信额度信息
     *
     * @param lbTReclcIbnkLglpsnLvl
     * @return
     */
    @Override
    public int updateBySelective(LbTReclcIbnkLglpsnLvlDo lbTReclcIbnkLglpsnLvl) {
        if (lbTReclcIbnkLglpsnLvl == null) {
            return -1;
        }

        return getMapper().updateByPrimaryKeySelective(lbTReclcIbnkLglpsnLvl);
    }

    @Override
    public int updateBySelectiveByExample(LbTReclcIbnkLglpsnLvlDo lbTReclcIbnkLglpsnLvl,
        LbTReclcIbnkLglpsnLvlQuery lbTReclcIbnkLglpsnLvlQuery) {
        return getMapper().updateByExampleSelective(lbTReclcIbnkLglpsnLvl, buildExample(lbTReclcIbnkLglpsnLvlQuery));
    }

    /**
     * 构建额度中心-中间表-额度重算中同业客户中法人综合授信额度Example信息
     *
     * @param lbTReclcIbnkLglpsnLvl
     * @return
     */
    public LbTReclcIbnkLglpsnLvlExample buildExample(LbTReclcIbnkLglpsnLvlQuery lbTReclcIbnkLglpsnLvl) {
        LbTReclcIbnkLglpsnLvlExample example = new LbTReclcIbnkLglpsnLvlExample();
        LbTReclcIbnkLglpsnLvlExample.Criteria criteria = example.createCriteria();
        if (lbTReclcIbnkLglpsnLvl != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbTReclcIbnkLglpsnLvl.getCustNo())) {
                criteria.andCustNoEqualTo(lbTReclcIbnkLglpsnLvl.getCustNo());
            }
            if (StringUtil.isNotEmpty(lbTReclcIbnkLglpsnLvl.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lbTReclcIbnkLglpsnLvl.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lbTReclcIbnkLglpsnLvl.getTemplateNodeId())) {
                criteria.andTemplateNodeIdEqualTo(lbTReclcIbnkLglpsnLvl.getTemplateNodeId());
            }
            if (StringUtil.isNotEmpty(lbTReclcIbnkLglpsnLvl.getLimitStatus())) {
                criteria.andLimitStatusEqualTo(lbTReclcIbnkLglpsnLvl.getLimitStatus());
            }
            if (null != lbTReclcIbnkLglpsnLvl.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbTReclcIbnkLglpsnLvl.getTotalAmount());
            }
            if (null != lbTReclcIbnkLglpsnLvl.getPreOccupyAmount()) {
                criteria.andPreOccupyAmountEqualTo(lbTReclcIbnkLglpsnLvl.getPreOccupyAmount());
            }
            if (null != lbTReclcIbnkLglpsnLvl.getRealOccupyAmount()) {
                criteria.andRealOccupyAmountEqualTo(lbTReclcIbnkLglpsnLvl.getRealOccupyAmount());
            }
            if (null != lbTReclcIbnkLglpsnLvl.getLowRiskAmount()) {
                criteria.andLowRiskAmountEqualTo(lbTReclcIbnkLglpsnLvl.getLowRiskAmount());
            }
            if (null != lbTReclcIbnkLglpsnLvl.getPreOccupyLowRiskAmt()) {
                criteria.andPreOccupyLowRiskAmtEqualTo(lbTReclcIbnkLglpsnLvl.getPreOccupyLowRiskAmt());
            }
            if (null != lbTReclcIbnkLglpsnLvl.getRealOccupyLowRiskAmt()) {
                criteria.andRealOccupyLowRiskAmtEqualTo(lbTReclcIbnkLglpsnLvl.getRealOccupyLowRiskAmt());
            }
            if (StringUtil.isNotEmpty(lbTReclcIbnkLglpsnLvl.getBlngLglpsnCoreInsNo())) {
                criteria.andBlngLglpsnCoreInsNoEqualTo(lbTReclcIbnkLglpsnLvl.getBlngLglpsnCoreInsNo());
            }
        }
        buildExampleExt(lbTReclcIbnkLglpsnLvl, criteria);
        return example;
    }

    /**
     * 构建额度中心-中间表-额度重算中同业客户中法人综合授信额度ExampleExt方法
     *
     * @param lbTReclcIbnkLglpsnLvl
     * @return
     */
    public void buildExampleExt(LbTReclcIbnkLglpsnLvlQuery lbTReclcIbnkLglpsnLvl,
        LbTReclcIbnkLglpsnLvlExample.Criteria criteria) {

        //自定义实现
    }

    // ==================== 同业客户法人层额度重算相关方法实现 ====================

    /**
     * 1.1.清空法人层额度中间表
     */
    @Override
    public int truncateLegalPersonLevelLimit() {
        return getMapper().truncateLegalPersonLevelLimit();
    }

    /**
     * 1.2.插入法人层额度客户编号和额度编号
     */
    @Override
    public int insertLegalPersonLevelLimit() {
        return getMapper().insertLegalPersonLevelLimit();
    }

    /**
     * 1.3.更新法人层额度中间表金额信息
     */
    @Override
    public int mergeLegalPersonLevelLimitAmount() {
        return getMapper().mergeLegalPersonLevelLimitAmount();
    }

    /**
     * 1.4.更新额度实例金额信息
     */
    @Override
    public int mergeLegalPersonLevelLimitInstance() {
        return getMapper().mergeLegalPersonLevelLimitInstance();
    }

}
