/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.impl;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import com.google.common.collect.Lists;
import com.hsjry.base.common.model.enums.common.EnumSerialModalName;
import com.hsjry.base.common.model.enums.limit.EnumAmtLimitRecordStatus;
import com.hsjry.base.common.model.enums.limit.EnumAmtLimitRuleStatus;
import com.hsjry.base.common.model.enums.limit.EnumAmtQuotaType;
import com.hsjry.base.common.model.enums.limit.EnumPlanAdjustType;
import com.hsjry.base.common.model.enums.limit.EnumPlanStatus;
import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.base.common.utils.BigDecimalUtil;
import com.hsjry.base.common.utils.BusinessSequenceUtil;
import com.hsjry.core.limit.batch.biz.AmtLimitBiz;
import com.hsjry.core.limit.batch.common.constants.LimitBatchConstants;
import com.hsjry.core.limit.center.dal.dao.intf.AmtLimitAdjustPlanBatchDao;
import com.hsjry.core.limit.center.dal.dao.intf.AmtLimitRecordInfoBatchDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcAmtLimitRecordDetailDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcAmtLimitRecordInfoDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcAmtLimitRuleDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcAmtRuleAdjustPlanDao;
import com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitRecordDetailDo;
import com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitRecordInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitRuleDo;
import com.hsjry.core.limit.center.dal.dao.model.LcAmtRuleAdjustPlanDo;
import com.hsjry.core.limit.center.dal.dao.query.LcAmtLimitRecordDetailQuery;
import com.hsjry.core.limit.center.dal.dao.query.LcAmtLimitRecordInfoQuery;
import com.hsjry.core.limit.center.dal.dao.query.LcAmtLimitRuleQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.stereotype.enums.EnumBool;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/6 15:18
 */
@Service
@Slf4j
public class AmtLimitBizImpl implements AmtLimitBiz {
    @Autowired
    private AmtLimitRecordInfoBatchDao amtLimitRecordBatchDao;
    @Autowired
    private LcAmtLimitRecordInfoDao lcAmtLimitRecordInfoDao;
    @Autowired
    private LcAmtLimitRecordDetailDao lcAmtLimitRecordDetailDao;
    @Autowired
    private LcAmtLimitRuleDao lcAmtLimitRuleDao;
    @Autowired
    private AmtLimitAdjustPlanBatchDao amtLimitAdjustPlanBatchDao;
    @Autowired
    private LcAmtRuleAdjustPlanDao lcAmtRuleAdjustPlanDao;

    @Autowired
    @Qualifier("limitTransactionNewTemplate")
    private TransactionTemplate transactionTemplate;

    @Override
    public void amtLimitValid(Date date) {
        long total = 0;
        int pageNum = 1;
        do {
            PageInfo<LcAmtLimitRecordInfoDo> pageInfo = amtLimitRecordBatchDao.queryNeedValidByPage(date,
                PageParam.Builder.getInstance()
                    .addPageNum(pageNum++)
                    .build());
            List<LcAmtLimitRecordInfoDo> infoDoList = pageInfo.getList();
            if (CollectionUtil.isNotEmpty(pageInfo.getList())) {
                List<LcAmtLimitRecordInfoDo> updateList = Lists.newArrayList();
                List<LcAmtLimitRuleDo> updateRuleList = Lists.newArrayList();
                for (LcAmtLimitRecordInfoDo infoDo : infoDoList) {
                    LcAmtLimitRecordInfoDo updateDo = new LcAmtLimitRecordInfoDo();
                    updateDo.setRecordId(infoDo.getRecordId());
                    updateDo.setRecordStatus(EnumAmtLimitRecordStatus.INEFFECTIVE.getCode());
                    updateDo.setTenantId(AppParamUtil.getTenantId());
                    updateList.add(updateDo);
                    LcAmtLimitRuleDo limitRuleDo = new LcAmtLimitRuleDo();
                    limitRuleDo.setTenantId(AppParamUtil.getTenantId());
                    limitRuleDo.setRuleId(infoDo.getRuleId());
                    limitRuleDo.setRuleStatus(EnumAmtLimitRuleStatus.DEACTIVATE.getCode());
                    updateRuleList.add(limitRuleDo);
                }
                lcAmtLimitRecordInfoDao.updateByPrimaryKeySelectiveList(updateList);
                lcAmtLimitRuleDao.updateByPrimaryKeySelectiveList(updateRuleList);
            }
            total = total == 0 ? pageInfo.getTotal() : total;
            total = total - pageInfo.getList()
                .size();

        } while (total > 0);
    }

    @Override
    public void amtLimitPlanAdjust(Date date) {
        long total = 0;
        int pageNum = 1;
        do {
            PageInfo<LcAmtRuleAdjustPlanDo> pageInfo = amtLimitAdjustPlanBatchDao.queryNeedAdjustPlan(date,
                PageParam.Builder.getInstance()
                    .addPageNum(pageNum++)
                    .build());
            List<LcAmtRuleAdjustPlanDo> infoDoList = pageInfo.getList();
            if (CollectionUtil.isNotEmpty(pageInfo.getList())) {
                amtLimitPlanAdjustHandle(infoDoList);
            }
            total = total == 0 ? pageInfo.getTotal() : total;
            total = total - pageInfo.getList()
                .size();
        } while (total > 0);
    }

    /**
     * 限额调整
     *
     * @param infoDoList
     */
    private void amtLimitPlanAdjustHandle(List<LcAmtRuleAdjustPlanDo> infoDoList) {
        List<String> ruleIdList = infoDoList.stream()
            .map(LcAmtRuleAdjustPlanDo::getRuleId)
            .collect(Collectors.toList());
        //获取记录
        Map<String, LcAmtLimitRecordInfoDo> recordInfoDoMap = getRecordInfoDoMap(ruleIdList);
        //获取规则
        Map<String, LcAmtLimitRuleDo> ruleDoMap = getRuleDoMap(ruleIdList);
        //获取当前时间
        Date now = BusinessDateUtil.getDate();
        for (LcAmtRuleAdjustPlanDo planDo : infoDoList) {
            //加锁
            try {
                LcAmtLimitRuleDo limitRuleDo = ruleDoMap.get(planDo.getRuleId());
                if (null == limitRuleDo) {
                    continue;
                }
                LcAmtLimitRecordInfoDo updateInfoDo = recordInfoDoMap.get(planDo.getRuleId());
                LcAmtLimitRecordInfoDo insertInfoDo = null;
                limitRuleDo.setUpdateTime(now);
                limitRuleDo.setTotalAmount(planDo.getPlanAmount());
                if (EnumAmtQuotaType.COCN_RATIO.getCode()
                    .equals(limitRuleDo.getQuotaType())) {
                    limitRuleDo.setCocnRatioWarnStrategy(planDo.getCocnRatioWarnStrategy());
                    limitRuleDo.setCocnRatioWarnType(planDo.getCocnRatioWarnType());
                } else {
                    limitRuleDo.setAmountWarnStrategy(planDo.getAmountWarnStrategy());
                    limitRuleDo.setAmountWarnType(planDo.getAmountWarnType());
                }
                updateInfoDo.setUpdateTime(now);
                List<LcAmtLimitRecordDetailDo> updateDetailList = Lists.newArrayList();
                List<LcAmtLimitRecordDetailDo> insertDetailList = Lists.newArrayList();
                if (EnumPlanAdjustType.CHANGE.getCode()
                    .equals(planDo.getPlanAdjustType())) {
                    List<LcAmtLimitRecordDetailDo> detailDoList = lcAmtLimitRecordDetailDao.selectByExample(
                        LcAmtLimitRecordDetailQuery.builder()
                            .recordId(updateInfoDo.getRecordId())
                            .build());
                    updateInfoDo.setEffectiveEndTime(planDo.getPlanEffectiveEndTime());
                    updateInfoDo.setEffectiveStartTime(now);
                    updateInfoDo.setTotalAmount(planDo.getPlanAmount());
                    for (LcAmtLimitRecordDetailDo lcAmtLimitRecordDetailDo : detailDoList) {
                        LcAmtLimitRecordDetailDo detailDo = new LcAmtLimitRecordDetailDo();
                        detailDo.setTotalAmount(BigDecimalUtil.splitRoundUp(planDo.getPlanAmount(), detailDoList.size(),
                            lcAmtLimitRecordDetailDo.getShardSeq()));
                        detailDo.setTenantId(lcAmtLimitRecordDetailDo.getTenantId());
                        detailDo.setRecordId(lcAmtLimitRecordDetailDo.getRecordId());
                        detailDo.setShardSeq(lcAmtLimitRecordDetailDo.getShardSeq());
                        detailDo.setUpdateTime(now);
                        updateDetailList.add(detailDo);
                    }
                } else {
                    insertInfoDo = buildRecord(limitRuleDo, planDo);
                    insertDetailList.addAll(buildRecordDetail(insertInfoDo));
                    updateInfoDo.setRecordStatus(EnumAmtLimitRecordStatus.INEFFECTIVE.getCode());
                }
                LcAmtLimitRecordInfoDo finalInsertInfoDo = insertInfoDo;
                planDo.setPlanStatus(EnumPlanStatus.SUCCESS.getCode());
                transactionTemplate.execute(transactionStatus -> {
                    lcAmtLimitRuleDao.updateBySelective(limitRuleDo);
                    if (CollectionUtil.isNotEmpty(updateDetailList)) {
                        lcAmtLimitRecordDetailDao.updateByPrimaryKeySelectiveList(updateDetailList);
                    }
                    if (CollectionUtil.isNotEmpty(insertDetailList)) {
                        lcAmtLimitRecordDetailDao.insertListByJdbc(insertDetailList);
                    }
                    if (null != finalInsertInfoDo) {

                        lcAmtLimitRecordInfoDao.insertBySelective(finalInsertInfoDo);
                    }
                    lcAmtLimitRecordInfoDao.updateBySelective(updateInfoDo);
                    lcAmtRuleAdjustPlanDao.updateBySelective(planDo);
                    return transactionStatus;
                });

                staticHandle(insertInfoDo, limitRuleDo);
            } catch (Exception e) {
                log.info("计划[{}]执行失败", planDo.getRuleAdjustPlanId());
                transactionTemplate.execute(transactionStatus -> {
                    planDo.setPlanStatus(EnumPlanStatus.FAIL.getCode());
                    lcAmtRuleAdjustPlanDao.updateBySelective(planDo);
                    return transactionStatus;
                });
            } finally {
                //解锁
            }
        }
    }

    /**
     * @param ruleIdList
     * @return
     */
    private Map<String, LcAmtLimitRuleDo> getRuleDoMap(List<String> ruleIdList) {
        List<LcAmtLimitRuleDo> ruleDoList = lcAmtLimitRuleDao.selectByExample(LcAmtLimitRuleQuery.builder()
            .ruleIdList(ruleIdList)
            .build());
        if (CollectionUtil.isEmpty(ruleDoList)) {
            return Collections.emptyMap();
        }
        return ruleDoList.stream()
            .collect(Collectors.toMap(LcAmtLimitRuleDo::getRuleId, o -> o));
    }

    /**
     * 获取记录信息
     *
     * @param ruleIdList
     * @return
     */
    private Map<String, LcAmtLimitRecordInfoDo> getRecordInfoDoMap(List<String> ruleIdList) {
        List<LcAmtLimitRecordInfoDo> recordInfoDoList = lcAmtLimitRecordInfoDao.selectByExample(
            LcAmtLimitRecordInfoQuery.builder()
                .ruleIdList(ruleIdList)
                .recordStatus(EnumAmtLimitRecordStatus.EFFECTIVE.getCode())
                .build());
        if (CollectionUtil.isNotEmpty(recordInfoDoList)) {
            return recordInfoDoList.stream()
                .collect(Collectors.toMap(LcAmtLimitRecordInfoDo::getRuleId, o -> o));
        }
        return Collections.emptyMap();
    }

    /**
     * @param insertInfoDo
     * @param limitRuleDo
     */
    private void staticHandle(LcAmtLimitRecordInfoDo insertInfoDo, LcAmtLimitRuleDo limitRuleDo) {
        if (null == insertInfoDo) {
            return;
        }
        if (EnumBool.NO.getCode()
            .equals(limitRuleDo.getStockBusinessFlag())) {
            return;
        }
        //TODO： 唤起历史统计
    }

    /**
     * 构建记录明细
     *
     * @param insertInfoDo
     * @return
     */
    private List<LcAmtLimitRecordDetailDo> buildRecordDetail(LcAmtLimitRecordInfoDo insertInfoDo) {
        if (null == insertInfoDo) {
            return null;
        }
        List<LcAmtLimitRecordDetailDo> detailDoList = Lists.newArrayList();
        for (int i = 0; i < LimitBatchConstants.AMT_LIMIT_DETAIL_SHARD_NUM; i++) {
            LcAmtLimitRecordDetailDo detailDo = new LcAmtLimitRecordDetailDo();
            detailDo.setRecordId(insertInfoDo.getRecordId());
            detailDo.setShardSeq(i);
            detailDo.setTenantId(AppParamUtil.getTenantId());
            detailDo.setCreateTime(BusinessDateUtil.getDate());
            detailDo.setUpdateTime(BusinessDateUtil.getDate());
            detailDo.setPreOccupyAmount(BigDecimal.ZERO);
            detailDo.setRealOccupyAmount(BigDecimal.ZERO);
            detailDo.setTotalAmount(BigDecimalUtil.splitRoundUp(insertInfoDo.getTotalAmount(),
                LimitBatchConstants.AMT_LIMIT_DETAIL_SHARD_NUM, i));
            detailDoList.add(detailDo);
        }
        return detailDoList;
    }

    /**
     * 构建记录
     *
     * @param limitRuleDo
     * @param planDo
     * @return
     */
    private LcAmtLimitRecordInfoDo buildRecord(LcAmtLimitRuleDo limitRuleDo, LcAmtRuleAdjustPlanDo planDo) {
        Date now = BusinessDateUtil.getDate();
        LcAmtLimitRecordInfoDo insertInfoDo = new LcAmtLimitRecordInfoDo();
        insertInfoDo.setCollectFlag(EnumBool.NO.getCode());
        insertInfoDo.setCreateTime(now);
        insertInfoDo.setEffectiveEndTime(planDo.getPlanEffectiveEndTime());
        insertInfoDo.setEffectiveStartTime(now);
        insertInfoDo.setPreOccupyAmount(BigDecimal.ZERO);
        insertInfoDo.setRealOccupyAmount(BigDecimal.ZERO);
        insertInfoDo.setRecordStatus(EnumAmtLimitRecordStatus.EFFECTIVE.getCode());
        insertInfoDo.setRuleId(limitRuleDo.getRuleId());
        insertInfoDo.setTotalAmount(limitRuleDo.getTotalAmount());
        insertInfoDo.setUpdateTime(now);
        insertInfoDo.setRecordId(BusinessSequenceUtil.getTypeSerialNo(EnumSerialModalName.LC_AMT_LIMIT_RECORD_INFO));
        insertInfoDo.setTenantId(AppParamUtil.getTenantId());
        return insertInfoDo;
    }
}
