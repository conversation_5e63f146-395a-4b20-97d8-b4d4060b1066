/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.sharding.biz;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.hsjry.base.common.fs.bean.SimpleFile;
import com.hsjry.base.common.fs.service.FileProcessService;
import com.hsjry.base.common.fs.service.FileProcessServiceFactory;
import com.hsjry.base.common.job.biz.BaseShardingPrepareBiz;
import com.hsjry.base.common.utils.GzipUtil;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.enums.EnumLimitBatchErrorCode;
import com.hsjry.core.limit.batch.common.utils.ExceptionUtil;
import com.hsjry.core.limit.batch.core.slice.SliceBatchSerialCore;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/11/7 10:53
 */
@Slf4j
public abstract class AbstractFileBaseShardingPrepareBizImpl<T> implements BaseShardingPrepareBiz, JobCoreBusiness<T> {
    @Autowired
    protected FileProcessServiceFactory serviceFactory;
    @Autowired
    protected SliceBatchSerialCore sliceBatchSerialCore;

    /**
     * 下载
     *
     * @param remoteFilePath
     * @param remoteFileName
     * @param localFile
     */
    protected void download(String remoteFilePath, String remoteFileName, File localFile, boolean unzipFlag) {
        //获取远程服务器配置
        FileProcessService processService = serviceFactory.getInstance();
        List<SimpleFile> simpleFileList = processService.listFile(remoteFilePath);
        SimpleFile file = null;
        if (CollectionUtil.isNotEmpty(processService.listFile(remoteFilePath))) {
            file = simpleFileList.stream()
                .filter(simpleFile -> simpleFile.getName()
                    .equals(remoteFileName))
                .findFirst()
                .orElse(null);
        }
        if (!Objects.equals(null, file)) {
            try {
                log.info("{}:文件[{}]下载开始", getJobTrade().getDescription(), remoteFileName);
                byte[] fileData = processService.downLoadFileBytes(remoteFilePath + remoteFileName);
                if (!localFile.getParentFile()
                    .exists()) {
                    localFile.getParentFile()
                        .mkdirs();
                }
                if (unzipFlag) {
                    //解压
                    fileData = GzipUtil.uncompress(fileData);
                }
                //写到本地
                FileUtils.writeByteArrayToFile(localFile, fileData);
                log.info("{}:文件[{}]下载完成", getJobTrade().getDescription(), localFile.getName());

            } catch (IOException e) {
                log.error("文件下载异常:", e);
                throw new HsjryBizException(EnumLimitBatchErrorCode.FILE_DOWNLOAD_ERROR.getCode(),
                    EnumLimitBatchErrorCode.FILE_DOWNLOAD_ERROR.getDescription());
            }
        } else {
            throw new HsjryBizException(EnumLimitBatchErrorCode.FILE_DATA_ERROR.getCode(),
                EnumLimitBatchErrorCode.FILE_DATA_ERROR.getDescription());
        }
    }

    /**
     * 通用分页流水更新
     *
     * @param sharedPassCount
     * @param sliceBatchSerialDo
     */
    protected void normalUpdateSliceSerial(Integer sharedPassCount, LcSliceBatchSerialDo sliceBatchSerialDo) {
        try {
            sliceBatchSerialDo.setSharedPassCount(sharedPassCount);
            //更新成功分片信息
            sliceBatchSerialCore.updateSuccessSliceBatchSerial(sliceBatchSerialDo);
        } catch (Exception e) {
            log.error("{}[失败]", getJobTrade().getDescription());
            throw ExceptionUtil.getBizException(EnumBatchJobError.SHARDING_INFO_ERROR,
                getJobTrade().getDescription() + "【异常】", this.getClass(), e);
        }
    }
}
