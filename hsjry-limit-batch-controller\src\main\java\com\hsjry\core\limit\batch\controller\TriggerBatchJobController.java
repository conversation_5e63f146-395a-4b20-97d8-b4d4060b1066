package com.hsjry.core.limit.batch.controller;

import org.springframework.beans.factory.annotation.Autowired;

import com.hsjry.core.limit.batch.biz.trigger.TriggerJobBiz;
import com.hsjry.core.limit.batch.facade.dto.trigger.TriggerJobDto;
import com.hsjry.core.limit.batch.facade.intf.trigger.ITriggerBatchJob;
import com.hsjry.lang.rpc.annotation.RpcService;

/**
 * 触发批量任务实现
 *
 * <AUTHOR>
 * @version V3.0
 * @since 3.0.1 2021/3/1 15:30
 */
@RpcService
public class TriggerBatchJobController implements ITriggerBatchJob {
    @Autowired
    private TriggerJobBiz triggerJobBiz;

    @Override
    public void executeTriggerJob(TriggerJobDto jobDto) {
        triggerJobBiz.executeTriggerJob(jobDto);
    }
}
