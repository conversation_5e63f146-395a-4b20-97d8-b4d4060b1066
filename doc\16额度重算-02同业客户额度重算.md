# 02同业客户额度重算

## 额度重算中同业客户中法人综合授信额度

### 创建[额度中心-中间表-额度重算中同业客户中法人综合授信额度]

```sql
CREATE TABLE LB_T_RECLC_IBNK_LGLPSN_LVL
(
    CUST_NO VARCHAR2 (60) NOT NULL,
    CUST_LIMIT_ID VARCHAR2 (32) NOT NULL,
    TEMPLATE_NODE_ID VARCHAR2 (32),
    LIMIT_STATUS VARCHAR2 (3),
    TOTAL_AMOUNT NUMBER (24, 6),
    PRE_OCCUPY_AMOUNT NUMBER (24, 6),
    REAL_OCCUPY_AMOUNT NUMBER (24, 6),
    LOW_RISK_AMOUNT NUMBER (24, 6),
    PRE_OCCUPY_LOW_RISK_AMT NUMBER (24, 6),
    REAL_OCCUPY_LOW_RISK_AMT NUMBER (24, 6),
    BLNG_LGLPSN_CORE_INS_NO VARCHAR2 (32),
    CONSTRAINT PK_LTRILL_CN_CLI
        PRIMARY KEY (CUST_NO, CUST_LIMIT_ID)
);
COMMENT ON TABLE LB_T_RECLC_IBNK_LGLPSN_LVL IS '额度中心-中间表-额度重算中同业客户中法人综合授信额度';
COMMENT ON COLUMN LB_T_RECLC_IBNK_LGLPSN_LVL.CUST_NO IS '客户编号';
COMMENT ON COLUMN LB_T_RECLC_IBNK_LGLPSN_LVL.CUST_LIMIT_ID IS '额度编号';
COMMENT ON COLUMN LB_T_RECLC_IBNK_LGLPSN_LVL.TEMPLATE_NODE_ID IS '模板节点编号';
COMMENT ON COLUMN LB_T_RECLC_IBNK_LGLPSN_LVL.LIMIT_STATUS IS '额度状态';
COMMENT ON COLUMN LB_T_RECLC_IBNK_LGLPSN_LVL.TOTAL_AMOUNT IS '总额度';
COMMENT ON COLUMN LB_T_RECLC_IBNK_LGLPSN_LVL.PRE_OCCUPY_AMOUNT IS '预占额度';
COMMENT ON COLUMN LB_T_RECLC_IBNK_LGLPSN_LVL.REAL_OCCUPY_AMOUNT IS '实占额度';
COMMENT ON COLUMN LB_T_RECLC_IBNK_LGLPSN_LVL.LOW_RISK_AMOUNT IS '总低风险额度';
COMMENT ON COLUMN LB_T_RECLC_IBNK_LGLPSN_LVL.PRE_OCCUPY_LOW_RISK_AMT IS '预占低风险';
COMMENT ON COLUMN LB_T_RECLC_IBNK_LGLPSN_LVL.REAL_OCCUPY_LOW_RISK_AMT IS '实占低风险';
comment on column LB_T_RECLC_IBNK_LGLPSN_LVL.BLNG_LGLPSN_CORE_INS_NO is '所属法人的核心机构号';
```

### 清空[额度中心-中间表-额度重算中同业客户中法人综合授信额度]

```sql
TRUNCATE TABLE LB_T_RECLC_IBNK_LGLPSN_LVL;
```

### 插入[额度中心-中间表-额度重算中同业客户中法人综合授信额度]中客户编号和额度编号

```sql
INSERT INTO LB_T_RECLC_IBNK_LGLPSN_LVL(CUST_NO, CUST_LIMIT_ID, TEMPLATE_NODE_ID, LIMIT_STATUS, BLNG_LGLPSN_CORE_INS_NO)
SELECT DISTINCT LCLI.LIMIT_OBJECT_ID,
                LCLI.CUST_LIMIT_ID,
                lcli.TEMPLATE_NODE_ID,
                LCLI.LIMIT_STATUS,
                LCLI.BLNG_LGLPSN_CORE_INS_NO
FROM LC_CUST_LIMIT_INFO LCLI
WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSTYKHEDTX')
  AND LCLI.TEMPLATE_NODE_ID IN ('TYFRZHSXED')
  AND LCLI.LIMIT_STATUS IN ('020');
```

### 更新[额度中心-中间表-额度重算中同业客户中法人综合授信额度]中额度金额信息

```sql
MERGE INTO LB_T_RECLC_IBNK_LGLPSN_LVL TGT
USING (SELECT LCLI.LIMIT_OBJECT_ID,
              SUM(LCLAI.TOTAL_AMOUNT)             AS TOTAL_AMOUNT,
              SUM(LCLAI.PRE_OCCUPY_AMOUNT)        AS PRE_OCCUPY_AMOUNT,
              SUM(LCLAI.REAL_OCCUPY_AMOUNT)       AS REAL_OCCUPY_AMOUNT,
              SUM(LCLAI.LOW_RISK_AMOUNT)          AS LOW_RISK_AMOUNT,
              SUM(LCLAI.PRE_OCCUPY_LOW_RISK_AMT)  AS PRE_OCCUPY_LOW_RISK_AMT,
              SUM(LCLAI.REAL_OCCUPY_LOW_RISK_AMT) AS REAL_OCCUPY_LOW_RISK_AMT
       FROM LC_CUST_LIMIT_INFO LCLI
                INNER JOIN LC_CUST_LIMIT_AMT_INFO LCLAI
                           ON LCLI.CUST_LIMIT_ID = LCLAI.CUST_LIMIT_ID AND
                              LCLI.LIMIT_OBJECT_ID = LCLAI.CUST_NO
       WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSTYKHEDTX')
         AND LCLI.TEMPLATE_NODE_ID IN ('TYTRZYWED', 'TYPJYWED', 'TYGJYWED')
         AND LCLI.LIMIT_STATUS IN ('020')
         AND LCLI.CUST_LIMIT_ID IN (SELECT CURRENT_NODE_LIMIT_ID
                                    FROM LC_CUST_LIMIT_RELATION LCLR
                                    WHERE LCLR.PARENT_NODE_LIMIT_ID IN (SELECT DISTINCT CUST_LIMIT_ID
                                                                        FROM LB_T_RECLC_IBNK_LGLPSN_LVL))
         AND LCLI.BLNG_LGLPSN_CORE_INS_NO IN (SELECT DISTINCT BLNG_LGLPSN_CORE_INS_NO FROM LB_T_RECLC_IBNK_LGLPSN_LVL)
       GROUP BY LCLI.LIMIT_OBJECT_ID) SRC
ON (TGT.CUST_NO = SRC.LIMIT_OBJECT_ID)
WHEN MATCHED THEN
UPDATE
    SET TGT.TOTAL_AMOUNT = SRC.TOTAL_AMOUNT,
    TGT.PRE_OCCUPY_AMOUNT = SRC.PRE_OCCUPY_AMOUNT,
    TGT.REAL_OCCUPY_AMOUNT = SRC.REAL_OCCUPY_AMOUNT,
    TGT.LOW_RISK_AMOUNT = SRC.LOW_RISK_AMOUNT,
    TGT.PRE_OCCUPY_LOW_RISK_AMT = SRC.PRE_OCCUPY_LOW_RISK_AMT,
    TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT;
```

### 更新[额度实例金额信息]中[法人综合授信额度]中额度金额信息

```sql
MERGE INTO LC_CUST_LIMIT_AMT_INFO TGT
USING (SELECT CUST_NO,
              CUST_LIMIT_ID,
              LIMIT_STATUS,
              TOTAL_AMOUNT,
              PRE_OCCUPY_AMOUNT,
              REAL_OCCUPY_AMOUNT,
              LOW_RISK_AMOUNT,
              PRE_OCCUPY_LOW_RISK_AMT,
              REAL_OCCUPY_LOW_RISK_AMT
       FROM LB_T_RECLC_IBNK_LGLPSN_LVL) SRC
ON (TGT.CUST_NO = SRC.CUST_NO
    AND TGT.CUST_LIMIT_ID = SRC.CUST_LIMIT_ID)
WHEN MATCHED THEN
UPDATE
    SET TGT.TOTAL_AMOUNT = SRC.TOTAL_AMOUNT,
    TGT.PRE_OCCUPY_AMOUNT = SRC.PRE_OCCUPY_AMOUNT,
    TGT.REAL_OCCUPY_AMOUNT = SRC.REAL_OCCUPY_AMOUNT,
    TGT.LOW_RISK_AMOUNT = SRC.LOW_RISK_AMOUNT,
    TGT.PRE_OCCUPY_LOW_RISK_AMT = SRC.PRE_OCCUPY_LOW_RISK_AMT,
    TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT,
    TGT.UPDATE_TIME = sysdate;
```

## 额度重算中同业客户中总额度

### 创建[额度中心-中间表-额度重算中同业客户中总额度]

```sql
CREATE TABLE LB_T_RECLC_IBNK_TOTL
(
    CUST_NO                  VARCHAR2(60) NOT NULL,
    CUST_LIMIT_ID            VARCHAR2(32) NOT NULL,
    TEMPLATE_NODE_ID         VARCHAR2(32),
    LIMIT_STATUS             VARCHAR2(3),
    TOTAL_AMOUNT             NUMBER(24, 6),
    PRE_OCCUPY_AMOUNT        NUMBER(24, 6),
    REAL_OCCUPY_AMOUNT       NUMBER(24, 6),
    LOW_RISK_AMOUNT          NUMBER(24, 6),
    PRE_OCCUPY_LOW_RISK_AMT  NUMBER(24, 6),
    REAL_OCCUPY_LOW_RISK_AMT NUMBER (24, 6),
    BLNG_LGLPSN_CORE_INS_NO VARCHAR2 (32),
    CONSTRAINT PK_LTRIT_CN_CLI
        PRIMARY KEY (CUST_NO, CUST_LIMIT_ID)
);
COMMENT ON TABLE LB_T_RECLC_IBNK_TOTL IS '额度中心-中间表-额度重算中同业客户中总额度';
COMMENT ON COLUMN LB_T_RECLC_IBNK_TOTL.CUST_NO IS '客户编号';
COMMENT ON COLUMN LB_T_RECLC_IBNK_TOTL.CUST_LIMIT_ID IS '额度编号';
COMMENT ON COLUMN LB_T_RECLC_IBNK_TOTL.TEMPLATE_NODE_ID IS '模板节点编号';
COMMENT ON COLUMN LB_T_RECLC_IBNK_TOTL.LIMIT_STATUS IS '额度状态';
COMMENT ON COLUMN LB_T_RECLC_IBNK_TOTL.TOTAL_AMOUNT IS '总额度';
COMMENT ON COLUMN LB_T_RECLC_IBNK_TOTL.PRE_OCCUPY_AMOUNT IS '预占额度';
COMMENT ON COLUMN LB_T_RECLC_IBNK_TOTL.REAL_OCCUPY_AMOUNT IS '实占额度';
COMMENT ON COLUMN LB_T_RECLC_IBNK_TOTL.LOW_RISK_AMOUNT IS '总低风险额度';
COMMENT ON COLUMN LB_T_RECLC_IBNK_TOTL.PRE_OCCUPY_LOW_RISK_AMT IS '预占低风险';
COMMENT ON COLUMN LB_T_RECLC_IBNK_TOTL.REAL_OCCUPY_LOW_RISK_AMT IS '实占低风险';
COMMENT ON COLUMN LB_T_RECLC_IBNK_TOTL.BLNG_LGLPSN_CORE_INS_NO IS '所属法人的核心机构号';
```

### 清空[额度中心-中间表-额度重算中同业客户中总额度]

```sql
TRUNCATE TABLE LB_T_RECLC_IBNK_TOTL;
```

### 插入[额度中心-中间表-额度重算中同业客户中总额度]中客户编号和额度编号

```sql
INSERT INTO LB_T_RECLC_IBNK_TOTL(CUST_NO, CUST_LIMIT_ID, TEMPLATE_NODE_ID, LIMIT_STATUS, BLNG_LGLPSN_CORE_INS_NO)
SELECT DISTINCT LCLI.LIMIT_OBJECT_ID,
                LCLI.CUST_LIMIT_ID,
                lcli.TEMPLATE_NODE_ID,
                LCLI.LIMIT_STATUS,
                BLNG_LGLPSN_CORE_INS_NO
FROM LC_CUST_LIMIT_INFO LCLI
WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSTYKHEDTX')
  AND LCLI.TEMPLATE_NODE_ID IN ('TYZED')
  AND LCLI.LIMIT_STATUS IN ('020');
```

### 更新[额度中心-中间表-额度重算中同业客户中总额度]中额度金额信息

```sql
MERGE INTO LB_T_RECLC_IBNK_TOTL TGT
USING (SELECT LCLI.LIMIT_OBJECT_ID,
              SUM(LCLAI.TOTAL_AMOUNT)             AS TOTAL_AMOUNT,
              SUM(LCLAI.PRE_OCCUPY_AMOUNT)        AS PRE_OCCUPY_AMOUNT,
              SUM(LCLAI.REAL_OCCUPY_AMOUNT)       AS REAL_OCCUPY_AMOUNT,
              SUM(LCLAI.LOW_RISK_AMOUNT)          AS LOW_RISK_AMOUNT,
              SUM(LCLAI.PRE_OCCUPY_LOW_RISK_AMT)  AS PRE_OCCUPY_LOW_RISK_AMT,
              SUM(LCLAI.REAL_OCCUPY_LOW_RISK_AMT) AS REAL_OCCUPY_LOW_RISK_AMT
       FROM LC_CUST_LIMIT_INFO LCLI
                INNER JOIN LC_CUST_LIMIT_AMT_INFO LCLAI
                           ON LCLI.CUST_LIMIT_ID = LCLAI.CUST_LIMIT_ID AND
                              LCLI.LIMIT_OBJECT_ID = LCLAI.CUST_NO
       WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSTYKHEDTX')
         AND LCLI.TEMPLATE_NODE_ID IN ('TYFRZHSXED')
         AND LCLI.LIMIT_STATUS IN ('020')
         AND LCLI.CUST_LIMIT_ID IN (SELECT CURRENT_NODE_LIMIT_ID
                                    FROM LC_CUST_LIMIT_RELATION LCLR
                                    WHERE LCLR.PARENT_NODE_LIMIT_ID IN (SELECT DISTINCT CUST_LIMIT_ID
                                                                        FROM LB_T_RECLC_IBNK_TOTL))
       GROUP BY LCLI.LIMIT_OBJECT_ID) SRC
ON (TGT.CUST_NO = SRC.LIMIT_OBJECT_ID)
WHEN MATCHED THEN
    UPDATE
    SET TGT.TOTAL_AMOUNT             = SRC.TOTAL_AMOUNT,
        TGT.PRE_OCCUPY_AMOUNT        = SRC.PRE_OCCUPY_AMOUNT,
        TGT.REAL_OCCUPY_AMOUNT       = SRC.REAL_OCCUPY_AMOUNT,
        TGT.LOW_RISK_AMOUNT          = SRC.LOW_RISK_AMOUNT,
        TGT.PRE_OCCUPY_LOW_RISK_AMT  = SRC.PRE_OCCUPY_LOW_RISK_AMT,
        TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT;
```



### 更新[额度实例金额信息]中[客户总额度]中额度金额信息

```sql
MERGE INTO LC_CUST_LIMIT_AMT_INFO TGT
USING (SELECT CUST_NO,
              CUST_LIMIT_ID,
              LIMIT_STATUS,
              TOTAL_AMOUNT,
              PRE_OCCUPY_AMOUNT,
              REAL_OCCUPY_AMOUNT,
              LOW_RISK_AMOUNT,
              PRE_OCCUPY_LOW_RISK_AMT,
              REAL_OCCUPY_LOW_RISK_AMT
       FROM LB_T_RECLC_IBNK_TOTL) SRC
ON (TGT.CUST_NO = SRC.CUST_NO
    AND TGT.CUST_LIMIT_ID = SRC.CUST_LIMIT_ID)
WHEN MATCHED THEN
    UPDATE
    SET TGT.TOTAL_AMOUNT             = SRC.TOTAL_AMOUNT,
        TGT.PRE_OCCUPY_AMOUNT        = SRC.PRE_OCCUPY_AMOUNT,
        TGT.REAL_OCCUPY_AMOUNT       = SRC.REAL_OCCUPY_AMOUNT,
        TGT.LOW_RISK_AMOUNT          = SRC.LOW_RISK_AMOUNT,
        TGT.PRE_OCCUPY_LOW_RISK_AMT  = SRC.PRE_OCCUPY_LOW_RISK_AMT,
        TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT,
        TGT.UPDATE_TIME              = sysdate;
```



