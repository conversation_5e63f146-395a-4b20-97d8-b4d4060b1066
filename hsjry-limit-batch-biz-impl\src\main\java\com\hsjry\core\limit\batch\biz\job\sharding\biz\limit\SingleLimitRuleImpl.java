// package com.hsjry.core.limit.batch.biz.job.sharding.biz.limit;
//
// import java.util.ArrayList;
// import java.util.List;
// import java.util.Map;
// import java.util.stream.Collectors;
//
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.beans.factory.annotation.Qualifier;
// import org.springframework.stereotype.Service;
// import org.springframework.transaction.support.TransactionTemplate;
// import org.springframework.util.CollectionUtils;
//
// import com.hsjry.base.common.job.dto.IEnumTrade;
// import com.hsjry.base.common.job.dto.JobInitDto;
// import com.hsjry.base.common.job.dto.JobShared;
// import com.hsjry.base.common.model.enums.limit.EnumSingleLimitRuleStatus;
// import com.hsjry.base.common.model.enums.limit.EnumSingleLimitUseStage;
// import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
// import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
// import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
// import com.hsjry.core.limit.batch.dal.dao.intf.LcSingleLimitRuleBatchDao;
// import com.hsjry.core.limit.batch.dal.dao.model.LcSliceBatchSerialDo;
// import com.hsjry.core.limit.batch.dal.dao.query.LcSingleLimitRuleQuery;
// import com.hsjry.core.limit.center.core.ISingleLimitRuleCore;
// import com.hsjry.core.limit.center.dal.dao.model.LcSingleLimitRuleDo;
// import com.hsjry.lang.common.utils.DateUtil;
// import com.hsjry.lang.common.utils.GsonUtil;
// import com.hsjry.lang.common.utils.StringUtil;
//
// import lombok.extern.slf4j.Slf4j;
//
// /**
//  * <AUTHOR>
//  * @version $$Id: SingleLimitRuleImpl.java, v 0.1 2023/6/27 2:47 下午 xuxd14949 Exp $$
//  */
// @Service
// @Slf4j
// public class SingleLimitRuleImpl extends AbstractShardingPrepareBiz<LcSingleLimitRuleQuery>
//     implements JobCoreBusiness<LcSingleLimitRuleDo> {
//
//     @Autowired
//     private LcSingleLimitRuleBatchDao lcSingleLimitRuleBatchDao;
//     @Autowired
//     @Qualifier("limitTransactionNewTemplate")
//     private TransactionTemplate transactionTemplate;
//
//     @Autowired
//     private ISingleLimitRuleCore singleLimitRuleCore;
//
//     @Override
//     public Integer selectCountByCurrentGroupFromDb(LcSingleLimitRuleQuery query) {
//         return lcSingleLimitRuleBatchDao.selectCountByCurrentGroup(query);
//     }
//
//     @Override
//     public IEnumTrade getJobTrade() {
//         return EnumJobTrade.DISABLE_SINGLE_LIMIT;
//     }
//
//     @Override
//     public ShardingResult<LcSingleLimitRuleDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
//         JobInitDto jobInitDto, JobShared jobShared) {
//         ShardingResult<LcSingleLimitRuleDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
//         if (StringUtil.isBlank(jobShared.getExtParam())) {
//             return shardingResult;
//         }
//         //原始查询条件
//         LcSingleLimitRuleQuery lcSingleLimitRuleQuery = GsonUtil.json2Obj(jobShared.getExtParam(),
//             LcSingleLimitRuleQuery.class);
//         LcSingleLimitRuleQuery query = LcSingleLimitRuleQuery.builder()
//             .offset(jobShared.getOffset())
//             .limit(jobShared.getLimit())
//             .effectiveEndTimeInt(lcSingleLimitRuleQuery.getEffectiveEndTimeInt())
//             .limitStatus(lcSingleLimitRuleQuery.getLimitStatus())
//             .ruleId(lcSingleLimitRuleQuery.getRuleId())
//             .build();
//         log.info("=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");
//         List<LcSingleLimitRuleDo> list = lcSingleLimitRuleBatchDao.selectShardList(query);
//         shardingResult.setShardingResultList(list);
//         return shardingResult;
//     }
//
//     @Override
//     public void execJobCoreBusiness(ShardingResult<LcSingleLimitRuleDo> shardingResult) {
//
//         List<LcSingleLimitRuleDo> lcSingleLimitRuleDoList = shardingResult.getShardingResultList();
//         if (CollectionUtils.isEmpty(lcSingleLimitRuleDoList)) {
//             log.info("=========分片执行结束:" + shardingResult.getJobShared()
//                 .getBatchNum() + "数量为空=========");
//             return;
//         }
//
//         transactionTemplate.execute(transactionStatus -> {
//             lcSingleLimitRuleDoList.forEach(lcSingleLimitRuleDo -> lcSingleLimitRuleDo.setLimitStatus(
//                 EnumSingleLimitRuleStatus.DEACTIVATE.getCode()));
//             lcSingleLimitRuleBatchDao.updateByPrimaryKeyList(lcSingleLimitRuleDoList);
//             return true;
//         });
//         //清除缓存
//         Map<String, List<LcSingleLimitRuleDo>> result = lcSingleLimitRuleDoList.stream()
//             .collect(Collectors.groupingBy(lcSingleLimitRuleDo -> lcSingleLimitRuleDo.getLimitObjectId() + "_"
//                 + lcSingleLimitRuleDo.getUseStage()));
//         result.keySet()
//             .forEach(key -> {
//                 String[] keyArr = key.split("_");
//                 String limitId = keyArr[0];
//                 String useStage = keyArr[1];
//                 singleLimitRuleCore.flushRule(limitId, EnumSingleLimitUseStage.find(useStage));
//             });
//     }
//
//     @Override
//     public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
//         log.info("====================== 接入业务{}分片逻辑 start ================================================",
//             getJobTrade().getDescription());
//         List<JobShared> jobSharedList = new ArrayList<>();
//         //sql 批处理数量 暂定为分片数量，不放大
//         Integer batchFixNum = jobInitDto.getFixNum();
//         //当前分组的最大值，为下次 批处理的最小值
//         LcSingleLimitRuleDo maxLimitInfoDo = new LcSingleLimitRuleDo();
//         //构造查询条件 查询当前 分批处理的 排序 最大 对象
//         LcSingleLimitRuleQuery query = LcSingleLimitRuleQuery.builder()
//             .limitStatus(EnumSingleLimitRuleStatus.ENABLED.getCode())
//             .effectiveEndTimeInt(jobInitDto.getBusinessDate())
//             .offset(batchFixNum - 1)
//             .limit(1)
//             .build();
//         //分片流水
//         int batchNum = 0;
//         while (maxLimitInfoDo != null) {
//             query.setRuleId(maxLimitInfoDo.getRuleId());
//             maxLimitInfoDo = lcSingleLimitRuleBatchDao.selectFirstOne(query);
//             //统计分片 数量
//             batchNum = countBatchNum(batchFixNum, query, maxLimitInfoDo, batchNum, jobInitDto, jobSharedList,
//                 query.getRuleId(), false);
//         }
//         log.info("====================== 接入业务{}分片逻辑 end ================================================",
//             getJobTrade().getDescription());
//         return jobSharedList;
//     }
// }
