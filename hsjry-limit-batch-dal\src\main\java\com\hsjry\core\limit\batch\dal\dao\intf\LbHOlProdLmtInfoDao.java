package com.hsjry.core.limit.batch.dal.dao.intf;

import com.hsjry.core.limit.batch.dal.dao.model.LbHOlProdLmtInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbHOlProdLmtInfoQuery;

import java.util.List;

import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 网贷系统-历史表-产品额度信息（记录客户产品额度信息）数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbHOlProdLmtInfoDao extends IBaseDao<LbHOlProdLmtInfoDo> {
    /**
     * 分页查询网贷系统-历史表-产品额度信息（记录客户产品额度信息）
     *
     * @param lbHOlProdLmtInfoQuery 条件
     * @return PageInfo<LbHOlProdLmtInfoDo>
     */
    PageInfo<LbHOlProdLmtInfoDo> selectPage(LbHOlProdLmtInfoQuery lbHOlProdLmtInfoQuery, PageParam pageParam);

    /**
     * 根据key查询网贷系统-历史表-产品额度信息（记录客户产品额度信息）
     *
     * @param creditLimitId
     * @param dataDate
     * @return
     */
    LbHOlProdLmtInfoDo selectByKey(String creditLimitId, String dataDate);

    /**
     * 根据key删除网贷系统-历史表-产品额度信息（记录客户产品额度信息）
     *
     * @param creditLimitId
     * @param dataDate
     * @return
     */
    int deleteByKey(String creditLimitId, String dataDate);

    /**
     * 查询网贷系统-历史表-产品额度信息（记录客户产品额度信息）信息
     *
     * @param lbHOlProdLmtInfoQuery 条件
     * @return List<LbHOlProdLmtInfoDo>
     */
    List<LbHOlProdLmtInfoDo> selectByExample(LbHOlProdLmtInfoQuery lbHOlProdLmtInfoQuery);

    /**
     * 新增网贷系统-历史表-产品额度信息（记录客户产品额度信息）信息
     *
     * @param lbHOlProdLmtInfo 条件
     * @return int>
     */
    int insertBySelective(LbHOlProdLmtInfoDo lbHOlProdLmtInfo);

    /**
     * 修改网贷系统-历史表-产品额度信息（记录客户产品额度信息）信息
     *
     * @param lbHOlProdLmtInfo
     * @return
     */
    int updateBySelective(LbHOlProdLmtInfoDo lbHOlProdLmtInfo);

    /**
     * 修改网贷系统-历史表-产品额度信息（记录客户产品额度信息）信息
     *
     * @param lbHOlProdLmtInfo
     * @param lbHOlProdLmtInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbHOlProdLmtInfoDo lbHOlProdLmtInfo, LbHOlProdLmtInfoQuery lbHOlProdLmtInfoQuery);

    /**
     * 删除所有数据
     * 用于文件同步作业的全量数据刷新
     *
     * @return 删除的记录数
     */
    int deleteAll();

    /**
     * 批量插入数据
     * 用于文件同步作业的大批量数据插入
     *
     * @param lbHOlProdLmtInfoList 待插入的数据列表
     * @return 插入的记录数
     */
    @Override
    int insertList(List<LbHOlProdLmtInfoDo> lbHOlProdLmtInfoList);

    /**
     * 删除历史表-产品额度信息当前业务日期的数据
     * @param dataDate
     * @return
     */
    int deleteByDataDate(String dataDate);
}
