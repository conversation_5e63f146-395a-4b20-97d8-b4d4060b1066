package com.hsjry.core.limit.batch.biz.convert.copy;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.hsjry.core.limit.batch.common.dto.file.LbCLimitOperateSerialDto;
import com.hsjry.core.limit.batch.dal.dao.model.LbCLimitOperateSerialDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitOperateSerialDo;
/**
 * 额度实例流水信息转换器
 *
 * <AUTHOR>
 * @date 2025/7/10
 */
@Mapper(componentModel = "spring")
public interface LbCLimitOperateSerialCnvs {

    LbCLimitOperateSerialCnvs INSTANCE = Mappers.getMapper(LbCLimitOperateSerialCnvs.class);

    LbCLimitOperateSerialDo do2Copy(LcCustLimitOperateSerialDo model);

    LbCLimitOperateSerialDo dtoToDo(LbCLimitOperateSerialDto dto);

    LbCLimitOperateSerialDto do2Dto(LbCLimitOperateSerialDo model);
}
