// package com.hsjry.core.limit.batch.biz.job.job;
//
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.beans.factory.annotation.Qualifier;
// import org.springframework.stereotype.Service;
//
// import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
// import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;
//
// import lombok.extern.slf4j.Slf4j;
//
// /**
//  * 客户额度信息文件推送任务
//  *
//  * <AUTHOR>
//  * @version V4.0
//  * @since 4.0 2024/2/6 14:02
//  */
// @Service
// @Slf4j
// public class PushCustomerLimitJob extends AbstractBaseBatchJob {
//     @Autowired
//     @Qualifier("pushCustomerLimitBizImpl")
//     private BaseOrdinaryBiz baseOrdinaryBiz;
//
//     @Override
//     public BaseOrdinaryBiz getBaseOrdinaryBiz() {
//         return baseOrdinaryBiz;
//     }
// }
