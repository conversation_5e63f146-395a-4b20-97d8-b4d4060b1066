package com.hsjry.core.limit.batch.biz.job.job.copy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * 备份表-额度实例信息处理任务
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/11 11:41
 */
@Slf4j
@Service("lbCLimitInfoBakSyncJob")
public class LbCLimitInfoBakSyncJob extends AbstractBaseBatchJob {
    public LbCLimitInfoBakSyncJob() {
        log.info("LbCLimitInfoBakSyncJob Bean初始化完成");
    }

    @Autowired
    @Qualifier("lbCLimitInfoBakSyncBizImpl")
    private BaseOrdinaryBiz baseOrdinaryBiz;

    @Override
    public BaseOrdinaryBiz getBaseOrdinaryBiz() {
        return baseOrdinaryBiz;
    }
}
