package com.hsjry.core.limit.batch.dal.dao.intf;

import com.hsjry.core.limit.batch.dal.dao.model.LbSCorePprodDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbSCorePprodDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSCorePprodQuery;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.query.LbSCorePprodQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 核心系统-落地表-产品定义数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbSCorePprodDao extends IBaseDao<LbSCorePprodDo> {
    /**
     * 分页查询核心系统-落地表-产品定义
     *
     * @param lbSCorePprodQuery 条件
     * @return PageInfo<LbSCorePprodDo>
     */
    PageInfo<LbSCorePprodDo> selectPage(LbSCorePprodQuery lbSCorePprodQuery, PageParam pageParam);

    /**
     * 根据key查询核心系统-落地表-产品定义
     *
     * @param chapbh
     * @return
     */
    LbSCorePprodDo selectByKey(String chapbh);

    /**
     * 根据key删除核心系统-落地表-产品定义
     *
     * @param chapbh
     * @return
     */
    int deleteByKey(String chapbh);

    /**
     * 查询核心系统-落地表-产品定义信息
     *
     * @param lbSCorePprodQuery 条件
     * @return List<LbSCorePprodDo>
     */
    List<LbSCorePprodDo> selectByExample(LbSCorePprodQuery lbSCorePprodQuery);

    /**
     * 新增核心系统-落地表-产品定义信息
     *
     * @param lbSCorePprod 条件
     * @return int>
     */
    int insertBySelective(LbSCorePprodDo lbSCorePprod);

    /**
     * 修改核心系统-落地表-产品定义信息
     *
     * @param lbSCorePprod
     * @return
     */
    int updateBySelective(LbSCorePprodDo lbSCorePprod);

    /**
     * 修改核心系统-落地表-产品定义信息
     *
     * @param lbSCorePprod
     * @param lbSCorePprodQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbSCorePprodDo lbSCorePprod, LbSCorePprodQuery lbSCorePprodQuery);


    /**
     * 批量插入核心系统产品定义表-落地信息
     *
     * @param lbSCorePprodList 批量数据
     * @return int
     */
    @Override
    int insertList(List<LbSCorePprodDo> lbSCorePprodList);

    /**
     * 清空核心系统产品定义表-落地所有数据
     *
     * @return int
     */
    int deleteAll();

    /**
     * 查询分片数据
     *
     * @param query 查询条件
     * @return List<LbSCorePprodDo>
     */
    List<LbSCorePprodDo> selectShardList(LbSCorePprodQuery query);

    /**
     * 获取第一个对象，limit m，1
     *
     * @param query 查询条件
     * @return LbSCorePprodDo
     */
    LbSCorePprodDo selectFirstOne(LbSCorePprodQuery query);

    /**
     * 获取当前组的数据量
     *
     * @param query 查询条件
     * @return Integer
     */
    Integer selectCountByCurrentGroup(LbSCorePprodQuery query);

}
