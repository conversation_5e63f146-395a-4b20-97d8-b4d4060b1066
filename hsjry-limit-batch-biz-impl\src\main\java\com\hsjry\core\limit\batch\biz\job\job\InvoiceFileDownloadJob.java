// package com.hsjry.core.limit.batch.biz.job.job;
//
// import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
// import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;
// import lombok.extern.slf4j.Slf4j;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.beans.factory.annotation.Qualifier;
// import org.springframework.stereotype.Service;
//
// /**
//  * 外围借据文件下载
//  * <AUTHOR>
//  * @version V4.0
//  * @since 4.0.1 2024/5/21 10:25
//  */
// @Service
// @Slf4j
// public class InvoiceFileDownloadJob extends AbstractBaseBatchJob {
//     @Autowired
//     @Qualifier("invoiceFileDownBizImpl")
//     private BaseOrdinaryBiz baseOrdinaryBiz;
//     @Override
//     public BaseOrdinaryBiz getBaseOrdinaryBiz() {
//         return baseOrdinaryBiz;
//     }
// }
