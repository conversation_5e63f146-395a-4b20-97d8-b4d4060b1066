/*
 * yunrong.cn Inc. Copyright (c) 2014-2022 All Rights Reserved
 */

package com.hsjry.core.limit.batch.common.enums;

import com.hsjry.lang.common.stereotype.enums.IEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 各业务名称及关联主键url
 *
 * <AUTHOR>
 * @version $Id: EnumSerialModalName.java, v 1.0 2017年3月9日 下午2:47:29 wangpp15032 Exp $
 */
@Getter
@AllArgsConstructor
public enum EnumSerialModalName implements IEnum {
    /** 借据流水 */
    RZYH_INVOICE_SERIAL("RIS","RZYH_INVOICE_SERIAL","借据流水"),
    ;
    /**
     * 状态码
     **/
    private String code;
    /**
     * 类型
     **/
    private String type;
    /**
     * 状态描述
     **/
    private String description;

    /**
     * 根据编码查找枚举
     *
     * @param code 编码
     * @return {@link EnumSerialModalName} 实例
     **/
    public static EnumSerialModalName find(String code) {
        for (EnumSerialModalName frs : EnumSerialModalName.values()) {
            if (frs.getCode()
                .equals(code)) {
                return frs;
            }
        }
        return null;
    }
}
