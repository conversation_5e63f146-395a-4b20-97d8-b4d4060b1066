# 03个人客户额度重算

## 额度重算中个人客户中经营贷额度

### 创建[额度中心-中间表-额度重算中个人客户中经营贷额度]

```sql
CREATE TABLE LB_T_RECLC_INDV_OP_LOAN
(
    CUST_NO                  VARCHAR2(60) NOT NULL,
    CUST_LIMIT_ID            VARCHAR2(32) NOT NULL,
    TEMPLATE_NODE_ID         VARCHAR2(32),
    LIMIT_STATUS             VARCHAR2(3),
    TOTAL_AMOUNT             NUMBER(24, 6),
    PRE_OCCUPY_AMOUNT        NUMBER(24, 6),
    REAL_OCCUPY_AMOUNT       NUMBER(24, 6),
    LOW_RISK_AMOUNT          NUMBER(24, 6),
    PRE_OCCUPY_LOW_RISK_AMT  NUMBER(24, 6),
    REAL_OCCUPY_LOW_RISK_AMT NUMBER(24, 6),
    CONSTRAINT PK_LTRIOL_CN_CLI
        PRIMARY KEY (CUST_NO, CUST_LIMIT_ID)
);
COMMENT ON TABLE LB_T_RECLC_INDV_OP_LOAN IS '额度中心-中间表-额度重算中个人额度中经营贷额度';
COMMENT ON COLUMN LB_T_RECLC_INDV_OP_LOAN.CUST_NO IS '客户编号';
COMMENT ON COLUMN LB_T_RECLC_INDV_OP_LOAN.CUST_LIMIT_ID IS '额度编号';
COMMENT ON COLUMN LB_T_RECLC_INDV_OP_LOAN.TEMPLATE_NODE_ID IS '模板节点编号';
COMMENT ON COLUMN LB_T_RECLC_INDV_OP_LOAN.LIMIT_STATUS IS '额度状态';
COMMENT ON COLUMN LB_T_RECLC_INDV_OP_LOAN.TOTAL_AMOUNT IS '总额度';
COMMENT ON COLUMN LB_T_RECLC_INDV_OP_LOAN.PRE_OCCUPY_AMOUNT IS '预占额度';
COMMENT ON COLUMN LB_T_RECLC_INDV_OP_LOAN.REAL_OCCUPY_AMOUNT IS '实占额度';
COMMENT ON COLUMN LB_T_RECLC_INDV_OP_LOAN.LOW_RISK_AMOUNT IS '总低风险额度';
COMMENT ON COLUMN LB_T_RECLC_INDV_OP_LOAN.PRE_OCCUPY_LOW_RISK_AMT IS '预占低风险';
COMMENT ON COLUMN LB_T_RECLC_INDV_OP_LOAN.REAL_OCCUPY_LOW_RISK_AMT IS '实占低风险';
```

### 清空[额度中心-中间表-额度重算中个人客户中经营贷额度]

```sql
TRUNCATE TABLE LB_T_RECLC_INDV_OP_LOAN;
```

### 插入[额度中心-中间表-额度重算中个人客户中经营贷额度]中客户编号和额度编号

```sql
INSERT INTO LB_T_RECLC_INDV_OP_LOAN(CUST_NO, CUST_LIMIT_ID, TEMPLATE_NODE_ID, LIMIT_STATUS)
SELECT DISTINCT LCLI.LIMIT_OBJECT_ID,
                LCLI.CUST_LIMIT_ID,
                lcli.TEMPLATE_NODE_ID,
                LCLI.LIMIT_STATUS
FROM LC_CUST_LIMIT_INFO LCLI
WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSGRKHEDTX')
  AND LCLI.TEMPLATE_NODE_ID IN ('GRJYDED')
  AND LCLI.LIMIT_STATUS IN ('020');
```

### 更新[额度中心-中间表-额度重算中个人客户中经营贷额度]中额度金额信息

```sql
MERGE INTO LB_T_RECLC_INDV_OP_LOAN TGT
USING (SELECT LCLI.LIMIT_OBJECT_ID,
              SUM(LCLAI.TOTAL_AMOUNT)             AS TOTAL_AMOUNT,
              SUM(LCLAI.PRE_OCCUPY_AMOUNT)        AS PRE_OCCUPY_AMOUNT,
              SUM(LCLAI.REAL_OCCUPY_AMOUNT)       AS REAL_OCCUPY_AMOUNT,
              SUM(LCLAI.LOW_RISK_AMOUNT)          AS LOW_RISK_AMOUNT,
              SUM(LCLAI.PRE_OCCUPY_LOW_RISK_AMT)  AS PRE_OCCUPY_LOW_RISK_AMT,
              SUM(LCLAI.REAL_OCCUPY_LOW_RISK_AMT) AS REAL_OCCUPY_LOW_RISK_AMT
       FROM LC_CUST_LIMIT_INFO LCLI
                INNER JOIN LC_CUST_LIMIT_AMT_INFO LCLAI
                           ON LCLI.CUST_LIMIT_ID = LCLAI.CUST_LIMIT_ID AND
                              LCLI.LIMIT_OBJECT_ID = LCLAI.CUST_NO
       WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSGRKHEDTX')
         AND LCLI.TEMPLATE_NODE_ID IN ('GRJYDCPED', 'GRJYAJCPED')
         AND LCLI.LIMIT_STATUS IN ('020')
         AND LCLI.CUST_LIMIT_ID IN (SELECT CURRENT_NODE_LIMIT_ID
                                    FROM LC_CUST_LIMIT_RELATION LCLR
                                    WHERE LCLR.PARENT_NODE_LIMIT_ID IN (SELECT DISTINCT CUST_LIMIT_ID
                                                                        FROM LB_T_RECLC_INDV_OP_LOAN))
       GROUP BY LCLI.LIMIT_OBJECT_ID) SRC
ON (TGT.CUST_NO = SRC.LIMIT_OBJECT_ID)
WHEN MATCHED THEN
    UPDATE
    SET TGT.TOTAL_AMOUNT             = SRC.TOTAL_AMOUNT,
        TGT.PRE_OCCUPY_AMOUNT        = SRC.PRE_OCCUPY_AMOUNT,
        TGT.REAL_OCCUPY_AMOUNT       = SRC.REAL_OCCUPY_AMOUNT,
        TGT.LOW_RISK_AMOUNT          = SRC.LOW_RISK_AMOUNT,
        TGT.PRE_OCCUPY_LOW_RISK_AMT  = SRC.PRE_OCCUPY_LOW_RISK_AMT,
        TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT;
```

### 更新[额度实例金额信息]中[经营贷额度]中额度金额信息

```sql
MERGE INTO LC_CUST_LIMIT_AMT_INFO TGT
USING (SELECT CUST_NO,
              CUST_LIMIT_ID,
              LIMIT_STATUS,
              TOTAL_AMOUNT,
              PRE_OCCUPY_AMOUNT,
              REAL_OCCUPY_AMOUNT,
              LOW_RISK_AMOUNT,
              PRE_OCCUPY_LOW_RISK_AMT,
              REAL_OCCUPY_LOW_RISK_AMT
       FROM LB_T_RECLC_INDV_OP_LOAN) SRC
ON (TGT.CUST_NO = SRC.CUST_NO
    AND TGT.CUST_LIMIT_ID = SRC.CUST_LIMIT_ID)
WHEN MATCHED THEN
    UPDATE
    SET TGT.TOTAL_AMOUNT             = SRC.TOTAL_AMOUNT,
        TGT.PRE_OCCUPY_AMOUNT        = SRC.PRE_OCCUPY_AMOUNT,
        TGT.REAL_OCCUPY_AMOUNT       = SRC.REAL_OCCUPY_AMOUNT,
        TGT.LOW_RISK_AMOUNT          = SRC.LOW_RISK_AMOUNT,
        TGT.PRE_OCCUPY_LOW_RISK_AMT  = SRC.PRE_OCCUPY_LOW_RISK_AMT,
        TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT,
        TGT.UPDATE_TIME              = sysdate;
```

## 额度重算中个人客户中消费贷额度

### 创建[额度中心-中间表-额度重算中个人客户中消费贷额度]

```sql
CREATE TABLE LB_T_RECLC_INDV_CONSM_LOAN
(
    CUST_NO                  VARCHAR2(60) NOT NULL,
    CUST_LIMIT_ID            VARCHAR2(32) NOT NULL,
    TEMPLATE_NODE_ID         VARCHAR2(32),
    LIMIT_STATUS             VARCHAR2(3),
    TOTAL_AMOUNT             NUMBER(24, 6),
    PRE_OCCUPY_AMOUNT        NUMBER(24, 6),
    REAL_OCCUPY_AMOUNT       NUMBER(24, 6),
    LOW_RISK_AMOUNT          NUMBER(24, 6),
    PRE_OCCUPY_LOW_RISK_AMT  NUMBER(24, 6),
    REAL_OCCUPY_LOW_RISK_AMT NUMBER(24, 6),
    CONSTRAINT PK_LTRICL_CN_CLI
        PRIMARY KEY (CUST_NO, CUST_LIMIT_ID)
);
COMMENT ON TABLE LB_T_RECLC_INDV_CONSM_LOAN IS '额度中心-中间表-额度重算中个人额度中消费贷额度';
COMMENT ON COLUMN LB_T_RECLC_INDV_CONSM_LOAN.CUST_NO IS '客户编号';
COMMENT ON COLUMN LB_T_RECLC_INDV_CONSM_LOAN.CUST_LIMIT_ID IS '额度编号';
COMMENT ON COLUMN LB_T_RECLC_INDV_CONSM_LOAN.TEMPLATE_NODE_ID IS '模板节点编号';
COMMENT ON COLUMN LB_T_RECLC_INDV_CONSM_LOAN.LIMIT_STATUS IS '额度状态';
COMMENT ON COLUMN LB_T_RECLC_INDV_CONSM_LOAN.TOTAL_AMOUNT IS '总额度';
COMMENT ON COLUMN LB_T_RECLC_INDV_CONSM_LOAN.PRE_OCCUPY_AMOUNT IS '预占额度';
COMMENT ON COLUMN LB_T_RECLC_INDV_CONSM_LOAN.REAL_OCCUPY_AMOUNT IS '实占额度';
COMMENT ON COLUMN LB_T_RECLC_INDV_CONSM_LOAN.LOW_RISK_AMOUNT IS '总低风险额度';
COMMENT ON COLUMN LB_T_RECLC_INDV_CONSM_LOAN.PRE_OCCUPY_LOW_RISK_AMT IS '预占低风险';
COMMENT ON COLUMN LB_T_RECLC_INDV_CONSM_LOAN.REAL_OCCUPY_LOW_RISK_AMT IS '实占低风险';
```

### 清空[额度中心-中间表-额度重算中个人客户中消费贷额度]

```sql
TRUNCATE TABLE LB_T_RECLC_INDV_CONSM_LOAN;
```

### 插入[额度中心-中间表-额度重算中个人客户中消费贷额度]中客户编号和额度编号

```sql
INSERT INTO LB_T_RECLC_INDV_CONSM_LOAN(CUST_NO, CUST_LIMIT_ID, TEMPLATE_NODE_ID, LIMIT_STATUS)
SELECT DISTINCT LCLI.LIMIT_OBJECT_ID,
                LCLI.CUST_LIMIT_ID,
                lcli.TEMPLATE_NODE_ID,
                LCLI.LIMIT_STATUS
FROM LC_CUST_LIMIT_INFO LCLI
WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSGRKHEDTX')
  AND LCLI.TEMPLATE_NODE_ID IN ('GRXFDED')
  AND LCLI.LIMIT_STATUS IN ('020');
```

### 更新[额度中心-中间表-额度重算中个人客户中消费贷额度]中额度金额信息

```sql
MERGE INTO LB_T_RECLC_INDV_CONSM_LOAN TGT
USING (SELECT LCLI.LIMIT_OBJECT_ID,
              SUM(LCLAI.TOTAL_AMOUNT)             AS TOTAL_AMOUNT,
              SUM(LCLAI.PRE_OCCUPY_AMOUNT)        AS PRE_OCCUPY_AMOUNT,
              SUM(LCLAI.REAL_OCCUPY_AMOUNT)       AS REAL_OCCUPY_AMOUNT,
              SUM(LCLAI.LOW_RISK_AMOUNT)          AS LOW_RISK_AMOUNT,
              SUM(LCLAI.PRE_OCCUPY_LOW_RISK_AMT)  AS PRE_OCCUPY_LOW_RISK_AMT,
              SUM(LCLAI.REAL_OCCUPY_LOW_RISK_AMT) AS REAL_OCCUPY_LOW_RISK_AMT
       FROM LC_CUST_LIMIT_INFO LCLI
                INNER JOIN LC_CUST_LIMIT_AMT_INFO LCLAI
                           ON LCLI.CUST_LIMIT_ID = LCLAI.CUST_LIMIT_ID AND
                              LCLI.LIMIT_OBJECT_ID = LCLAI.CUST_NO
       WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSGRKHEDTX')
         AND LCLI.TEMPLATE_NODE_ID IN ('GRXFDCPED', 'GRXFAJCPED', 'GRXYKED')
         AND LCLI.LIMIT_STATUS IN ('020')
         AND LCLI.CUST_LIMIT_ID IN (SELECT CURRENT_NODE_LIMIT_ID
                                    FROM LC_CUST_LIMIT_RELATION LCLR
                                    WHERE LCLR.PARENT_NODE_LIMIT_ID IN (SELECT DISTINCT CUST_LIMIT_ID
                                                                        FROM LB_T_RECLC_INDV_CONSM_LOAN))
       GROUP BY LCLI.LIMIT_OBJECT_ID) SRC
ON (TGT.CUST_NO = SRC.LIMIT_OBJECT_ID)
WHEN MATCHED THEN
    UPDATE
    SET TGT.TOTAL_AMOUNT             = SRC.TOTAL_AMOUNT,
        TGT.PRE_OCCUPY_AMOUNT        = SRC.PRE_OCCUPY_AMOUNT,
        TGT.REAL_OCCUPY_AMOUNT       = SRC.REAL_OCCUPY_AMOUNT,
        TGT.LOW_RISK_AMOUNT          = SRC.LOW_RISK_AMOUNT,
        TGT.PRE_OCCUPY_LOW_RISK_AMT  = SRC.PRE_OCCUPY_LOW_RISK_AMT,
        TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT;
```

### 更新[额度实例金额信息]中[消费贷额度]中额度金额信息

```sql
MERGE INTO LC_CUST_LIMIT_AMT_INFO TGT
USING (SELECT CUST_NO,
              CUST_LIMIT_ID,
              LIMIT_STATUS,
              TOTAL_AMOUNT,
              PRE_OCCUPY_AMOUNT,
              REAL_OCCUPY_AMOUNT,
              LOW_RISK_AMOUNT,
              PRE_OCCUPY_LOW_RISK_AMT,
              REAL_OCCUPY_LOW_RISK_AMT
       FROM LB_T_RECLC_INDV_CONSM_LOAN) SRC
ON (TGT.CUST_NO = SRC.CUST_NO
    AND TGT.CUST_LIMIT_ID = SRC.CUST_LIMIT_ID)
WHEN MATCHED THEN
    UPDATE
    SET TGT.TOTAL_AMOUNT             = SRC.TOTAL_AMOUNT,
        TGT.PRE_OCCUPY_AMOUNT        = SRC.PRE_OCCUPY_AMOUNT,
        TGT.REAL_OCCUPY_AMOUNT       = SRC.REAL_OCCUPY_AMOUNT,
        TGT.LOW_RISK_AMOUNT          = SRC.LOW_RISK_AMOUNT,
        TGT.PRE_OCCUPY_LOW_RISK_AMT  = SRC.PRE_OCCUPY_LOW_RISK_AMT,
        TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT,
        TGT.UPDATE_TIME              = sysdate;
```

## 额度重算中个人客户中其他额度

### 创建[额度中心-中间表-额度重算中个人客户中其他额度]

```sql
CREATE TABLE LB_T_RECLC_INDV_OTHR
(
    CUST_NO                  VARCHAR2(60) NOT NULL,
    CUST_LIMIT_ID            VARCHAR2(32) NOT NULL,
    TEMPLATE_NODE_ID         VARCHAR2(32),
    LIMIT_STATUS             VARCHAR2(3),
    TOTAL_AMOUNT             NUMBER(24, 6),
    PRE_OCCUPY_AMOUNT        NUMBER(24, 6),
    REAL_OCCUPY_AMOUNT       NUMBER(24, 6),
    LOW_RISK_AMOUNT          NUMBER(24, 6),
    PRE_OCCUPY_LOW_RISK_AMT  NUMBER(24, 6),
    REAL_OCCUPY_LOW_RISK_AMT NUMBER(24, 6),
    CONSTRAINT PK_LTRIO_CN_CLI
        PRIMARY KEY (CUST_NO, CUST_LIMIT_ID)
);
COMMENT ON TABLE LB_T_RECLC_INDV_OTHR IS '额度中心-中间表-额度中心-中间表-额度重算中个人额度中其他额度';
COMMENT ON COLUMN LB_T_RECLC_INDV_OTHR.CUST_NO IS '客户编号';
COMMENT ON COLUMN LB_T_RECLC_INDV_OTHR.CUST_LIMIT_ID IS '额度编号';
COMMENT ON COLUMN LB_T_RECLC_INDV_OTHR.TEMPLATE_NODE_ID IS '模板节点编号';
COMMENT ON COLUMN LB_T_RECLC_INDV_OTHR.LIMIT_STATUS IS '额度状态';
COMMENT ON COLUMN LB_T_RECLC_INDV_OTHR.TOTAL_AMOUNT IS '总额度';
COMMENT ON COLUMN LB_T_RECLC_INDV_OTHR.PRE_OCCUPY_AMOUNT IS '预占额度';
COMMENT ON COLUMN LB_T_RECLC_INDV_OTHR.REAL_OCCUPY_AMOUNT IS '实占额度';
COMMENT ON COLUMN LB_T_RECLC_INDV_OTHR.LOW_RISK_AMOUNT IS '总低风险额度';
COMMENT ON COLUMN LB_T_RECLC_INDV_OTHR.PRE_OCCUPY_LOW_RISK_AMT IS '预占低风险';
COMMENT ON COLUMN LB_T_RECLC_INDV_OTHR.REAL_OCCUPY_LOW_RISK_AMT IS '实占低风险';
```

### 清空[额度中心-中间表-额度重算中个人客户中其他额度]

```sql
TRUNCATE TABLE LB_T_RECLC_INDV_OTHR;
```

### 插入[额度中心-中间表-额度重算中个人客户中其他额度]中客户编号和额度编号

```sql
INSERT INTO LB_T_RECLC_INDV_OTHR(CUST_NO, CUST_LIMIT_ID, TEMPLATE_NODE_ID, LIMIT_STATUS)
SELECT DISTINCT LCLI.LIMIT_OBJECT_ID,
                LCLI.CUST_LIMIT_ID,
                lcli.TEMPLATE_NODE_ID,
                LCLI.LIMIT_STATUS
FROM LC_CUST_LIMIT_INFO LCLI
WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSGRKHEDTX')
  AND LCLI.TEMPLATE_NODE_ID IN ('GRQTED')
  AND LCLI.LIMIT_STATUS IN ('020');
```

### 更新[额度中心-中间表-额度重算中个人客户中其他额度]中额度金额信息

```sql
MERGE INTO LB_T_RECLC_INDV_OTHR TGT
USING (SELECT LCLI.LIMIT_OBJECT_ID,
              SUM(LCLAI.TOTAL_AMOUNT)             AS TOTAL_AMOUNT,
              SUM(LCLAI.PRE_OCCUPY_AMOUNT)        AS PRE_OCCUPY_AMOUNT,
              SUM(LCLAI.REAL_OCCUPY_AMOUNT)       AS REAL_OCCUPY_AMOUNT,
              SUM(LCLAI.LOW_RISK_AMOUNT)          AS LOW_RISK_AMOUNT,
              SUM(LCLAI.PRE_OCCUPY_LOW_RISK_AMT)  AS PRE_OCCUPY_LOW_RISK_AMT,
              SUM(LCLAI.REAL_OCCUPY_LOW_RISK_AMT) AS REAL_OCCUPY_LOW_RISK_AMT
       FROM LC_CUST_LIMIT_INFO LCLI
                INNER JOIN LC_CUST_LIMIT_AMT_INFO LCLAI
                           ON LCLI.CUST_LIMIT_ID = LCLAI.CUST_LIMIT_ID AND
                              LCLI.LIMIT_OBJECT_ID = LCLAI.CUST_NO
       WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSGRKHEDTX')
         AND LCLI.TEMPLATE_NODE_ID IN ('GRQTCPED')
         AND LCLI.LIMIT_STATUS IN ('020')
         AND LCLI.CUST_LIMIT_ID IN (SELECT CURRENT_NODE_LIMIT_ID
                                    FROM LC_CUST_LIMIT_RELATION LCLR
                                    WHERE LCLR.PARENT_NODE_LIMIT_ID IN (SELECT DISTINCT CUST_LIMIT_ID
                                                                        FROM LB_T_RECLC_INDV_OTHR))
       GROUP BY LCLI.LIMIT_OBJECT_ID) SRC
ON (TGT.CUST_NO = SRC.LIMIT_OBJECT_ID)
WHEN MATCHED THEN
    UPDATE
    SET TGT.TOTAL_AMOUNT             = SRC.TOTAL_AMOUNT,
        TGT.PRE_OCCUPY_AMOUNT        = SRC.PRE_OCCUPY_AMOUNT,
        TGT.REAL_OCCUPY_AMOUNT       = SRC.REAL_OCCUPY_AMOUNT,
        TGT.LOW_RISK_AMOUNT          = SRC.LOW_RISK_AMOUNT,
        TGT.PRE_OCCUPY_LOW_RISK_AMT  = SRC.PRE_OCCUPY_LOW_RISK_AMT,
        TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT;
```

### 更新[额度实例金额信息]中[其他额度]中额度金额信息

```sql
MERGE INTO LC_CUST_LIMIT_AMT_INFO TGT
USING (SELECT CUST_NO,
              CUST_LIMIT_ID,
              LIMIT_STATUS,
              TOTAL_AMOUNT,
              PRE_OCCUPY_AMOUNT,
              REAL_OCCUPY_AMOUNT,
              LOW_RISK_AMOUNT,
              PRE_OCCUPY_LOW_RISK_AMT,
              REAL_OCCUPY_LOW_RISK_AMT
       FROM LB_T_RECLC_INDV_OTHR) SRC
ON (TGT.CUST_NO = SRC.CUST_NO
    AND TGT.CUST_LIMIT_ID = SRC.CUST_LIMIT_ID)
WHEN MATCHED THEN
    UPDATE
    SET TGT.TOTAL_AMOUNT             = SRC.TOTAL_AMOUNT,
        TGT.PRE_OCCUPY_AMOUNT        = SRC.PRE_OCCUPY_AMOUNT,
        TGT.REAL_OCCUPY_AMOUNT       = SRC.REAL_OCCUPY_AMOUNT,
        TGT.LOW_RISK_AMOUNT          = SRC.LOW_RISK_AMOUNT,
        TGT.PRE_OCCUPY_LOW_RISK_AMT  = SRC.PRE_OCCUPY_LOW_RISK_AMT,
        TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT,
        TGT.UPDATE_TIME              = sysdate;
```

## 额度重算中个人客户中总额度

### 创建[额度中心-中间表-额度重算中个人客户中总额度]

```sql
CREATE TABLE LB_T_RECLC_INDV_TOTL
(
    CUST_NO                  VARCHAR2(60) NOT NULL,
    CUST_LIMIT_ID            VARCHAR2(32) NOT NULL,
    TEMPLATE_NODE_ID         VARCHAR2(32),
    LIMIT_STATUS             VARCHAR2(3),
    TOTAL_AMOUNT             NUMBER(24, 6),
    PRE_OCCUPY_AMOUNT        NUMBER(24, 6),
    REAL_OCCUPY_AMOUNT       NUMBER(24, 6),
    LOW_RISK_AMOUNT          NUMBER(24, 6),
    PRE_OCCUPY_LOW_RISK_AMT  NUMBER(24, 6),
    REAL_OCCUPY_LOW_RISK_AMT NUMBER(24, 6),
    CONSTRAINT PK_LTRITL_CN_CLI
        PRIMARY KEY (CUST_NO, CUST_LIMIT_ID)
);
COMMENT ON TABLE LB_T_RECLC_INDV_TOTL IS '额度中心-中间表-额度中心-中间表-额度重算中个人额度中客户总额度';
COMMENT ON COLUMN LB_T_RECLC_INDV_TOTL.CUST_NO IS '客户编号';
COMMENT ON COLUMN LB_T_RECLC_INDV_TOTL.CUST_LIMIT_ID IS '额度编号';
COMMENT ON COLUMN LB_T_RECLC_INDV_TOTL.TEMPLATE_NODE_ID IS '模板节点编号';
COMMENT ON COLUMN LB_T_RECLC_INDV_TOTL.LIMIT_STATUS IS '额度状态';
COMMENT ON COLUMN LB_T_RECLC_INDV_TOTL.TOTAL_AMOUNT IS '总额度';
COMMENT ON COLUMN LB_T_RECLC_INDV_TOTL.PRE_OCCUPY_AMOUNT IS '预占额度';
COMMENT ON COLUMN LB_T_RECLC_INDV_TOTL.REAL_OCCUPY_AMOUNT IS '实占额度';
COMMENT ON COLUMN LB_T_RECLC_INDV_TOTL.LOW_RISK_AMOUNT IS '总低风险额度';
COMMENT ON COLUMN LB_T_RECLC_INDV_TOTL.PRE_OCCUPY_LOW_RISK_AMT IS '预占低风险';
COMMENT ON COLUMN LB_T_RECLC_INDV_TOTL.REAL_OCCUPY_LOW_RISK_AMT IS '实占低风险';
```

### 清空[额度中心-中间表-额度重算中个人客户中总额度]

```sql
TRUNCATE TABLE LB_T_RECLC_INDV_TOTL;
```

### 插入[额度中心-中间表-额度重算中个人客户中总额度]中客户编号和额度编号

```sql
INSERT INTO LB_T_RECLC_INDV_TOTL(CUST_NO, CUST_LIMIT_ID, TEMPLATE_NODE_ID, LIMIT_STATUS)
SELECT DISTINCT LCLI.LIMIT_OBJECT_ID,
                LCLI.CUST_LIMIT_ID,
                lcli.TEMPLATE_NODE_ID,
                LCLI.LIMIT_STATUS
FROM LC_CUST_LIMIT_INFO LCLI
WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSGRKHEDTX')
  AND LCLI.TEMPLATE_NODE_ID IN ('GRZED')
  AND LCLI.LIMIT_STATUS IN ('020');
```

### 更新[额度中心-中间表-额度重算中个人客户中总额度]中额度金额信息

```sql
MERGE INTO LB_T_RECLC_INDV_TOTL TGT
USING (SELECT LCLI.LIMIT_OBJECT_ID,
              SUM(LCLAI.TOTAL_AMOUNT)             AS TOTAL_AMOUNT,
              SUM(LCLAI.PRE_OCCUPY_AMOUNT)        AS PRE_OCCUPY_AMOUNT,
              SUM(LCLAI.REAL_OCCUPY_AMOUNT)       AS REAL_OCCUPY_AMOUNT,
              SUM(LCLAI.LOW_RISK_AMOUNT)          AS LOW_RISK_AMOUNT,
              SUM(LCLAI.PRE_OCCUPY_LOW_RISK_AMT)  AS PRE_OCCUPY_LOW_RISK_AMT,
              SUM(LCLAI.REAL_OCCUPY_LOW_RISK_AMT) AS REAL_OCCUPY_LOW_RISK_AMT
       FROM LC_CUST_LIMIT_INFO LCLI
                INNER JOIN LC_CUST_LIMIT_AMT_INFO LCLAI
                           ON LCLI.CUST_LIMIT_ID = LCLAI.CUST_LIMIT_ID AND
                              LCLI.LIMIT_OBJECT_ID = LCLAI.CUST_NO
       WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSGRKHEDTX')
         AND LCLI.TEMPLATE_NODE_ID IN ('GRJYDED','GRXFDED','GRQTED')
         AND LCLI.LIMIT_STATUS IN ('020')
         AND LCLI.CUST_LIMIT_ID IN (SELECT CURRENT_NODE_LIMIT_ID
                                    FROM LC_CUST_LIMIT_RELATION LCLR
                                    WHERE LCLR.PARENT_NODE_LIMIT_ID IN (SELECT DISTINCT CUST_LIMIT_ID
                                                                        FROM LB_T_RECLC_INDV_TOTL))
       GROUP BY LCLI.LIMIT_OBJECT_ID) SRC
ON (TGT.CUST_NO = SRC.LIMIT_OBJECT_ID)
WHEN MATCHED THEN
    UPDATE
    SET TGT.TOTAL_AMOUNT             = SRC.TOTAL_AMOUNT,
        TGT.PRE_OCCUPY_AMOUNT        = SRC.PRE_OCCUPY_AMOUNT,
        TGT.REAL_OCCUPY_AMOUNT       = SRC.REAL_OCCUPY_AMOUNT,
        TGT.LOW_RISK_AMOUNT          = SRC.LOW_RISK_AMOUNT,
        TGT.PRE_OCCUPY_LOW_RISK_AMT  = SRC.PRE_OCCUPY_LOW_RISK_AMT,
        TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT;
```

### 更新[额度实例金额信息]中[客户总额度]中额度金额信息

```sql
MERGE INTO LC_CUST_LIMIT_AMT_INFO TGT
USING (SELECT CUST_NO,
              CUST_LIMIT_ID,
              LIMIT_STATUS,
              TOTAL_AMOUNT,
              PRE_OCCUPY_AMOUNT,
              REAL_OCCUPY_AMOUNT,
              LOW_RISK_AMOUNT,
              PRE_OCCUPY_LOW_RISK_AMT,
              REAL_OCCUPY_LOW_RISK_AMT
       FROM LB_T_RECLC_INDV_TOTL) SRC
ON (TGT.CUST_NO = SRC.CUST_NO
    AND TGT.CUST_LIMIT_ID = SRC.CUST_LIMIT_ID)
WHEN MATCHED THEN
    UPDATE
    SET TGT.TOTAL_AMOUNT             = SRC.TOTAL_AMOUNT,
        TGT.PRE_OCCUPY_AMOUNT        = SRC.PRE_OCCUPY_AMOUNT,
        TGT.REAL_OCCUPY_AMOUNT       = SRC.REAL_OCCUPY_AMOUNT,
        TGT.LOW_RISK_AMOUNT          = SRC.LOW_RISK_AMOUNT,
        TGT.PRE_OCCUPY_LOW_RISK_AMT  = SRC.PRE_OCCUPY_LOW_RISK_AMT,
        TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT,
        TGT.UPDATE_TIME              = sysdate;
```



