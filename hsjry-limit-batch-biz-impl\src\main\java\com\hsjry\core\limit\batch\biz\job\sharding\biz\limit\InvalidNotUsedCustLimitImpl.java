// package com.hsjry.core.limit.batch.biz.job.sharding.biz.limit;
//
// import java.math.BigDecimal;
// import java.text.ParseException;
// import java.text.SimpleDateFormat;
// import java.util.ArrayList;
// import java.util.Arrays;
// import java.util.Comparator;
// import java.util.Date;
// import java.util.List;
// import java.util.Map;
// import java.util.stream.Collectors;
//
// import com.alibaba.fastjson.JSON;
// import com.alibaba.fastjson.JSONArray;
// import com.alibaba.fastjson.JSONObject;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.beans.factory.annotation.Qualifier;
// import org.springframework.beans.factory.annotation.Value;
// import org.springframework.stereotype.Service;
// import org.springframework.transaction.support.TransactionTemplate;
// import org.springframework.util.CollectionUtils;
//
// import com.google.common.collect.Lists;
// import com.google.common.collect.Maps;
// import com.hsjry.base.common.job.dto.IEnumTrade;
// import com.hsjry.base.common.job.dto.JobInitDto;
// import com.hsjry.base.common.job.dto.JobShared;
// import com.hsjry.base.common.model.enums.common.EnumSerialModalName;
// import com.hsjry.base.common.model.enums.limit.EnumCustLimitOperateStatus;
// import com.hsjry.base.common.model.enums.limit.EnumCustLimitOperateType;
// import com.hsjry.base.common.model.enums.limit.EnumCustLimitStatus;
// import com.hsjry.base.common.model.enums.limit.EnumLimitGrantType;
// import com.hsjry.base.common.utils.BusinessSequenceUtil;
// import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
// import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
// import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
// import com.hsjry.core.limit.batch.common.enums.EnumLimitBatchErrorCode;
// import com.hsjry.core.limit.center.dal.dao.intf.CustLimitInfoBatchDao;
// import com.hsjry.core.limit.batch.dal.dao.model.LcSliceBatchSerialDo;
// import com.hsjry.core.limit.center.dal.dao.query.CustLimitInfoQuery;
// import com.hsjry.core.limit.center.common.utils.BigDecimalUtil;
// import com.hsjry.core.limit.center.core.ICustLimitCalculateCore;
// import com.hsjry.core.limit.center.core.ICustLimitLockCore;
// import com.hsjry.core.limit.center.core.ICustLimitOperateCore;
// import com.hsjry.core.limit.center.core.ICustLimitTreeCore;
// import com.hsjry.core.limit.center.core.bo.CustLimitBo;
// import com.hsjry.core.limit.center.core.bo.CustLimitOperateSerialBo;
// import com.hsjry.core.limit.center.core.bo.CustLimitRelationBo;
// import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitAmtInfoDao;
// import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitOperateSerialDao;
// import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitAmtInfoDo;
// import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoDo;
// import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitOperateSerialDo;
// import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitOperateSerialQuery;
// import com.hsjry.lang.common.exception.HsjryBizException;
// import com.hsjry.lang.common.stereotype.enums.EnumBool;
// import com.hsjry.lang.common.utils.CollectionUtil;
// import com.hsjry.lang.common.utils.DateUtil;
// import com.hsjry.lang.common.utils.GsonUtil;
// import com.hsjry.lang.common.utils.StringUtil;
//
// import lombok.extern.slf4j.Slf4j;
//
// /**
//  * 额度未使用失效
//  * 默认超过6个月额度未发生预占、直接实占自动失效
//  *
//  * <AUTHOR>
//  * @version V4.0
//  * @since 4.0 2023/3/22 14:21
//  */
// @Service
// @Slf4j
// public class InvalidNotUsedCustLimitImpl extends AbstractShardingPrepareBiz<CustLimitInfoQuery>
//     implements JobCoreBusiness<LcCustLimitInfoDo> {
//     @Autowired
//     private CustLimitInfoBatchDao custLimitInfoBatchDao;
//     @Autowired
//     private ICustLimitTreeCore iCustLimitTreeCore;
//     @Autowired
//     private ICustLimitLockCore iCustLimitLockCore;
//     @Autowired
//     private ICustLimitCalculateCore iCustLimitCalculateCore;
//     @Autowired
//     private ICustLimitOperateCore iCustLimitOperateCore;
//     @Autowired
//     private LcCustLimitOperateSerialDao lcCustLimitOperateSerialDao;
//     @Autowired
//     private LcCustLimitAmtInfoDao lcCustLimitAmtInfoDao;
//     @Autowired
//     @Qualifier("limitTransactionNewTemplate")
//     private TransactionTemplate transactionTemplate;
//
//     /** 额度未使用月数 */
//     @Value("${not.used.limit.month:6}")
//     private int notUsedLimitMonth;
//
//     @Override
//     public Integer selectCountByCurrentGroupFromDb(CustLimitInfoQuery query) {
//         return custLimitInfoBatchDao.selectExpireCountByCurrentGroup(query);
//     }
//
//     @Override
//     public IEnumTrade getJobTrade() {
//         return EnumJobTrade.CUST_LIMIT_NOT_USED_INVALID;
//     }
//
//     @Override
//     public ShardingResult<LcCustLimitInfoDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
//         JobInitDto jobInitDto, JobShared jobShared) {
//         ShardingResult<LcCustLimitInfoDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
//         if (StringUtil.isBlank(jobShared.getExtParam())) {
//             return shardingResult;
//         }
//         JSONObject inParam = JSON.parseObject(jobInitDto.getInPara());
//         List<String> userIdList = JSONArray.parseArray(inParam.getString("userIdList"), String.class);
//         //原始查询条件
//         Integer batchFixNum = jobInitDto.getFixNum();
//         CustLimitInfoQuery limitInfoQuery = GsonUtil.json2Obj(jobShared.getExtParam(), CustLimitInfoQuery.class);
//         CustLimitInfoQuery query = CustLimitInfoQuery.builder()
//                 .offset(0)
//                 .limit(jobShared.getLimit())
//                 .limitObjectId(limitInfoQuery.getLimitObjectId())
//             .limitStatusList(limitInfoQuery.getLimitStatusList()).limitObjectIdList(userIdList)
//             .beforeTime(limitInfoQuery.getBeforeTime())
//             .contractLimitFlag(limitInfoQuery.getContractLimitFlag())
//             .limitGrantTypeList(Arrays.asList(EnumLimitGrantType.All_SPLIT.getCode(),
//                 EnumLimitGrantType.PART_SPLIT.getCode(),
//                 EnumLimitGrantType.SHARE.getCode()))
//             .build();
//         log.info("=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");
//         List<LcCustLimitInfoDo> list = custLimitInfoBatchDao.selectExpireShardList(query);
//         shardingResult.setShardingResultList(list);
//         return shardingResult;
//     }
//
//     @Override
//     public void execJobCoreBusiness(ShardingResult<LcCustLimitInfoDo> shardingResult) {
//         List<LcCustLimitInfoDo> custLimitInfoDoList = shardingResult.getShardingResultList();
//         if (CollectionUtils.isEmpty(custLimitInfoDoList)) {
//             log.info("=========分片执行结束:" + shardingResult.getJobShared()
//                 .getBatchNum() + "数量为空=========");
//             return;
//         }
//         log.info("=========[{}]分片执行开始:" + shardingResult.getJobShared()
//                 .getBatchNum() + "=========",getJobTrade().getDescription());
//         List<LcCustLimitInfoDo> list = new ArrayList<>();
//         // 任务时间
//         String dateStr = String.valueOf(shardingResult.getJobShared().getBusinessDate());
//         SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.DATE_FORMAT_2);
//         Date date;
//         try {
//             date = sdf.parse(dateStr);
//         } catch (ParseException e) {
//             throw new HsjryBizException(EnumLimitBatchErrorCode.BUSINESS_DATE_FORMAT_ERROR.getCode(),
//                     EnumLimitBatchErrorCode.BUSINESS_DATE_FORMAT_ERROR.getDescription());
//         }
//         //为了把同一客户数据分到一片，custLimitInfoDoList里实际只有limitObjectId,这里再去查询下每个客户的过期数据
//         for (LcCustLimitInfoDo lcCustLimitInfoDo : custLimitInfoDoList) {
//             CustLimitInfoQuery query = CustLimitInfoQuery.builder()
//                     .limitStatusList(Lists.newArrayList(EnumCustLimitStatus.VALID.getCode()))
//                     .beforeTime(DateUtil.dateReduceMonth(date, notUsedLimitMonth))
//                     .contractLimitFlag(EnumBool.NO.getCode())
//                     .limitGrantTypeList(Arrays.asList(EnumLimitGrantType.All_SPLIT.getCode(),
//                             EnumLimitGrantType.PART_SPLIT.getCode(),
//                             EnumLimitGrantType.SHARE.getCode()))
//                     .limitObjectId(lcCustLimitInfoDo.getLimitObjectId())
//                     .build();
//             //根据分片传过来的limitObjectId再去查该用户的
//             List<LcCustLimitInfoDo> dos = custLimitInfoBatchDao.selectExpireLimitInfoByObjectId(query);
//             if(CollectionUtil.isNotEmpty(dos)){
//                 list.addAll(dos);
//             }
//         }
//
//
//         // 过滤出需要失效的额度信息列表
//         List<LcCustLimitInfoDo> invalidCustLimitList = filterPendingLimitList(list);
//         // 失效额度信息
//         if (CollectionUtil.isNotEmpty(invalidCustLimitList)) {
//             log.info("待处理额度信息总条数[{}]", invalidCustLimitList.size());
//             for (LcCustLimitInfoDo lcCustLimitInfoDo : invalidCustLimitList) {
//                 handle(lcCustLimitInfoDo);
//             }
//         }
//         //更新分片流水成功
//         normalUpdateSliceSerial(custLimitInfoDoList.size(), shardingResult.getLcSliceBatchSerialDo());
//         log.info("=========[{}]分片执行结束:" + shardingResult.getJobShared()
//                 .getBatchNum() + "=========",getJobTrade().getDescription());
//     }
//
//     /**
//      * 过滤待处理的额度信息列表
//      *
//      * @param custLimitInfoDoList
//      * @return
//      */
//     private List<LcCustLimitInfoDo> filterPendingLimitList(List<LcCustLimitInfoDo> custLimitInfoDoList) {
//         List<String> custLimitIdList = custLimitInfoDoList.stream().map(LcCustLimitInfoDo::getCustLimitId).collect(
//             Collectors.toList());
//         // 查询额度用信业务流水
//         List<LcCustLimitOperateSerialDo> limitOperateSerialDoList = lcCustLimitOperateSerialDao.selectByExample(
//             LcCustLimitOperateSerialQuery.builder()
//                 .custLimitIdList(custLimitIdList)
//                 .operateTypeList(Lists.newArrayList(EnumCustLimitOperateType.PRE_OCCUPY.getCode(),
//                     EnumCustLimitOperateType.REAL_OCCUPY_NO_PRE.getCode()))
//                 .build());
//         // 用信业务流水map
//         Map<String, List<LcCustLimitOperateSerialDo>> serialDoMap = CollectionUtil.isEmpty(limitOperateSerialDoList) ?
//             Maps.newHashMap() : limitOperateSerialDoList.stream().collect(
//             Collectors.groupingBy(LcCustLimitOperateSerialDo::getCustLimitId));
//         // 查询额度续授信业务流水
//         List<LcCustLimitOperateSerialDo> limitReCreditSerialDoList = lcCustLimitOperateSerialDao.selectByExample(
//             LcCustLimitOperateSerialQuery.builder()
//                 .custLimitIdList(custLimitIdList)
//                 .status(EnumCustLimitOperateStatus.SUCCESS.getCode())
//                 .operateTypeList(Lists.newArrayList(EnumCustLimitOperateType.RE_CREDIT_UNFROZEN.getCode(),
//                     EnumCustLimitOperateType.RE_CREDIT_BREAK.getCode(),
//                     EnumCustLimitOperateType.RE_CREDIT_EXPIRE.getCode(),
//                     EnumCustLimitOperateType.RE_CREDIT_UNTIE_FORBID.getCode(),
//                     EnumCustLimitOperateType.RE_CREDIT_NORMAL.getCode()))
//                 .build());
//         // 当前片没有续授信的额度信息 返回没有做过业务的额度信息
//         if (CollectionUtil.isEmpty(limitReCreditSerialDoList)) {
//             log.info("当前片没有做过续授信的额度信息!");
//             return custLimitInfoDoList.stream()
//                 .filter(model -> !serialDoMap.containsKey(model.getCustLimitId()))
//                 .collect(Collectors.toList());
//         }
//
//         // 续授信流水map
//         Map<String, List<LcCustLimitOperateSerialDo>> reCreditSerialMap = limitReCreditSerialDoList.stream()
//             .collect(Collectors.groupingBy(LcCustLimitOperateSerialDo::getCustLimitId));
//         return custLimitInfoDoList.stream().filter(
//             model -> handleInvalidReCredit(model, serialDoMap, reCreditSerialMap)).collect(Collectors.toList());
//     }
//
//     /**
//      * 处理失效续授信逻辑
//      *
//      * @param model
//      * @param serialDoMap 用信流水map
//      * @param reCreditSerialMap 续授信流水map
//      * @return true-失效 false-不失效
//      */
//     private boolean handleInvalidReCredit(LcCustLimitInfoDo model,
//         Map<String, List<LcCustLimitOperateSerialDo>> serialDoMap,
//         Map<String, List<LcCustLimitOperateSerialDo>> reCreditSerialMap) {
//         List<LcCustLimitOperateSerialDo> serialList = serialDoMap.get(model.getCustLimitId());
//         // 1.如果没有做过用信业务 加入失效列表
//         if (CollectionUtil.isEmpty(serialList)) {
//             return true;
//         }
//         List<LcCustLimitOperateSerialDo> reCreditSerialList = reCreditSerialMap.get(
//             model.getCustLimitId());
//         // 2.做过用信业务 额度没有进行过续授信 不加入失效列表
//         if (CollectionUtil.isEmpty(reCreditSerialList)) {
//             return false;
//         }
//         // 3.做过用信业务 额度进行过续授信
//         // 获取最新的续授信流水
//         LcCustLimitOperateSerialDo newestReCredit = reCreditSerialList.stream()
//             .max(Comparator.comparing(serial -> serial.getBizDatetime() == null
//                 ? serial.getCreateTime() : serial.getBizDatetime()))
//             .orElse(null);
//         assert newestReCredit != null;
//         // 最新续授信时间
//         Date newestReCreditDate = newestReCredit.getBizDatetime() == null ? newestReCredit.getCreateTime() :
//             newestReCredit.getBizDatetime();
//         // 过滤出续授信后做的用信业务流水
//         LcCustLimitOperateSerialDo afterReCreditSerial = serialList.stream().filter(
//                 serial -> serial.getBizDatetime() == null ?
//                     DateUtil.compare(serial.getCreateTime(), newestReCreditDate) > 0 :
//                     DateUtil.compare(serial.getBizDatetime(), newestReCreditDate) > 0)
//             .findAny()
//             .orElse(null);
//         // 3.1 如果续授信后做过业务 不加入失效列表
//         if (afterReCreditSerial != null) {
//             return false;
//         }
//         // 3.2 如果续授信后没有做过业务 判断续授信前的用信业务是否结清
//         LcCustLimitAmtInfoDo custLimitAmtDo = lcCustLimitAmtInfoDao.selectByKey(model.getCustLimitId());
//         // 3.2.1 之前做过的用信业务都结清了 加入失效列表
//         if (BigDecimalUtil.isEqual(custLimitAmtDo.getPreOccupyAmount(), BigDecimal.ZERO)
//             && BigDecimalUtil.isEqual(custLimitAmtDo.getRealOccupyAmount(), BigDecimal.ZERO)
//             && BigDecimalUtil.isEqual(custLimitAmtDo.getPreOccupyLowRiskAmt(), BigDecimal.ZERO)
//             && BigDecimalUtil.isEqual(custLimitAmtDo.getRealOccupyLowRiskAmt(), BigDecimal.ZERO)) {
//             return true;
//         }
//         // 3.2.2 之前做过的用信业务没有结清 不加入失效列表
//         return false;
//     }
//
//     /**
//      * 额度处理
//      *
//      * @param lcCustLimitInfoDo
//      */
//     private void handle(LcCustLimitInfoDo lcCustLimitInfoDo) {
//         log.info("=========额度[{}]正在处理===========", lcCustLimitInfoDo.getCustLimitId());
//         CustLimitBo custLimitBo = do2CustLimitBo(lcCustLimitInfoDo);
//         List<CustLimitBo> limitBoList = Lists.newArrayList(custLimitBo);
//         List<CustLimitRelationBo> limitRelationBoList = Lists.newArrayList();
//         try {
//             transactionTemplate.execute(transactionStatus -> {
//                 //加锁
//                 iCustLimitLockCore.lock(limitBoList, null, getJobTrade().getCode());
//                 // 额度失效
//                 custLimitBo.setLimitStatus(EnumCustLimitStatus.INVALID.getCode());
//                 custLimitBo.getLimitOperateSerialBoList()
//                     .add(CustLimitOperateSerialBo.builder()
//                         .operateAmount(BigDecimal.ZERO)
//                         .operateAmountCurrency(custLimitBo.getCustLimitAmountBo()
//                             .getCurrency())
//                         .operateLowRiskAmount(BigDecimal.ZERO)
//                         .operateLowRiskCurrency(custLimitBo.getCustLimitAmountBo()
//                             .getLowRiskCurrency())
//                         .operateType(EnumCustLimitOperateType.INVALID)
//                         .relationId(custLimitBo.getRelationId())
//                         .status(EnumCustLimitOperateStatus.SUCCESS.getCode())
//                         .closSerialNo(
//                             BusinessSequenceUtil.getTypeSerialNo(EnumSerialModalName.LC_CUST_LIMIT_OPERATE_SERIAL))
//                         .build());
//
//                 limitBoList.clear();
//                 limitBoList.add(custLimitBo);
//                 // 串用退回
//                 // 汇总额度重算
//                 iCustLimitTreeCore.supplementCollectParentAndRelation(limitBoList, limitRelationBoList);
//                 iCustLimitCalculateCore.collectCalculate(limitBoList, limitRelationBoList);
//                 // 更新数据库
//                 iCustLimitOperateCore.updateDataWithoutAmt(limitBoList);
//                 return true;
//             });
//         } finally {
//             iCustLimitLockCore.unLock(limitBoList);
//         }
//         log.info("=========额度[{}]处理完成===========", lcCustLimitInfoDo.getCustLimitId());
//     }
//
//     /**
//      * do 转 CustLimitBo
//      *
//      * @param lcCustLimitInfoDo
//      * @return
//      */
//     private CustLimitBo do2CustLimitBo(LcCustLimitInfoDo lcCustLimitInfoDo) {
//         if (null == lcCustLimitInfoDo) {
//             return null;
//         }
//         CustLimitBo custLimitBo = CustLimitBo.builder()
//             .build();
//
//         custLimitBo.setLimitTemplateId(lcCustLimitInfoDo.getLimitTemplateId());
//         custLimitBo.setNodeId(lcCustLimitInfoDo.getTemplateNodeId());
//         custLimitBo.setTypeId(lcCustLimitInfoDo.getCustLimitTypeId());
//         custLimitBo.setCustLimitId(lcCustLimitInfoDo.getCustLimitId());
//         custLimitBo.setLimitTerm(lcCustLimitInfoDo.getLimitTerm());
//         custLimitBo.setLimitTermUnit(lcCustLimitInfoDo.getLimitTermUnit());
//         custLimitBo.setLimitGraceTerm(lcCustLimitInfoDo.getLimitGraceTerm());
//         custLimitBo.setLimitGraceTermUnit(lcCustLimitInfoDo.getLimitGraceTermUnit());
//         custLimitBo.setOutCustLimitId(lcCustLimitInfoDo.getOutCustLimitId());
//         custLimitBo.setProductId(lcCustLimitInfoDo.getProductId());
//         custLimitBo.setLimitUsageType(lcCustLimitInfoDo.getLimitUsageType());
//         custLimitBo.setEffectiveStartTime(lcCustLimitInfoDo.getEffectiveStartTime());
//         custLimitBo.setEffectiveEndTime(lcCustLimitInfoDo.getEffectiveEndTime());
//         custLimitBo.setLimitGrantType(lcCustLimitInfoDo.getLimitGrantType());
//         custLimitBo.setOpenPeriod(lcCustLimitInfoDo.getLimitEnableTerm());
//         custLimitBo.setLimitObjectType(lcCustLimitInfoDo.getLimitObjectType());
//         custLimitBo.setOpenPeriodUnit(lcCustLimitInfoDo.getLimitEnableTermUnit());
//         custLimitBo.setApprovalRegisterDate(lcCustLimitInfoDo.getLimitApprovalDate());
//         custLimitBo.setLimitObjectId(lcCustLimitInfoDo.getLimitObjectId());
//         custLimitBo.setLimitCoreObjectId(lcCustLimitInfoDo.getLimitCoreObjectId());
//         custLimitBo.setLimitStatus(lcCustLimitInfoDo.getLimitStatus());
//         custLimitBo.setLimitLastTime(lcCustLimitInfoDo.getLimitLastTime());
//         custLimitBo.setOperatorId(lcCustLimitInfoDo.getOperatorId());
//         custLimitBo.setOwnOrganId(lcCustLimitInfoDo.getOwnOrganId());
//         custLimitBo.setContractLimitFlag(lcCustLimitInfoDo.getContractLimitFlag());
//         custLimitBo.setDbExistFlag(StringUtil.isNotBlank(lcCustLimitInfoDo.getCustLimitId()));
//         custLimitBo.setExcessOccupationType(lcCustLimitInfoDo.getExcessOccupationType());
//         custLimitBo.setSeq(lcCustLimitInfoDo.getSeq());
//         custLimitBo.setLimitClassification(lcCustLimitInfoDo.getLimitClassification());
//         custLimitBo.setOccupyTimesLimit(lcCustLimitInfoDo.getOccupyTimesLimit());
//         custLimitBo.setUseOccupyTimes(lcCustLimitInfoDo.getUseOccupyTimes());
//         custLimitBo.setLimitApprovalDate(lcCustLimitInfoDo.getLimitApprovalDate());
//         custLimitBo.setRelationId(lcCustLimitInfoDo.getRelationId());
//         custLimitBo.setLimitLevel(lcCustLimitInfoDo.getLimitLevel());
//         custLimitBo.setLimitEnableEndTime(lcCustLimitInfoDo.getLimitEnableEndTime());
//         custLimitBo.setLimitOccupationType(lcCustLimitInfoDo.getLimitOccupationType());
//         custLimitBo.setBizLine(lcCustLimitInfoDo.getBizLine());
//         if (StringUtil.isBlank(custLimitBo.getInstId())) {
//             custLimitBo.setInstId(lcCustLimitInfoDo.getCustLimitId());
//         }
//         custLimitBo.setCreateTime(lcCustLimitInfoDo.getCreateTime());
//         return custLimitBo;
//
//     }
//
//     @Override
//     public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
//         log.info("====================== 接入业务{}分片逻辑 start ================================================",
//             getJobTrade().getDescription());
//         List<JobShared> jobSharedList = new ArrayList<>();
//         //sql 批处理数量 暂定为分片数量，不放大
//         Integer batchFixNum = jobInitDto.getFixNum();
//         //当前分组的最大值，为下次 批处理的最小值
//         LcCustLimitInfoDo maxLimitInfoDo = new LcCustLimitInfoDo();
//         // 任务时间
//         String dateStr = String.valueOf(jobInitDto.getBusinessDate());
//         SimpleDateFormat sdf = new SimpleDateFormat(DateUtil.DATE_FORMAT_2);
//         Date date;
//         try {
//             date = sdf.parse(dateStr);
//         } catch (ParseException e) {
//             throw new HsjryBizException(EnumLimitBatchErrorCode.BUSINESS_DATE_FORMAT_ERROR.getCode(),
//                 EnumLimitBatchErrorCode.BUSINESS_DATE_FORMAT_ERROR.getDescription());
//         }
//         JSONObject inParam = JSON.parseObject(jobInitDto.getInPara());
//         List<String> userIdList = JSONArray.parseArray(inParam.getString("userIdList"), String.class);
//         //构造查询条件 查询当前 分批处理的 排序 最大 对象
//         CustLimitInfoQuery query = CustLimitInfoQuery.builder()
//                 .limitObjectIdList(userIdList)
//             .limitStatusList(Lists.newArrayList(EnumCustLimitStatus.VALID.getCode()))
//             .beforeTime(DateUtil.dateReduceMonth(date, notUsedLimitMonth))
//             .contractLimitFlag(EnumBool.NO.getCode())
//             .limitGrantTypeList(Arrays.asList(EnumLimitGrantType.All_SPLIT.getCode(),
//                 EnumLimitGrantType.PART_SPLIT.getCode(),
//                 EnumLimitGrantType.SHARE.getCode()))
//             .offset(batchFixNum - 1)
//             .limit(1)
//             .build();
//         //分片流水
//         int batchNum = 0;
//         while (maxLimitInfoDo != null) {
//             query.setLimitObjectId(maxLimitInfoDo.getLimitObjectId());
// //            query.setLimitObjectId(maxLimitInfoDo.getLimitObjectId());
//             //改了sql。sql里按custLimitId排序 改分页临界点为 >maxLimitInfoDo.getCustLimitId()
//             maxLimitInfoDo = custLimitInfoBatchDao.selectExpireFirstOne(query);
//             //统计分片 数量
//             batchNum = countBatchNum(batchFixNum, query, maxLimitInfoDo, batchNum, jobInitDto, jobSharedList,
//                 query.getLimitObjectId(), false);
//         }
//         log.info("====================== 接入业务{}分片逻辑 end ================================================",
//             getJobTrade().getDescription());
//         return jobSharedList;
//     }
//
// }
