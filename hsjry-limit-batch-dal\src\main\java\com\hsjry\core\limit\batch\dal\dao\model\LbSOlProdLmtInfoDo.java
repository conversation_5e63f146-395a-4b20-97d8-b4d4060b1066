package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 网贷系统-落地表-产品额度信息（记录客户产品额度信息）Do
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
@Table(name = "lb_s_ol_prod_lmt_info")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LbSOlProdLmtInfoDo extends LbSOlProdLmtInfoKeyDo implements Serializable {
    private static final long serialVersionUID = 1942415996337979399L;
    /** 冻结额度 */
    @Column(name = "frozen_amount")
    private java.math.BigDecimal frozenAmount;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 所属组织ID */
    @Column(name = "own_organ_id")
    private String ownOrganId;
    /** 操作人ID */
    @Column(name = "operator_id")
    private String operatorId;
    /** 生效结束时间 */
    @Column(name = "effective_end_time")
    private java.util.Date effectiveEndTime;
    /** 生效起始时间 */
    @Column(name = "effective_start_time")
    private java.util.Date effectiveStartTime;
    /** 支用次数限制(-1表示无限制) */
    @Column(name = "loan_times_limit")
    private Integer loanTimesLimit;
    /** 已使用支用次数 */
    @Column(name = "use_loan_times")
    private Integer useLoanTimes;
    /** 已使用额度 */
    @Column(name = "used_amount")
    private java.math.BigDecimal usedAmount;
    /** 使用中额度 */
    @Column(name = "using_amount")
    private java.math.BigDecimal usingAmount;
    /** 总额度 */
    @Column(name = "total_amount")
    private java.math.BigDecimal totalAmount;
    /** 状态 */
    @Column(name = "status")
    private String status;
    /** 额度类型(1循环/2非循环) */
    @Column(name = "credit_type")
    private String creditType;
    /** 产品名称 */
    @Column(name = "product_name")
    private String productName;
    /** 产品编号 */
    @Column(name = "product_id")
    private String productId;
    /** 手机号码 */
    @Column(name = "user_mobile")
    private String userMobile;
    /** 证件类型 */
    @Column(name = "certificate_type")
    private String certificateType;
    /** 证件号码 */
    @Column(name = "certificate_no")
    private String certificateNo;
    /** 客户类型 */
    @Column(name = "user_type")
    private String userType;
    /** 客户姓名 */
    @Column(name = "user_name")
    private String userName;
    /** 客户编号 */
    @Column(name = "user_id")
    private String userId;
}
