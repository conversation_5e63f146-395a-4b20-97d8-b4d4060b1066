package com.hsjry.core.limit.batch.common.enums;

import com.hsjry.lang.common.stereotype.enums.IEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 重算对象
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0 2023/11/8 11:09
 */
@Getter
@AllArgsConstructor
public enum EnumCalObject implements IEnum {
    /** 额度 */
    LIMIT("001", "额度"),
    /** 流水 */
    SERIAL("002", "流水"),
    ;

    /** 状态码 */
    private String code;

    /** 状态描述 */
    private String description;

    /**
     * 根据编码查找枚举
     *
     * @param code 编码
     * @return {@link EnumCalObject } 实例
     **/
    public static EnumCalObject find(String code) {
        for (EnumCalObject instance : EnumCalObject.values()) {
            if (instance.getCode()
                .equals(code)) {
                return instance;
            }
        }
        return null;
    }
}