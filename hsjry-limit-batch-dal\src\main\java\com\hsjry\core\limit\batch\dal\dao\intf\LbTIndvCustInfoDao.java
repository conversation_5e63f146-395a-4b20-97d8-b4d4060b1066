package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTIndvCustInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTIndvCustInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-个人客户信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbTIndvCustInfoDao extends IBaseDao<LbTIndvCustInfoDo> {
    /**
     * 分页查询额度中心-中间表-个人客户信息
     *
     * @param lbTIndvCustInfoQuery 条件
     * @return PageInfo<LbTIndvCustInfoDo>
     */
    PageInfo<LbTIndvCustInfoDo> selectPage(LbTIndvCustInfoQuery lbTIndvCustInfoQuery, PageParam pageParam);

    /**
     * 根据key查询额度中心-中间表-个人客户信息
     *
     * @param custNo
     * @return
     */
    LbTIndvCustInfoDo selectByKey(String custNo);

    /**
     * 根据key删除额度中心-中间表-个人客户信息
     *
     * @param custNo
     * @return
     */
    int deleteByKey(String custNo);

    /**
     * 查询额度中心-中间表-个人客户信息信息
     *
     * @param lbTIndvCustInfoQuery 条件
     * @return List<LbTIndvCustInfoDo>
     */
    List<LbTIndvCustInfoDo> selectByExample(LbTIndvCustInfoQuery lbTIndvCustInfoQuery);

    /**
     * 新增额度中心-中间表-个人客户信息信息
     *
     * @param lbTIndvCustInfo 条件
     * @return int>
     */
    int insertBySelective(LbTIndvCustInfoDo lbTIndvCustInfo);

    /**
     * 修改额度中心-中间表-个人客户信息信息
     *
     * @param lbTIndvCustInfo
     * @return
     */
    int updateBySelective(LbTIndvCustInfoDo lbTIndvCustInfo);

    /**
     * 修改额度中心-中间表-个人客户信息信息
     *
     * @param lbTIndvCustInfo
     * @param lbTIndvCustInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTIndvCustInfoDo lbTIndvCustInfo, LbTIndvCustInfoQuery lbTIndvCustInfoQuery);

    /**
     * 清空个人客户信息表数据
     * 
     * @return 影响行数
     */
    int truncateTable();

    /**
     * 从源表LC_CUST_LIMIT_OBJECT_INFO导入个人客户信息数据
     * 
     * @return 影响行数
     */
    int insertFromSource();

    /**
     * 根据客户编号列表从源表LC_CUST_LIMIT_OBJECT_INFO导入个人客户信息数据
     *
     * @param userIdList 客户编号列表
     * @return 影响行数
     */
    int insertFromSource(List<String> userIdList);
}
