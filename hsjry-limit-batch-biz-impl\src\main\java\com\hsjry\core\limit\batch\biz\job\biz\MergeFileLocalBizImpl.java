/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.biz;

import java.util.List;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;
import com.hsjry.base.common.job.biz.file.FileMergeSharedBiz;
import com.hsjry.base.common.job.biz.file.IFileBaseBuilder;
import com.hsjry.base.common.job.dto.BaseFile;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobConstant;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.util.JobUtil;
import com.hsjry.base.common.utils.IpUtil;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.core.bo.InboundSerialFileBo;
import com.hsjry.core.limit.center.dal.dao.intf.LcSliceBatchSerialDao;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.LcSliceBatchSerialQuery;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/4/17 16:04
 */
@Slf4j
@Service
public class MergeFileLocalBizImpl implements BaseOrdinaryBiz, IFileBaseBuilder {
    @Autowired
    private LcSliceBatchSerialDao lcSliceBatchSerialDao;
    @Autowired
    private FileMergeSharedBiz fileMergeSharedBiz;

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.MERGE_LOCAL_FILE;
    }

    @Override
    public String initBatchSerialNo(JobInitDto jobInitDto) {
        return String.join(JobConstant.BATCH_SEPARATOR, jobInitDto.getTenantId(), jobInitDto.getBusinessDate()
            .toString(), JobUtil.parseInpara(jobInitDto.getInPara())
            .getJobTradeCode());
    }

    private String ip;

    @PostConstruct
    public void initIp() {
        if (StringUtil.isBlank(ip)) {
            ip = IpUtil.getIPAddress();
        }
    }

    @Override
    public void execBaseJob(JobInitDto jobInitDto) {

        //获取并 校验 分片流水信息
        List<LcSliceBatchSerialDo> sliceBatchSerialDoList = this.checkSliceBatchSerialList(jobInitDto);
        if (CollectionUtil.isEmpty(sliceBatchSerialDoList)) {
            log.info("未查询到分片数据！，查询条件批次总流水为【{}】，ip地址为【{}】", jobInitDto.getBatchSerialNo(), ip);
            return;
        }
        //文件处理参数初始化
        fileMergeSharedBiz.handleMergeFileLocal(jobInitDto, Lists.newArrayList(sliceBatchSerialDoList), this);
    }

    private List<LcSliceBatchSerialDo> checkSliceBatchSerialList(JobInitDto jobInitDto) {
        //查询 出  本机ip地址 当前批次流水的所有分片数据
        LcSliceBatchSerialQuery lcSliceBatchSerialQuery = LcSliceBatchSerialQuery.builder()
            .batchSerialNo(jobInitDto.getBatchSerialNo())
            .execIp(ip)
            .build();
        return lcSliceBatchSerialDao.selectByExample(lcSliceBatchSerialQuery);
    }

    @Override
    public BaseFile getBaseFile() {
        return InboundSerialFileBo.builder()
            .build();
    }
}
