package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIndvOpLoanDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcIndvOpLoanQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中个人额度中经营贷额度数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-08-21 13:10:51
 */
public interface LbTReclcIndvOpLoanDao extends IBaseDao<LbTReclcIndvOpLoanDo> {
    /**
     * 分页查询额度中心-中间表-额度重算中个人额度中经营贷额度
     *
     * @param lbTReclcIndvOpLoanQuery 条件
     * @return PageInfo<LbTReclcIndvOpLoanDo>
     */
    PageInfo<LbTReclcIndvOpLoanDo> selectPage(LbTReclcIndvOpLoanQuery lbTReclcIndvOpLoanQuery, PageParam pageParam);

    /**
     * 根据key查询额度中心-中间表-额度重算中个人额度中经营贷额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    LbTReclcIndvOpLoanDo selectByKey(String custNo, String custLimitId);

    /**
     * 根据key删除额度中心-中间表-额度重算中个人额度中经营贷额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custNo, String custLimitId);

    /**
     * 查询额度中心-中间表-额度重算中个人额度中经营贷额度信息
     *
     * @param lbTReclcIndvOpLoanQuery 条件
     * @return List<LbTReclcIndvOpLoanDo>
     */
    List<LbTReclcIndvOpLoanDo> selectByExample(LbTReclcIndvOpLoanQuery lbTReclcIndvOpLoanQuery);

    /**
     * 新增额度中心-中间表-额度重算中个人额度中经营贷额度信息
     *
     * @param lbTReclcIndvOpLoan 条件
     * @return int>
     */
    int insertBySelective(LbTReclcIndvOpLoanDo lbTReclcIndvOpLoan);

    /**
     * 修改额度中心-中间表-额度重算中个人额度中经营贷额度信息
     *
     * @param lbTReclcIndvOpLoan
     * @return
     */
    int updateBySelective(LbTReclcIndvOpLoanDo lbTReclcIndvOpLoan);

    /**
     * 修改额度中心-中间表-额度重算中个人额度中经营贷额度信息
     *
     * @param lbTReclcIndvOpLoan
     * @param lbTReclcIndvOpLoanQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTReclcIndvOpLoanDo lbTReclcIndvOpLoan,
        LbTReclcIndvOpLoanQuery lbTReclcIndvOpLoanQuery);

    // ==================== 经营贷额度重算相关方法 ====================

    /**
     * 1.1.清空经营贷额度中间表
     */
    int truncateOpLoanLimit();

    /**
     * 1.2.插入经营贷额度客户编号和额度编号
     */
    int insertOpLoanLimit();

    /**
     * 1.3.更新经营贷额度中间表金额信息
     */
    int mergeOpLoanLimitAmount();

    /**
     * 1.4.更新额度实例金额信息
     */
    int mergeOpLoanLimitInstance();
}
