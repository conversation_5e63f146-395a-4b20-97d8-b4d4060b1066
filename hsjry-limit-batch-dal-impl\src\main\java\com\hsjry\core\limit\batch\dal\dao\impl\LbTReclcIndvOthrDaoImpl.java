package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.core.limit.batch.dal.dao.intf.LbTReclcIndvOthrDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTReclcIndvOthrMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIndvOthrDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIndvOthrExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIndvOthrKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcIndvOthrQuery;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中个人额度中其他额度数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-08-21 13:10:51
 */
@Repository
public class LbTReclcIndvOthrDaoImpl extends AbstractBaseDaoImpl<LbTReclcIndvOthrDo, LbTReclcIndvOthrMapper>
    implements LbTReclcIndvOthrDao {
    /**
     * 分页查询
     *
     * @param lbTReclcIndvOthr 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTReclcIndvOthrDo> selectPage(LbTReclcIndvOthrQuery lbTReclcIndvOthr, PageParam pageParam) {
        LbTReclcIndvOthrExample example = buildExample(lbTReclcIndvOthr);
        return PageHelper.<LbTReclcIndvOthrDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询额度中心-中间表-额度重算中个人额度中其他额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public LbTReclcIndvOthrDo selectByKey(String custNo, String custLimitId) {
        LbTReclcIndvOthrKeyDo lbTReclcIndvOthrKeyDo = new LbTReclcIndvOthrKeyDo();
        lbTReclcIndvOthrKeyDo.setCustNo(custNo);
        lbTReclcIndvOthrKeyDo.setCustLimitId(custLimitId);
        return getMapper().selectByPrimaryKey(lbTReclcIndvOthrKeyDo);
    }

    /**
     * 根据key删除额度中心-中间表-额度重算中个人额度中其他额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public int deleteByKey(String custNo, String custLimitId) {
        LbTReclcIndvOthrKeyDo lbTReclcIndvOthrKeyDo = new LbTReclcIndvOthrKeyDo();
        lbTReclcIndvOthrKeyDo.setCustNo(custNo);
        lbTReclcIndvOthrKeyDo.setCustLimitId(custLimitId);
        return getMapper().deleteByPrimaryKey(lbTReclcIndvOthrKeyDo);
    }

    /**
     * 查询额度中心-中间表-额度重算中个人额度中其他额度信息
     *
     * @param lbTReclcIndvOthr 条件
     * @return List<LbTReclcIndvOthrDo>
     */
    @Override
    public List<LbTReclcIndvOthrDo> selectByExample(LbTReclcIndvOthrQuery lbTReclcIndvOthr) {
        return getMapper().selectByExample(buildExample(lbTReclcIndvOthr));
    }

    /**
     * 新增额度中心-中间表-额度重算中个人额度中其他额度信息
     *
     * @param lbTReclcIndvOthr 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTReclcIndvOthrDo lbTReclcIndvOthr) {
        if (lbTReclcIndvOthr == null) {
            return -1;
        }

        return getMapper().insertSelective(lbTReclcIndvOthr);
    }

    /**
     * 修改额度中心-中间表-额度重算中个人额度中其他额度信息
     *
     * @param lbTReclcIndvOthr
     * @return
     */
    @Override
    public int updateBySelective(LbTReclcIndvOthrDo lbTReclcIndvOthr) {
        if (lbTReclcIndvOthr == null) {
            return -1;
        }

        return getMapper().updateByPrimaryKeySelective(lbTReclcIndvOthr);
    }

    @Override
    public int updateBySelectiveByExample(LbTReclcIndvOthrDo lbTReclcIndvOthr,
        LbTReclcIndvOthrQuery lbTReclcIndvOthrQuery) {
        return getMapper().updateByExampleSelective(lbTReclcIndvOthr, buildExample(lbTReclcIndvOthrQuery));
    }

    /**
     * 构建额度中心-中间表-额度重算中个人额度中其他额度Example信息
     *
     * @param lbTReclcIndvOthr
     * @return
     */
    public LbTReclcIndvOthrExample buildExample(LbTReclcIndvOthrQuery lbTReclcIndvOthr) {
        LbTReclcIndvOthrExample example = new LbTReclcIndvOthrExample();
        LbTReclcIndvOthrExample.Criteria criteria = example.createCriteria();
        if (lbTReclcIndvOthr != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbTReclcIndvOthr.getCustNo())) {
                criteria.andCustNoEqualTo(lbTReclcIndvOthr.getCustNo());
            }
            if (StringUtil.isNotEmpty(lbTReclcIndvOthr.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lbTReclcIndvOthr.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lbTReclcIndvOthr.getTemplateNodeId())) {
                criteria.andTemplateNodeIdEqualTo(lbTReclcIndvOthr.getTemplateNodeId());
            }
            if (StringUtil.isNotEmpty(lbTReclcIndvOthr.getLimitStatus())) {
                criteria.andLimitStatusEqualTo(lbTReclcIndvOthr.getLimitStatus());
            }
            if (null != lbTReclcIndvOthr.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbTReclcIndvOthr.getTotalAmount());
            }
            if (null != lbTReclcIndvOthr.getPreOccupyAmount()) {
                criteria.andPreOccupyAmountEqualTo(lbTReclcIndvOthr.getPreOccupyAmount());
            }
            if (null != lbTReclcIndvOthr.getRealOccupyAmount()) {
                criteria.andRealOccupyAmountEqualTo(lbTReclcIndvOthr.getRealOccupyAmount());
            }
            if (null != lbTReclcIndvOthr.getLowRiskAmount()) {
                criteria.andLowRiskAmountEqualTo(lbTReclcIndvOthr.getLowRiskAmount());
            }
            if (null != lbTReclcIndvOthr.getPreOccupyLowRiskAmt()) {
                criteria.andPreOccupyLowRiskAmtEqualTo(lbTReclcIndvOthr.getPreOccupyLowRiskAmt());
            }
            if (null != lbTReclcIndvOthr.getRealOccupyLowRiskAmt()) {
                criteria.andRealOccupyLowRiskAmtEqualTo(lbTReclcIndvOthr.getRealOccupyLowRiskAmt());
            }
        }
        buildExampleExt(lbTReclcIndvOthr, criteria);
        return example;
    }

    /**
     * 构建额度中心-中间表-额度重算中个人额度中其他额度ExampleExt方法
     *
     * @param lbTReclcIndvOthr
     * @return
     */
    public void buildExampleExt(LbTReclcIndvOthrQuery lbTReclcIndvOthr, LbTReclcIndvOthrExample.Criteria criteria) {

        //自定义实现
    }

    // ==================== 其他额度重算相关方法实现 ====================

    /**
     * 2.1.清空其他额度中间表
     */
    @Override
    public int truncateOtherLimit() {
        return getMapper().truncateOtherLimit();
    }

    /**
     * 2.2.插入其他额度客户编号和额度编号
     */
    @Override
    public int insertOtherLimit() {
        return getMapper().insertOtherLimit();
    }

    /**
     * 2.3.更新其他额度中间表金额信息
     */
    @Override
    public int mergeOtherLimitAmount() {
        return getMapper().mergeOtherLimitAmount();
    }

    /**
     * 2.4.更新额度实例金额信息
     */
    @Override
    public int mergeOtherLimitInstance() {
        return getMapper().mergeOtherLimitInstance();
    }

}
