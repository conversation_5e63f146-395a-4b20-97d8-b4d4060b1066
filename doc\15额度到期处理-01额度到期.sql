-- INEFFECTIVE("010", "未生效"),
--     <PERSON><PERSON>ID("020", "生效"),
--     F<PERSON><PERSON><PERSON>("030", "冻结"),
--     BREAK("040", "终止"),
--     EXPIRE("050", "到期"),
--     INVALID("060", "失效"),
--     DGKHEDTX("HNNSDGKHEDTX", "对公客户额度体系"),
--     GRKHEDTX("HNNSGRKHEDTX", "个人客户额度体系"),
--     TYKHEDTX("HNNSTYKHEDTX", "同业客户额度体系"),
--     JTKHEDTX("HNNSJTKHEDTX", "集团客户额度体系"),
-- 查询额度状态为[020-生效]和[030-冻结]、生效结束时间小于当前营业日期的额度实例信息
select *
from lc_cust_limit_info lcli
where lcli.LIMIT_TEMPLATE_ID in ('HNNSDGKHEDTX', 'HNNSGRKHEDTX', 'HNNSTYKHEDTX', 'HNNSJTKHEDTX')
  and lcli.LIMIT_STATUS in ('020', '030')
  and lcli.EFFECTIVE_END_TIME < sysdate;
select length('lb_t_lmt_exp_cont_lmt_ocp_rltv')
from dual;

create table lb_t_lmt_exp_cont_lmt_ocp_rltv
(
    TENANT_ID VARCHAR2 (3) not null,
    CREATE_TIME DATE,
    UPDATE_TIME DATE,
    CUST_NO VARCHAR2 (60) not null,
    CUST_LIMIT_ID VARCHAR2 (32) not null

)/

comment on table lb_t_lmt_exp_cont_lmt_ocp_rltv is '额度中心-中间表-额度到期中合同与额度占用记录'
/

select *
from LC_CORP_LMT_VIEW;
select *
from LC_CUST_LIMIT_OBJECT_INFO t;

select length('LB_T_LMT_EXP_RL_OCP_AMT')
from dual;
select *
from LB_T_LMT_EXP_RL_OCP_AMT;
-- 查询额度状态为[020-生效]和[030-冻结]且生效结束时间小于[营业日期]的[额度操作流水]插入[额度中心-中间表-额度到期中实占金额相关]
insert into LB_T_LMT_EXP_RL_OCP_AMT(tenant_id, create_time, update_time, operator_id, own_organ_id, limit_object_id,
                                    cust_limit_id, limit_status, effective_end_time, relation_id, real_occupy_amount,
                                    currency, low_risk_currency, operate_type, operate_amount, operate_low_risk_amount,
                                    remark)
select lcli.TENANT_ID,
       lcli.CREATE_TIME,
       lcli.UPDATE_TIME,
       lcli.OPERATOR_ID,
       lcli.OWN_ORGAN_ID,
       lcli.LIMIT_OBJECT_ID,
       lcli.CUST_LIMIT_ID,
       lcli.LIMIT_STATUS,
       lcli.EFFECTIVE_END_TIME,
       lcli.RELATION_ID,
       lclai.REAL_OCCUPY_AMOUNT,
       lclai.CURRENCY,
       lclai.LOW_RISK_CURRENCY,
       case when lclai.REAL_OCCUPY_AMOUNT = 0 then '013' else '026' end            as operate_type,
       0                                                                           as operate_amount,
       0                                                                           as operate_low_risk_amount,
       case
           when lclai.REAL_OCCUPY_AMOUNT = 0 then '额度到期处理中结清:实占金额为0的时候插入[013-失效额度]流水'
           else '额度到期处理中结清:实占金额不为0的时候插入[026-到期额度]流水' end as remark
from LC_CUST_LIMIT_INFO lcli
         inner join LC_CUST_LIMIT_AMT_INFO lclai
                    on lcli.CUST_LIMIT_ID = lclai.CUST_LIMIT_ID and
                       lcli.LIMIT_OBJECT_ID = lclai.CUST_NO
where lcli.LIMIT_TEMPLATE_ID in ('HNNSDGKHEDTX', 'HNNSGRKHEDTX', 'HNNSTYKHEDTX', 'HNNSJTKHEDTX')
  and lcli.LIMIT_STATUS in ('020', '030')
  and lcli.EFFECTIVE_END_TIME < '营业日期';


-- 将实占金额为0的额度状态设置为[060-失效]
update LC_CUST_LIMIT_INFO t
set t.LIMIT_STATUS = '060'
where t.CUST_LIMIT_ID in (select distinct src.CUST_LIMIT_ID
                          from LB_T_LMT_EXP_RL_OCP_AMT src
                          where src.REAL_OCCUPY_AMOUNT = 0);
-- 插入操作类型为[013-失效额度]额度操作流水
INSERT INTO LC_CUST_LIMIT_OPERATE_SERIAL (GLOBAL_SERIAL_NO, SERIAL_NO, CHANNEL_NO, BIZ_DATETIME, INBOUND_SERIAL_NO,
                                          INBOUND_SERIAL_DATETIME, TENANT_ID, CREATE_TIME, UPDATE_TIME, CLOS_SERIAL_NO,
                                          STATUS, OPERATOR_ID, OWN_ORGAN_ID, OPERATE_TYPE, OPERATE_AMOUNT,
                                          OPERATE_AMOUNT_ID, OPERATE_AMOUNT_CURRENCY, OPERATE_LOW_RISK_AMOUNT,
                                          OPERATE_LOW_RISK_AMT_ID, OPERATE_LOW_RISK_CURRENCY, RELATION_ID,
                                          LAST_INBOUND_SERIAL_NO, CUST_LIMIT_ID, OPERATE_DIRECTION, FAIL_REASON, REMARK,
                                          ENTITY_ID, EXCHANGE_RATE_VERSION, CONTRACT_RECAL_FLAG, OPERATE_PATH, CUST_NO)
SELECT GENERATE_SERIAL_NO('LCLOS', 'GSN') AS GLOBAL_SERIAL_NO,
       GENERATE_SERIAL_NO('LCLOS', 'SN')  AS SERIAL_NO,
       NULL                               AS CHANNEL_NO,
       SYSDATE                            AS BIZ_DATETIME,
       GENERATE_SERIAL_NO('LCLOS', 'ISN') AS INBOUND_SERIAL_NO,
       SYSDATE                            AS INBOUND_SERIAL_DATETIME,
       src.TENANT_ID                      AS TENANT_ID,
       SYSDATE                            AS CREATE_TIME,
       SYSDATE                            AS UPDATE_TIME,
       GENERATE_SERIAL_NO('LCLOS', 'CSN') AS CLOS_SERIAL_NO,
       '020'                              AS STATUS,
       src.OPERATOR_ID                    AS OPERATOR_ID,
       src.OWN_ORGAN_ID                   AS OWN_ORGAN_ID,
       src.OPERATE_TYPE                   AS OPERATE_TYPE,
       0                                  AS OPERATE_AMOUNT,
       NULL                               AS OPERATE_AMOUNT_ID,
       src.CURRENCY                       AS OPERATE_AMOUNT_CURRENCY,
       src.OPERATE_LOW_RISK_AMOUNT        AS OPERATE_LOW_RISK_AMOUNT,
       NULL                               AS OPERATE_LOW_RISK_AMT_ID,
       src.LOW_RISK_CURRENCY              AS OPERATE_LOW_RISK_CURRENCY,
       src.RELATION_ID                    AS RELATION_ID,
       NULL                               AS LAST_INBOUND_SERIAL_NO,
       src.CUST_LIMIT_ID                  AS CUST_LIMIT_ID,
       NULL                               AS OPERATE_DIRECTION,
       NULL                               AS FAIL_REASON,
       src.REMARK                         AS REMARK,
       NULL                               AS ENTITY_ID,
       NULL                               AS EXCHANGE_RATE_VERSION,
       NULL                               AS CONTRACT_RECAL_FLAG,
       NULL                               AS OPERATE_PATH,
       src.LIMIT_OBJECT_ID                AS CUST_NO
FROM LB_T_LMT_EXP_RL_OCP_AMT src
where src.REAL_OCCUPY_AMOUNT = 0;

-- 将实占金额不为0的额度状态设置为[050-到期]
update LC_CUST_LIMIT_INFO t
set t.LIMIT_STATUS = '050'
where t.CUST_LIMIT_ID in (select distinct src.CUST_LIMIT_ID
                          from LB_T_LMT_EXP_RL_OCP_AMT src
                          where src.REAL_OCCUPY_AMOUNT <> 0);
-- 插入操作类型为[026-到期额度]额度操作流水
INSERT INTO LC_CUST_LIMIT_OPERATE_SERIAL (GLOBAL_SERIAL_NO, SERIAL_NO, CHANNEL_NO, BIZ_DATETIME, INBOUND_SERIAL_NO,
                                          INBOUND_SERIAL_DATETIME, TENANT_ID, CREATE_TIME, UPDATE_TIME, CLOS_SERIAL_NO,
                                          STATUS, OPERATOR_ID, OWN_ORGAN_ID, OPERATE_TYPE, OPERATE_AMOUNT,
                                          OPERATE_AMOUNT_ID, OPERATE_AMOUNT_CURRENCY, OPERATE_LOW_RISK_AMOUNT,
                                          OPERATE_LOW_RISK_AMT_ID, OPERATE_LOW_RISK_CURRENCY, RELATION_ID,
                                          LAST_INBOUND_SERIAL_NO, CUST_LIMIT_ID, OPERATE_DIRECTION, FAIL_REASON, REMARK,
                                          ENTITY_ID, EXCHANGE_RATE_VERSION, CONTRACT_RECAL_FLAG, OPERATE_PATH, CUST_NO)
SELECT GENERATE_SERIAL_NO('LCLOS', 'GSN') AS GLOBAL_SERIAL_NO,
       GENERATE_SERIAL_NO('LCLOS', 'SN')  AS SERIAL_NO,
       NULL                               AS CHANNEL_NO,
       SYSDATE                            AS BIZ_DATETIME,
       GENERATE_SERIAL_NO('LCLOS', 'ISN') AS INBOUND_SERIAL_NO,
       SYSDATE                            AS INBOUND_SERIAL_DATETIME,
       src.TENANT_ID                      AS TENANT_ID,
       SYSDATE                            AS CREATE_TIME,
       SYSDATE                            AS UPDATE_TIME,
       GENERATE_SERIAL_NO('LCLOS', 'CSN') AS CLOS_SERIAL_NO,
       '020'                              AS STATUS,
       src.OPERATOR_ID                    AS OPERATOR_ID,
       src.OWN_ORGAN_ID                   AS OWN_ORGAN_ID,
       src.OPERATE_TYPE                   AS OPERATE_TYPE,
       src.OPERATE_AMOUNT                 AS OPERATE_AMOUNT,
       NULL                               AS OPERATE_AMOUNT_ID,
       src.CURRENCY                       AS OPERATE_AMOUNT_CURRENCY,
       src.OPERATE_LOW_RISK_AMOUNT        AS OPERATE_LOW_RISK_AMOUNT,
       NULL                               AS OPERATE_LOW_RISK_AMT_ID,
       src.LOW_RISK_CURRENCY              AS OPERATE_LOW_RISK_CURRENCY,
       src.RELATION_ID                    AS RELATION_ID,
       NULL                               AS LAST_INBOUND_SERIAL_NO,
       src.CUST_LIMIT_ID                  AS CUST_LIMIT_ID,
       NULL                               AS OPERATE_DIRECTION,
       NULL                               AS FAIL_REASON,
       src.REMARK                         AS REMARK,
       NULL                               AS ENTITY_ID,
       NULL                               AS EXCHANGE_RATE_VERSION,
       NULL                               AS CONTRACT_RECAL_FLAG,
       NULL                               AS OPERATE_PATH,
       src.LIMIT_OBJECT_ID                AS CUST_NO
FROM LB_T_LMT_EXP_RL_OCP_AMT src
where src.REAL_OCCUPY_AMOUNT <> 0;;

-- 终止合同恢复 TODO


-- 预占取消，更新预占截止时间
-- 查询[003-预占额度]的[额度操作流水]后插入[额度中心-中间表-额度到期中预占额度流水相关]
insert into LB_T_LMT_EXP_PRE_OCP(tenant_id, limit_object_id, cust_limit_id, inbound_serial_no, status, operate_type)
select lclos.TENANT_ID,
       lclos.CUST_NO as limit_object_id,
       lclos.cust_limit_id,
       lclos.INBOUND_SERIAL_NO,
       lclos.status,
       lclos.operate_type
from LC_CUST_LIMIT_OPERATE_SERIAL lclos
where lclos.STATUS = '020'
  and lclos.OPERATE_TYPE in ('003')
  and lclos.CUST_LIMIT_ID in (select src.CUST_LIMIT_ID from LB_T_LMT_EXP_RL_OCP_AMT src);

-- 更新操作类型为[001-预发放]的预发放截止时间
update LC_ENTITY_OPERATE_SERIAL PRE_OCCUPY
set PRE_OCCUPY.PRE_GRANT_EXPIRY_DATE_STR = TO_CHAR('营业日期', 'YYYYMMDD')
where PRE_OCCUPY.OPERATE_TYPE = '001'
  and PRE_OCCUPY.OPERATE_STATUS = '020'
  and PRE_OCCUPY.INBOUND_SERIAL_NO in (select distinct src.INBOUND_SERIAL_NO
                                       from LB_T_LMT_EXP_PRE_OCP src);
