package com.hsjry.core.limit.batch.dal.dao.intf;

import com.hsjry.core.limit.batch.dal.dao.model.LbHElcblDsctLmtOcpDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbHElcblDsctLmtOcpQuery;

import java.util.List;

import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 电票系统-历史表-日终贴现额度占用同步数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbHElcblDsctLmtOcpDao extends IBaseDao<LbHElcblDsctLmtOcpDo> {
    /**
     * 分页查询电票系统-历史表-日终贴现额度占用同步
     *
     * @param lbHElcblDsctLmtOcpQuery 条件
     * @return PageInfo<LbHElcblDsctLmtOcpDo>
     */
    PageInfo<LbHElcblDsctLmtOcpDo> selectPage(LbHElcblDsctLmtOcpQuery lbHElcblDsctLmtOcpQuery, PageParam pageParam);

    /**
     * 根据key查询电票系统-历史表-日终贴现额度占用同步
     *
     * @param billNumb
     * @param billRangeStart
     * @param billRangeEnd
     * @param dataDate
     * @return
     */
    LbHElcblDsctLmtOcpDo selectByKey(String billNumb, String billRangeStart, String billRangeEnd, String dataDate);

    /**
     * 根据key删除电票系统-历史表-日终贴现额度占用同步
     *
     * @param billNumb
     * @param billRangeStart
     * @param billRangeEnd
     * @param dataDate
     * @return
     */
    int deleteByKey(String billNumb, String billRangeStart, String billRangeEnd, String dataDate);

    /**
     * 查询电票系统-历史表-日终贴现额度占用同步信息
     *
     * @param lbHElcblDsctLmtOcpQuery 条件
     * @return List<LbHElcblDsctLmtOcpDo>
     */
    List<LbHElcblDsctLmtOcpDo> selectByExample(LbHElcblDsctLmtOcpQuery lbHElcblDsctLmtOcpQuery);

    /**
     * 新增电票系统-历史表-日终贴现额度占用同步信息
     *
     * @param lbHElcblDsctLmtOcp 条件
     * @return int>
     */
    int insertBySelective(LbHElcblDsctLmtOcpDo lbHElcblDsctLmtOcp);

    /**
     * 修改电票系统-历史表-日终贴现额度占用同步信息
     *
     * @param lbHElcblDsctLmtOcp
     * @return
     */
    int updateBySelective(LbHElcblDsctLmtOcpDo lbHElcblDsctLmtOcp);

    /**
     * 修改电票系统-历史表-日终贴现额度占用同步信息
     *
     * @param lbHElcblDsctLmtOcp
     * @param lbHElcblDsctLmtOcpQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbHElcblDsctLmtOcpDo lbHElcblDsctLmtOcp,
        LbHElcblDsctLmtOcpQuery lbHElcblDsctLmtOcpQuery);

    int deleteByDataDate(String dataDate);
}
