package com.hsjry.core.limit.batch.biz.convert.copy;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.hsjry.core.limit.batch.common.dto.file.LbCLimitObjectInfoDto;
import com.hsjry.core.limit.batch.dal.dao.model.LbCLimitObjectInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitObjectInfoDo;
import com.hsjry.lang.common.utils.DateUtil;
import com.hsjry.lang.common.utils.StringUtil;

/**
 * 额度实例所属对象信息转换类
 *
 * <AUTHOR>
 * @date 2025-07-09 12:31:05
 */
public class LbCLimitObjectInfoConverter {

    /**
     * DTO转DO
     *
     * @param dto DTO对象
     * @return DO对象
     */
    public LbCLimitObjectInfoDo dtoToDo(LbCLimitObjectInfoDto dto) {
        if (dto == null) {
            return null;
        }
        return LbCLimitObjectInfoCnvs.INSTANCE.dtoToDo(dto);
    }

    /**
     * DO转DTO
     *
     * @param limitObjectInfoDo DO对象
     * @return DTO对象
     */
    public LbCLimitObjectInfoDto doToDto(LbCLimitObjectInfoDo limitObjectInfoDo) {
        if (limitObjectInfoDo == null) {
            return null;
        }
        return LbCLimitObjectInfoCnvs.INSTANCE.do2Dto(limitObjectInfoDo);
    }

    /**
     * DTO列表转DO列表
     *
     * @param dtoList DTO列表
     * @return DO列表
     */
    public List<LbCLimitObjectInfoDo> dtoListToDoList(List<LbCLimitObjectInfoDto> dtoList) {
        if (dtoList == null) {
            return null;
        }

        List<LbCLimitObjectInfoDo> doList = new ArrayList<>(dtoList.size());
        for (LbCLimitObjectInfoDto dto : dtoList) {
            doList.add(dtoToDo(dto));
        }
        return doList;
    }

    /**
     * DO列表转DTO列表
     *
     * @param doList DO列表
     * @return DTO列表
     */
    public List<LbCLimitObjectInfoDto> doListToDtoList(List<LbCLimitObjectInfoDo> doList) {
        if (doList == null) {
            return null;
        }

        List<LbCLimitObjectInfoDto> dtoList = new ArrayList<>(doList.size());
        for (LbCLimitObjectInfoDo limitInfoDo : doList) {
            dtoList.add(doToDto(limitInfoDo));
        }
        return dtoList;
    }

    /**
     * 从字符串数组创建DTO (根据CSV文件头字段顺序)
     *
     * @param fields 字段数组
     * @return DTO对象
     */
    public LbCLimitObjectInfoDto fieldsToDto(String[] fields) {
        if (fields == null || fields.length == 0) {
            return null;
        }
        return LbCLimitObjectInfoDto.builder()
            .tenantId(safeGet(fields, 0))
            .createTime(parseDate(safeGet(fields, 1)))
            .updateTime(parseDate(safeGet(fields, 2)))
            .custLimitId(safeGet(fields, 3))
            .userId(safeGet(fields, 4))
            .outUserId(safeGet(fields, 5))
            .userType(safeGet(fields, 6))
            .userName(safeGet(fields, 7))
            .userCertificateKind(safeGet(fields, 8))
            .userCertificateNo(safeGet(fields, 9))
            .ibFinancialProdId(safeGet(fields, 10))
            .ibFinancialProdCoreId(safeGet(fields, 11))
            .ibFinancialProdType(safeGet(fields, 12))
            .ibFinancialProdName(safeGet(fields, 13))
            .ibFinancialType(safeGet(fields, 14))
            .ibFinancialId(safeGet(fields, 15))
            .build();
    }

    /**
     * 安全获取数组元素
     */
    private String safeGet(String[] fields, int index) {
        if (fields.length > index) {
            return trimToNull(fields[index]);
        }
        return null;
    }

    /**
     * 字符串去空格并转换为null
     */
    private String trimToNull(String str) {
        return StringUtil.trimToNull(str);
    }

    /**
     * 解析日期字符串
     */
    private Date parseDate(String dateStr) {
        if (StringUtil.isBlank(dateStr)) {
            return null;
        }
        try {
            // 尝试多种日期格式
            if (dateStr.length() == 8) {
                return DateUtil.parseDate(dateStr, "yyyyMMdd");
            } else if (dateStr.length() == 14) {
                return DateUtil.parseDate(dateStr, "yyyyMMddHHmmss");
            } else if (dateStr.contains("-")) {
                return DateUtil.parseDate(dateStr, "yyyy-MM-dd");
            } else if (dateStr.contains("/")) {
                return DateUtil.parseDate(dateStr, "yyyy/MM/dd");
            }
        } catch (Exception e) {
            // 日期解析失败，返回null
        }
        return null;
    }

    /**
     * 解析BigDecimal
     */
    private BigDecimal parseBigDecimal(String numStr) {
        if (StringUtil.isBlank(numStr)) {
            return null;
        }
        try {
            return new BigDecimal(numStr.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * LcCustLimitObjectInfoDo转LbCLimitObjectInfoDo
     *
     * @param lcCustLimitObjectInfoDo 源DO对象
     * @return 目标DO对象
     */
    public LbCLimitObjectInfoDo objectInfoDoToInfoDo(LcCustLimitObjectInfoDo lcCustLimitObjectInfoDo) {
        if (lcCustLimitObjectInfoDo == null) {
            return null;
        }
        return LbCLimitObjectInfoCnvs.INSTANCE.do2Copy(lcCustLimitObjectInfoDo);
    }

    /**
     * LcCustLimitObjectInfoDo转LbCLimitObjectInfoDo
     *
     * @param model 源DO对象
     * @return 目标DO对象
     */
    public static LbCLimitObjectInfoDo do2Copy(LcCustLimitObjectInfoDo model) {
        return LbCLimitObjectInfoCnvs.INSTANCE.do2Copy(model);
    }

    /**
     * LcCustLimitObjectInfoDo列表转LbCLimitObjectInfoDo列表
     *
     * @param doList 源DO列表
     * @return 目标DO列表
     */
    public static List<LbCLimitObjectInfoDo> doList2CopyList(List<LcCustLimitObjectInfoDo> doList) {
        if (null == doList) {
            return Collections.emptyList();
        }
        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (doList.size() / 0.75f) + 1, 16);
        return doList.parallelStream().map(LbCLimitObjectInfoConverter::do2Copy)//
            .collect(Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
    }
    /**
     * Integer转BigDecimal工具方法
     */
    private BigDecimal integerToBigDecimal(Integer value) {
        return value == null ? null : new BigDecimal(value);
    }
}