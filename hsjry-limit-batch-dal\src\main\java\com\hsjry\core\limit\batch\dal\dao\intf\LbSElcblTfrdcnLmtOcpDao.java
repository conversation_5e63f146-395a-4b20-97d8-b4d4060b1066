package com.hsjry.core.limit.batch.dal.dao.intf;

import com.hsjry.core.limit.batch.dal.dao.model.LbSElcblTfrdcnLmtOcpDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSElcblTfrdcnLmtOcpQuery;

import java.util.List;

import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 电票系统-落地表-日终转贴现额度占用同步数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbSElcblTfrdcnLmtOcpDao extends IBaseDao<LbSElcblTfrdcnLmtOcpDo> {
    /**
     * 分页查询电票系统-落地表-日终转贴现额度占用同步
     *
     * @param lbSElcblTfrdcnLmtOcpQuery 条件
     * @return PageInfo<LbSElcblTfrdcnLmtOcpDo>
     */
    PageInfo<LbSElcblTfrdcnLmtOcpDo> selectPage(LbSElcblTfrdcnLmtOcpQuery lbSElcblTfrdcnLmtOcpQuery,
        PageParam pageParam);

    /**
     * 根据key查询电票系统-落地表-日终转贴现额度占用同步
     *
     * @param batchNumb
     * @param billNumb
     * @param billRangeStart
     * @param billRangeEnd
     * @return
     */
    LbSElcblTfrdcnLmtOcpDo selectByKey(String batchNumb, String billNumb, String billRangeStart, String billRangeEnd);

    /**
     * 根据key删除电票系统-落地表-日终转贴现额度占用同步
     *
     * @param batchNumb
     * @param billNumb
     * @param billRangeStart
     * @param billRangeEnd
     * @return
     */
    int deleteByKey(String batchNumb, String billNumb, String billRangeStart, String billRangeEnd);

    /**
     * 查询电票系统-落地表-日终转贴现额度占用同步信息
     *
     * @param lbSElcblTfrdcnLmtOcpQuery 条件
     * @return List<LbSElcblTfrdcnLmtOcpDo>
     */
    List<LbSElcblTfrdcnLmtOcpDo> selectByExample(LbSElcblTfrdcnLmtOcpQuery lbSElcblTfrdcnLmtOcpQuery);

    /**
     * 新增电票系统-落地表-日终转贴现额度占用同步信息
     *
     * @param lbSElcblTfrdcnLmtOcp 条件
     * @return int>
     */
    int insertBySelective(LbSElcblTfrdcnLmtOcpDo lbSElcblTfrdcnLmtOcp);

    /**
     * 修改电票系统-落地表-日终转贴现额度占用同步信息
     *
     * @param lbSElcblTfrdcnLmtOcp
     * @return
     */
    int updateBySelective(LbSElcblTfrdcnLmtOcpDo lbSElcblTfrdcnLmtOcp);

    /**
     * 修改电票系统-落地表-日终转贴现额度占用同步信息
     *
     * @param lbSElcblTfrdcnLmtOcp
     * @param lbSElcblTfrdcnLmtOcpQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbSElcblTfrdcnLmtOcpDo lbSElcblTfrdcnLmtOcp,
        LbSElcblTfrdcnLmtOcpQuery lbSElcblTfrdcnLmtOcpQuery);
}
