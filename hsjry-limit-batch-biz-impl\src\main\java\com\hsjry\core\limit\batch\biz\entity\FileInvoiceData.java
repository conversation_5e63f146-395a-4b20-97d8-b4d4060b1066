/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/11/7 10:28
 */
@Data
public class FileInvoiceData {

    /** 借据编号 */
    private String loanInvoiceId;
    /** 借据金额 */
    private java.math.BigDecimal amount;
    /** 借据余额 */
    private java.math.BigDecimal balance;
    /** 币种 */
    private String currency;
    /** 借据状态;010-正常、020-结清 */
    private String invoiceStatus;
    /** 更新账务时间 */
    private Integer updateAcctDate;
    /** 垫款标识 EnumBool */
    private String advancedMoneyFlag;
    /** 核心合同编号 */
    private String coreContractId;
    /** 垫款核心合同编号 */
    private String advancedCoreContractId;
    /** 生效开始时间 */
    private java.util.Date effectiveStartTime;
    /** 生效结束时间 */
    private java.util.Date effectiveEndTime;
    /** 垫款借据编号 */
    private String advancedLoanInvoiceId;
    /** 国结借据编号 */
    private String settleLoanInvoiceId;

}
