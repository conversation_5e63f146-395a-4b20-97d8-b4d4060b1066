package com.hsjry.core.limit.batch.biz.job.sharding.biz.copy;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.limit.EnumLimitHandlerStatus;
import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.biz.convert.copy.LbCEntityInfoConverter;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.limit.AbstractShardingPrepareBiz;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.dal.dao.intf.LbCEntityInfoDao;
import com.hsjry.core.limit.batch.dal.dao.model.LbCEntityInfoDo;
import com.hsjry.core.limit.center.dal.dao.intf.EntityInfoBatchDao;
import com.hsjry.core.limit.center.dal.dao.model.LcEntityInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.EntityInfoBatchQuery;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
/**
 * 实体信息数据同步到[核心系统-落地表-实体信息表]
 * 从源表lc_entity_info同步数据到目标表lb_c_entity_info
 *
 * <AUTHOR>
 * @date 2025-07-09 12:31:05
 */
@Slf4j
@Service("lbCEntityInfoBakSyncImpl")
@RequiredArgsConstructor
public class LbCEntityInfoBakSyncImpl extends AbstractShardingPrepareBiz<EntityInfoBatchQuery>
    implements JobCoreBusiness<LcEntityInfoDo> {

    private final LbCEntityInfoDao lbCEntityInfoDao;
    private final EntityInfoBatchDao entityInfoBatchDao;

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.C_ENTITY_INFO_BAK_SYNC;
    }

    @Override
    public Integer selectCountByCurrentGroupFromDb(EntityInfoBatchQuery query) {
        Integer count = entityInfoBatchDao.selectCountByCurrentGroup(query);
        return count != null ? count : 0;
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);
        log.info(prefixLog + "====================== 分片逻辑 start ================================================");

        List<JobShared> jobSharedList = new ArrayList<>();
        Integer fixNum = jobInitDto.getFixNum();

        // 统计总数量
        EntityInfoBatchQuery countQuery = EntityInfoBatchQuery.builder()
            .tenantId(AppParamUtil.getTenantId())
            .build();
        Integer totalCount = entityInfoBatchDao.selectCountByCurrentGroup(countQuery);
        if (totalCount == null || totalCount == 0) {
            log.info(prefixLog + "没有数据需要处理，总数据量: {}", totalCount);
            return jobSharedList;
        }

        // 计算分片数
        int shardCount = (totalCount + fixNum - 1) / fixNum;
        for (int i = 0; i < shardCount; i++) {
            int batchNum = i + 1;
            int offset = i * fixNum;
            int limit = Math.min(fixNum, totalCount - offset);

            // 构建查询条件（写入extParam）
            EntityInfoBatchQuery shardQuery = EntityInfoBatchQuery.builder()
                .tenantId(AppParamUtil.getTenantId())
                .offset(offset)
                .limit(limit)
                .build();

            JobShared jobShared = new JobShared();
            jobShared.setBatchSerialNo(batchSerialNo);
            jobShared.setBusinessDate(businessDate);
            jobShared.setBatchNum(batchNum);
            jobShared.setOffset(offset);
            jobShared.setLimit(limit);
            jobShared.setFixNum(fixNum);
            jobShared.setInPara(jobInitDto.getInPara());
            jobShared.setExtParam(GsonUtil.obj2Json(shardQuery));
            jobSharedList.add(jobShared);

            log.info(prefixLog + "生成分片[{}]: offset={}, limit={}", batchNum, offset, limit);
        }

        log.info(prefixLog + "====================== 分片逻辑 end ================================================");
        log.info(prefixLog + "实体信息同步分片任务生成完成,共{}个分片", jobSharedList.size());
        return jobSharedList;
    }

    @Override
    public ShardingResult<LcEntityInfoDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info(prefixLog + "开始查询分片数据,分片号:[{}]", batchNum);

        ShardingResult<LcEntityInfoDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }

        // 创建查询条件
        EntityInfoBatchQuery query = EntityInfoBatchQuery.builder().tenantId(AppParamUtil.getTenantId())//
            .offset(jobShared.getOffset()).limit(jobShared.getLimit()).build();

        log.info(prefixLog + "=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");

        // 查询分片数据
        List<LcEntityInfoDo> dataList = entityInfoBatchDao.selectShardList(query);
        shardingResult.setShardingResultList(dataList);

        log.info(prefixLog + "分片数据查询完成,分片号:[{}],数据量:[{}]", batchNum,
            CollectionUtil.isEmpty(dataList) ? 0 : dataList.size());

        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LcEntityInfoDo> shardingResult) {
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        Integer dataSize = shardingResult.getShardingResultList().size();

        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        List<LcEntityInfoDo> dataList = shardingResult.getShardingResultList();

        if (CollectionUtil.isEmpty(dataList)) {
            log.info(prefixLog + "=========分片执行结束:[{}]数量为空===========", batchNum);
            return;
        }

        log.info(prefixLog + "=========分片执行开始:[{}]数量为[{}]===========", batchNum, dataSize);

        try {
            LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();
            // 只在第一个分片中清空目标表
            if (sliceBatchSerialDo.getBatchNum() == 1) {
                log.info(prefixLog + "第一个分片,开始清空目标表lb_c_entity_info");
                lbCEntityInfoDao.deleteAll();
                log.info(prefixLog + "目标表lb_c_entity_info清空完成");
            }
            // 更新分片流水前，初始化执行状态，确保不为空
            if (sliceBatchSerialDo.getSharedStatus() == null) {
                sliceBatchSerialDo.setSharedStatus(EnumLimitHandlerStatus.IN_HANDLE.getCode());
            }
            // 数据转换和插入
            List<LbCEntityInfoDo> targetDataList = convertSourceToTarget(dataList);
            if (CollectionUtil.isNotEmpty(targetDataList)) {
                lbCEntityInfoDao.insertList(targetDataList);
                log.info(prefixLog + "成功插入[{}]条数据到目标表", targetDataList.size());
            }

            // 更新分片流水成功
            normalUpdateSliceSerial(dataSize, sliceBatchSerialDo);

        } catch (Exception e) {
            log.error(prefixLog + "分片执行异常:[{}]", batchNum, e);
            throw new HsjryBizException(EnumBatchJobError.SYSTEM_ERR.getCode(),
                EnumBatchJobError.SYSTEM_ERR.getDescription());
        }
        log.info(prefixLog + "=========分片执行结束:[{}]数量为[{}]===========", batchNum, dataSize);
    }

    /**
     * 数据转换:从LcEntityInfoDo转换为LbCEntityInfoDo
     */
    private List<LbCEntityInfoDo> convertSourceToTarget(List<LcEntityInfoDo> sourceList) {
        if (CollectionUtil.isEmpty(sourceList)) {
            return Collections.emptyList();
        }
        return LbCEntityInfoConverter.doList2CopyList(sourceList);
    }
}
