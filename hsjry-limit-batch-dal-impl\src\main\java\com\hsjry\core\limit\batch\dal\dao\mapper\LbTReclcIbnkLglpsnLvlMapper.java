package com.hsjry.core.limit.batch.dal.dao.mapper;

import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIbnkLglpsnLvlDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 额度中心-中间表-额度重算中同业客户中法人综合授信额度mapper
 *
 * <AUTHOR>
 * @date 2025-08-21 12:16:19
 */
public interface LbTReclcIbnkLglpsnLvlMapper extends CommonMapper<LbTReclcIbnkLglpsnLvlDo> {

    // ==================== 同业客户法人层额度重算相关方法 ====================

    /**
     * 1.1.清空法人层额度中间表
     */
    int truncateLegalPersonLevelLimit();

    /**
     * 1.2.插入法人层额度客户编号和额度编号
     */
    int insertLegalPersonLevelLimit();

    /**
     * 1.3.更新法人层额度中间表金额信息
     */
    int mergeLegalPersonLevelLimitAmount();

    /**
     * 1.4.更新额度实例金额信息
     */
    int mergeLegalPersonLevelLimitInstance();
}