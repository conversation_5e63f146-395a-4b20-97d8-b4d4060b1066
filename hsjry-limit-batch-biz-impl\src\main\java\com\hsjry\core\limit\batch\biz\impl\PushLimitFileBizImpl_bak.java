//package com.hsjry.core.limit.batch.biz.impl;
//
//import java.io.BufferedWriter;
//import java.io.File;
//import java.io.FileWriter;
//import java.io.IOException;
//import java.math.BigDecimal;
//import java.nio.file.Files;
//import java.nio.file.Paths;
//import java.text.SimpleDateFormat;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
//import com.google.common.collect.Maps;
//import com.hsjry.base.common.fs.service.FileProcessService;
//import com.hsjry.base.common.fs.service.FileProcessServiceFactory;
//import com.hsjry.base.common.model.enums.limit.EnumUsageType;
//import com.hsjry.core.limit.center.common.constants.LimitCenterConstants;
//
//import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitAmtInfoDao;
//import com.hsjry.core.limit.center.dal.dao.model.*;
//import org.apache.commons.compress.utils.Lists;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Service;
//
//import com.hsjry.base.common.model.enums.asset.EnumCurrency;
//import com.hsjry.base.common.model.enums.customer.EnumUserType;
//import com.hsjry.base.common.utils.AppParamUtil;
//import com.hsjry.core.limit.batch.biz.PushLimitFileBiz;
//import com.hsjry.core.limit.batch.common.constants.LimitProductIdConstants;
//import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
//import com.hsjry.core.limit.center.core.ICustLimitQueryCore;
//import com.hsjry.core.limit.center.core.IExchangeRateCore;
//import com.hsjry.core.limit.center.core.bo.CustLimitAmountBo;
//import com.hsjry.core.limit.center.core.bo.CustLimitBo;
//import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitInfoDao;
//import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitObjectInfoDao;
//import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitRelationDao;
//import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitInfoQuery;
//import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitObjectInfoQuery;
//import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitRelationQuery;
//import com.hsjry.lang.common.exception.HsjryBizException;
//import com.hsjry.lang.common.stereotype.constants.SymbolConstants;
//import com.hsjry.lang.common.stereotype.enums.EnumBool;
//import com.hsjry.lang.common.utils.CollectionUtil;
//import com.hsjry.lang.common.utils.StringUtil;
//import com.hsjry.lang.mybatis.pagehelper.PageInfo;
//import com.hsjry.lang.mybatis.pagehelper.PageParam;
//
//import lombok.extern.slf4j.Slf4j;
//
///**
// * 额度文件推送
// *
// * <AUTHOR>
// * @version V4.0
// * @since 4.0.1 2023/3/22 16:16
// */
//@Service
//@Slf4j
//public class PushLimitFileBizImpl_bak implements PushLimitFileBiz {
//
//    @Autowired
//    private LcCustLimitInfoDao lcCustLimitInfoDao;
//    @Autowired
//    private LcCustLimitObjectInfoDao lcCustLimitObjectInfoDao;
//    @Autowired
//    private LcCustLimitRelationDao lcCustLimitRelationDao;
//
//    @Autowired
//    private ICustLimitQueryCore custLimitQueryCore;
//    @Autowired
//    private IExchangeRateCore exchangeRateCore;
//
//    /** 单笔额度节点编号 */
//    @Value("${batch.single.node.id:DGLN0012}")
//    private String singleNodeId;
//    /** 统一授信节点编号 */
//    @Value("${batch.unified.credit.node.id:DGLN0002}")
//    private String unifiedCreditNodeId;
//    /** 综合授信节点编号 */
//    @Value("${batch.comprehensive.credit.node.id:DGLN0005}")
//    private String comprehensiveCreditNodeId;
//    /** 低风险节点编号 */
//    @Value("${batch.low.risk.node.id:DGLN0009}")
//    private String lowRiskNodeId;
//
//    @Value("${user.limit.file.name:LIMITCUSTOMERLIST.txt}")
//    private String userLimitFileName;
//    @Value("${contract.limit.file.name:LIMITBUSINESSRLIST_[DATE].txt}")
//    private String contractLimitFileName;
//    @Value("${user.limit.file.localFilePath:/app/loan/limit-file}")
//    private String localFilePathDefine;
//    @Value("${contract.limit.file.remoteFilePath:/nfsdata/file/midd/ULCS/ALS/data/[DATE]/810/}")
//    private String remoteFilePathDefine;
//    @Autowired
//    private LcCustLimitAmtInfoDao lcCustLimitAmtInfoDao;
//    @Autowired
//    protected FileProcessServiceFactory serviceFactory;
//
//    @Override
//    public void pushCustomerLimit(Integer businessDate) {
//        generateCustomerLimit();
//        FileProcessService instance = serviceFactory.getInstance();
//        String absoluteLocalPath =localFilePathDefine + File.separator + userLimitFileName;
//        String absoluteRemotePath = remoteFilePathDefine+File.separator + userLimitFileName;
//        absoluteRemotePath = absoluteRemotePath.replace("[DATE]",String.valueOf(businessDate));
//        if(new File(absoluteLocalPath).exists()){
//            byte[] bFile = null;
//            try {
//                bFile = Files.readAllBytes(Paths.get(absoluteLocalPath));
//            } catch (IOException e) {
//                log.error("读取本地客户额度文件失败，path={},e={}",absoluteLocalPath,e);
//                throw new HsjryBizException("999999","读取本地客户额度文件失败");
//            }
//            instance.uploadFile(absoluteRemotePath,bFile);
//        }
//    }
//
//
//    public void generateCustomerLimit() {
//        int batchPageSize = 100;
//        int pageNum = 1;
//        // 分页查询客户信息
//        PageParam pageParam = PageParam.Builder.getInstance()
//                .addPageNum(pageNum)
//                .addPageSize(batchPageSize)
//                .build();
//
//        // 文件全路径名
//        String fullFilePath = localFilePathDefine + SymbolConstants.BACKSLASH + userLimitFileName;
//        // 创建文件
//        File file = new File(fullFilePath);
//        if (file.exists()) {
//            log.info("{}：删除文件，localFilePath = {}，fileName = {}", "用户额度信息推送", localFilePathDefine,
//                    userLimitFileName);
//            file.delete();
//        }
//
//        try (FileWriter fileWriter = new FileWriter(file);
//             BufferedWriter bufferedReader = new BufferedWriter(fileWriter)) {
//            boolean hasNextPage;
//            do {
//                PageInfo<LcCustLimitObjectInfoDo> pageInfo = lcCustLimitObjectInfoDao.selectPageUserDistinct(
//                        LcCustLimitObjectInfoQuery.builder().tenantId(AppParamUtil.getTenantId()).build(), pageParam);
//                List<LcCustLimitObjectInfoDo> list = pageInfo.getList();
//                if (CollectionUtil.isEmpty(list)) {
//                    break;
//                }
//
//                for (LcCustLimitObjectInfoDo model : list) {
//                    // 组装客户额度信息行数据
//                    String rowData = generateUserLimitData(model.getUserId());
//
//                    if (StringUtil.isNotEmpty(rowData)) {
//                        // 写入文件
//                        bufferedReader.write(rowData);
//                        bufferedReader.newLine();
//                    }
//                }
//                // 刷新缓冲区
//                bufferedReader.flush();
//
//                //下次分页 条件
//                hasNextPage = pageInfo.isHasNextPage();
//                pageParam.setPageNum(++pageNum);
//            } while (hasNextPage);
//        } catch (IOException e) {
//            throw new HsjryBizException(EnumBatchJobError.WRITE_FILE_ERROR.getCode(),
//                    EnumBatchJobError.WRITE_FILE_ERROR.getDescription(), this.getClass());
//        }
//    }
//    @Override
//    public void pushContractLimit(Integer businessDate) {
//        generateContractLimit(businessDate);
//        FileProcessService instance = serviceFactory.getInstance();
//        String absoluteLocalPath =localFilePathDefine + File.separator + contractLimitFileName;
//        absoluteLocalPath = absoluteLocalPath.replaceAll("[DATE]",String.valueOf(businessDate));
//        String absoluteRemotePath = remoteFilePathDefine+File.separator + contractLimitFileName;
//        absoluteRemotePath = absoluteRemotePath.replaceAll("[DATE]",String.valueOf(businessDate));
//        if(new File(absoluteLocalPath).exists()){
//            byte[] bFile = null;
//            try {
//                bFile = Files.readAllBytes(Paths.get(absoluteLocalPath));
//            } catch (IOException e) {
//                log.error("读取本地合同额度文件失败，path={},e={}",absoluteLocalPath,e);
//                throw new HsjryBizException("999999","读取本地合同额度文件失败");
//            }
//            instance.uploadFile(absoluteRemotePath,bFile);
//        }
//    }
//
//
//    public void generateContractLimit(Integer businessDate) {
//        contractLimitFileName = String.format(contractLimitFileName,businessDate.toString());
//        Map<String,String> productMap = new HashMap<>();
//        //综合授信
//        productMap.put("DGLN0033","3011");
//        productMap.put("DGLN0032","3011");
//        //经销商额度
//        productMap.put("DGLN0021","3031");
//        productMap.put("DGLN0020","3031");
//        //房地产按揭合作项目额度
//        productMap.put("DGLN0023","3032");
//        //核心企业额度
//        productMap.put("DGLN0025","3033");
//        //担保额度
//        productMap.put("DGLN0040","3040");
//        //监管额度
//        productMap.put("DGLN0039","3041");
//        productMap.put("DGLN0027","3041");
//        //线上业务额度
//        productMap.put("DGLN0037","3110");
//        //低风险额度
//        productMap.put("DGLN0035","3101");
//        productMap.put("DGLN0034","3101");
//        //同业综合授信额度
//        productMap.put("TYLN0006","4010");
//        //同业单项授信额度
//        productMap.put("TYLN0007","4020");
//        int batchPageSize = 100;
//        int pageNum = 1;
//        // 分页查询客户信息
//        PageParam pageParam = PageParam.Builder.getInstance()
//                .addPageNum(pageNum)
//                .addPageSize(batchPageSize)
//                .build();
//        contractLimitFileName = contractLimitFileName.replace("[DATE]", String.valueOf(businessDate));
//        // 文件全路径名
//        String fullFilePath = localFilePathDefine + SymbolConstants.BACKSLASH + contractLimitFileName;
//        // 创建文件
//        File file = new File(fullFilePath);
//        if (file.exists()) {
//            log.info("{}：删除文件，localFilePath = {}，fileName = {}", "用户额度信息推送", localFilePathDefine,
//                    contractLimitFileName);
//            file.delete();
//        }
//
//        try (FileWriter fileWriter = new FileWriter(file);
//             BufferedWriter bufferedReader = new BufferedWriter(fileWriter)) {
//            boolean hasNextPage;
//            do {
//                PageInfo<LcCustLimitInfoDo> pageInfo = lcCustLimitInfoDao.selectPage(
//                        LcCustLimitInfoQuery.builder().contractLimitFlag(EnumBool.YES.getCode()).templateNodeIdList(new ArrayList<>(productMap.keySet())).build(), pageParam);
//                List<LcCustLimitInfoDo> list = pageInfo.getList();
//                if (CollectionUtil.isEmpty(list)) {
//                    break;
//                }
//
//                List<String> rowDataList = generateContractLimitData(
//                        list.stream().map(LcCustLimitInfoKeyDo::getCustLimitId).collect(Collectors.toList()),productMap);
//
//                if (CollectionUtil.isNotEmpty(rowDataList)) {
//                    for (String rowData : rowDataList) {
//                        // 写入文件
//                        bufferedReader.write(rowData);
//                        bufferedReader.newLine();
//                    }
//                }
//                // 刷新缓冲区
//                bufferedReader.flush();
//
//                //下次分页 条件
//                hasNextPage = pageInfo.isHasNextPage();
//                pageParam.setPageNum(++pageNum);
//            } while (hasNextPage);
//        } catch (IOException e) {
//            throw new HsjryBizException(EnumBatchJobError.WRITE_FILE_ERROR.getCode(),
//                    EnumBatchJobError.WRITE_FILE_ERROR.getDescription(), this.getClass());
//        }
//    }
//    /**
//     * 组转用户额度行信息
//     *
//     * @param userId 用户编号
//     * @return 用户额度信息数据
//     */
//    private String generateUserLimitData(String userId) {
//        // 查客户信息
//        List<LcCustLimitObjectInfoDo> limitObjectInfoDoList = lcCustLimitObjectInfoDao.selectByExample(
//            LcCustLimitObjectInfoQuery.builder()
//                .userId(userId)
//                .build());
//        LcCustLimitObjectInfoDo objectInfo = limitObjectInfoDoList.get(0);
//        // 查客户额度信息
//        List<LcCustLimitInfoDo> limitInfoList = lcCustLimitInfoDao.selectByExample(
//            LcCustLimitInfoQuery.builder()
//                .limitObjectId(userId)
//                .build());
//
//        if (CollectionUtil.isEmpty(limitInfoList)) {
//            log.error("用户【{}】查不到额度信息!", userId);
//            return null;
//        }
//        StringBuilder stringBuilder = new StringBuilder();
//        // 数据拼接-统一客户号
//        stringBuilder.append(userId);
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-信贷客户号
//        stringBuilder.append(userId);
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-客户名称
//        stringBuilder.append(objectInfo.getUserName());
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-证件号
//        stringBuilder.append(objectInfo.getUserCertificateNo());
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-统一授信额度编号
//        stringBuilder.append(limitInfoList.stream().filter(o -> unifiedCreditNodeId.equals(o.getTemplateNodeId())).map(
//            LcCustLimitInfoKeyDo::getCustLimitId).findFirst().orElse(""));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-综合授信额度编号
//        stringBuilder.append(limitInfoList.stream()
//            .filter(o -> comprehensiveCreditNodeId.equals(o.getTemplateNodeId()))
//            .map(LcCustLimitInfoKeyDo::getCustLimitId)
//            .findFirst()
//            .orElse(""));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-单笔额度编号
//        stringBuilder.append(limitInfoList.stream().filter(o -> singleNodeId.equals(o.getTemplateNodeId())).map(
//            LcCustLimitInfoKeyDo::getCustLimitId).findFirst().orElse(""));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-低风险额度编号
//        stringBuilder.append(limitInfoList.stream().filter(o -> lowRiskNodeId.equals(o.getTemplateNodeId())).map(
//            LcCustLimitInfoKeyDo::getCustLimitId).findFirst().orElse(""));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 查询额度信息
//        List<CustLimitBo> custLimitBoList = custLimitQueryCore.queryCustLimitBoList(
//            limitInfoList.stream().map(LcCustLimitInfoKeyDo::getCustLimitId).collect(Collectors.toList()));
//        // 数据拼接-单户敞口折人民币金额
//        stringBuilder.append(custLimitBoList.stream()
//            .filter(o -> comprehensiveCreditNodeId.equals(o.getNodeId()))
//            .map(CustLimitBo::getCustLimitAmountBo)
//            .map(CustLimitAmountBo::openAmount)
//            .reduce(BigDecimal.ZERO, BigDecimal::add));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-额度折人民币敞口金额
//        stringBuilder.append(custLimitBoList.stream()
//            .filter(o -> comprehensiveCreditNodeId.equals(o.getNodeId()))
//            .map(CustLimitBo::getCustLimitAmountBo)
//            .map(CustLimitAmountBo::getTotalAmount)
//            .reduce(BigDecimal.ZERO, BigDecimal::add));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-单户折人民币敞口金额(只含本业务品种)
//        // 文件不需要推这个字段
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-表内授信敞口折人民币金额
//        stringBuilder.append(custLimitBoList.stream()
//            .filter(o -> StringUtil.isNotEmpty(o.getProductId()) && o.getProductId()
//                .startsWith(LimitProductIdConstants.PRODUCT_TYPE_ONE))
//            .map(CustLimitBo::getCustLimitAmountBo)
//            .map(bo -> equivalentToRmb(bo.realOccupyOpen(), bo.getCurrency()))
//            .reduce(BigDecimal.ZERO, BigDecimal::add));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-表外授信敞口折人民币金额
//        stringBuilder.append(custLimitBoList.stream()
//            .filter(o -> StringUtil.isNotEmpty(o.getProductId()) && o.getProductId()
//                .startsWith(LimitProductIdConstants.PRODUCT_TYPE_TWO) && !o.getProductId()
//                .startsWith(LimitProductIdConstants.PRODUCT_TYPE_THREE) && !o.getProductId()
//                .startsWith(LimitProductIdConstants.PRODUCT_TYPE_FOUR) && !o.getProductId()
//                .startsWith(LimitProductIdConstants.PRODUCT_TYPE_SIX) && !o.getProductId()
//                .startsWith(LimitProductIdConstants.PRODUCT_TYPE_FIVE))
//            .map(CustLimitBo::getCustLimitAmountBo)
//            .map(bo -> equivalentToRmb(bo.realOccupyOpen(), bo.getCurrency()))
//            .reduce(BigDecimal.ZERO, BigDecimal::add));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-银行承兑汇票敞口折人民币金额
//        stringBuilder.append(custLimitBoList.stream()
//            .filter(o -> StringUtil.isNotEmpty(o.getProductId()) && o.getProductId()
//                .startsWith(LimitProductIdConstants.PRODUCT_TYPE_SEVEN)
//                || LimitProductIdConstants.PRODUCT_TYPE_EIGHT.equals(o.getProductId())
//                || LimitProductIdConstants.PRODUCT_TYPE_NINE.equals(o.getProductId()))
//            .map(CustLimitBo::getCustLimitAmountBo)
//            .map(bo -> equivalentToRmb(bo.realOccupyOpen(), bo.getCurrency()))
//            .reduce(BigDecimal.ZERO, BigDecimal::add));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-国内信用证敞口折人民币金额
//        stringBuilder.append(custLimitBoList.stream()
//            .filter(o -> StringUtil.isNotEmpty(o.getProductId()) && o.getProductId()
//                .startsWith(LimitProductIdConstants.PRODUCT_TYPE_TEN)
//                || LimitProductIdConstants.PRODUCT_TYPE_ELEVEN.equals(o.getProductId())
//                || LimitProductIdConstants.PRODUCT_TYPE_TWELVE.equals(o.getProductId()))
//            .map(CustLimitBo::getCustLimitAmountBo)
//            .map(bo -> equivalentToRmb(bo.realOccupyOpen(), bo.getCurrency()))
//            .reduce(BigDecimal.ZERO, BigDecimal::add));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-国际信用证敞口折人民币金额
//        stringBuilder.append(custLimitBoList.stream()
//            .filter(o -> StringUtil.isNotEmpty(o.getProductId()) && (o.getProductId().startsWith(
//                LimitProductIdConstants.PRODUCT_TYPE_THIRTEEN) || o.getProductId().startsWith(
//                LimitProductIdConstants.PRODUCT_TYPE_FOURTEEN) || LimitProductIdConstants.PRODUCT_TYPE_FIFTEEN.equals(
//                o.getProductId()) || LimitProductIdConstants.PRODUCT_TYPE_SIXTEEN.equals(o.getProductId())))
//            .map(CustLimitBo::getCustLimitAmountBo)
//            .map(bo -> equivalentToRmb(bo.realOccupyOpen(), bo.getCurrency()))
//            .reduce(BigDecimal.ZERO, BigDecimal::add));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-融资性保函敞口折人民币金额
//        stringBuilder.append(custLimitBoList.stream().filter(o ->
//                StringUtil.isNotEmpty(o.getProductId()) && LimitProductIdConstants.PRODUCT_TYPE_SEVENTEEN.equals(
//                    o.getProductId()) || LimitProductIdConstants.PRODUCT_TYPE_EIGHTEEN.equals(o.getProductId())
//                    || LimitProductIdConstants.PRODUCT_TYPE_NINETEEN.equals(o.getProductId())
//                    || LimitProductIdConstants.PRODUCT_TYPE_TWENTY.equals(o.getProductId())
//                    || LimitProductIdConstants.PRODUCT_TYPE_TWENTY_ONE.equals(o.getProductId())
//                    || LimitProductIdConstants.PRODUCT_TYPE_TWENTY_TWO.equals(o.getProductId())
//                    || LimitProductIdConstants.PRODUCT_TYPE_TWENTY_THREE.equals(o.getProductId()))
//            .map(CustLimitBo::getCustLimitAmountBo)
//            .map(bo -> equivalentToRmb(bo.realOccupyOpen(), bo.getCurrency()))
//            .reduce(BigDecimal.ZERO, BigDecimal::add));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-非融资性保函敞口折人民币金额
//        stringBuilder.append(custLimitBoList.stream()
//            .filter(o ->
//                StringUtil.isNotEmpty(o.getProductId()) && LimitProductIdConstants.PRODUCT_TYPE_TWENTY_FOUR.equals(
//                    o.getProductId()) || LimitProductIdConstants.PRODUCT_TYPE_TWENTY_FIVE.equals(o.getProductId())
//                    || LimitProductIdConstants.PRODUCT_TYPE_TWENTY_SIX.equals(o.getProductId())
//                    || LimitProductIdConstants.PRODUCT_TYPE_TWENTY_SEVEN.equals(o.getProductId())
//                    || LimitProductIdConstants.PRODUCT_TYPE_TWENTY_EIGHT.equals(o.getProductId())
//                    || LimitProductIdConstants.PRODUCT_TYPE_TWENTY_NINE.equals(o.getProductId())
//                    || LimitProductIdConstants.PRODUCT_TYPE_THIRTY.equals(o.getProductId()))
//            .map(CustLimitBo::getCustLimitAmountBo)
//            .map(bo -> equivalentToRmb(bo.realOccupyOpen(), bo.getCurrency()))
//            .reduce(BigDecimal.ZERO, BigDecimal::add));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-直接融资敞口折人民币金额
//        stringBuilder.append(custLimitBoList.stream()
//            .filter(o -> StringUtil.isNotEmpty(o.getProductId()) && o.getProductId()
//                .startsWith(LimitProductIdConstants.PRODUCT_TYPE_THIRTY_ONE)
//                || LimitProductIdConstants.PRODUCT_TYPE_THIRTY_TWO.equals(o.getProductId())
//                || LimitProductIdConstants.PRODUCT_TYPE_THIRTY_THREE.equals(o.getProductId()))
//            .map(CustLimitBo::getCustLimitAmountBo)
//            .map(bo -> equivalentToRmb(bo.realOccupyOpen(), bo.getCurrency()))
//            .reduce(BigDecimal.ZERO, BigDecimal::add));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-商票保贴已使用额度折人民币金额
//        stringBuilder.append(custLimitBoList.stream()
//            .filter(o -> StringUtil.isNotEmpty(o.getProductId()) && o.getProductId()
//                .startsWith(LimitProductIdConstants.PRODUCT_TYPE_THIRTY_FOUR))
//            .map(CustLimitBo::getCustLimitAmountBo)
//            .map(bo -> equivalentToRmb(bo.realOccupyOpen(), bo.getCurrency()))
//            .reduce(BigDecimal.ZERO, BigDecimal::add));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-反向保理已使用额度折人民币金额
//        stringBuilder.append(custLimitBoList.stream()
//            .filter(o ->
//                StringUtil.isNotEmpty(o.getProductId()) && LimitProductIdConstants.PRODUCT_TYPE_THIRTY_FIVE.equals(
//                    o.getProductId()) || LimitProductIdConstants.PRODUCT_TYPE_THIRTY_SIX.equals(o.getProductId())
//                    || LimitProductIdConstants.PRODUCT_TYPE_THIRTY_SEVEN.equals(o.getProductId()))
//            .map(CustLimitBo::getCustLimitAmountBo)
//            .map(bo -> equivalentToRmb(bo.realOccupyOpen(), bo.getCurrency()))
//            .reduce(BigDecimal.ZERO, BigDecimal::add));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-贷款承诺敞口折人民币金额
//        stringBuilder.append(custLimitBoList.stream()
//            .filter(o -> StringUtil.isNotEmpty(o.getProductId()) && o.getProductId()
//                .startsWith(LimitProductIdConstants.PRODUCT_TYPE_THIRTY_EIGHT))
//            .map(CustLimitBo::getCustLimitAmountBo)
//            .map(bo -> equivalentToRmb(bo.realOccupyOpen(), bo.getCurrency()))
//            .reduce(BigDecimal.ZERO, BigDecimal::add));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-保函敞口折人民币金额
//        stringBuilder.append(custLimitBoList.stream()
//            .filter(o -> StringUtil.isNotEmpty(o.getProductId()) && (o.getProductId().startsWith(
//                LimitProductIdConstants.PRODUCT_TYPE_THIRTY_NINE) || o.getProductId().startsWith(
//                LimitProductIdConstants.PRODUCT_TYPE_FORTY) || o.getProductId().startsWith(
//                LimitProductIdConstants.PRODUCT_TYPE_FORTY_ONE)))
//            .map(CustLimitBo::getCustLimitAmountBo)
//            .map(bo -> equivalentToRmb(bo.realOccupyOpen(), bo.getCurrency()))
//            .reduce(BigDecimal.ZERO, BigDecimal::add));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-提货担保敞口折人民币金额
//        stringBuilder.append(custLimitBoList.stream()
//            .filter(o -> StringUtil.isNotEmpty(o.getProductId()) && o.getProductId()
//                .startsWith(LimitProductIdConstants.PRODUCT_TYPE_FORTY_TWO))
//            .map(CustLimitBo::getCustLimitAmountBo)
//            .map(bo -> equivalentToRmb(bo.realOccupyOpen(), bo.getCurrency()))
//            .reduce(BigDecimal.ZERO, BigDecimal::add));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-本币自营资金业务敞口
//        stringBuilder.append(custLimitBoList.stream()
//            .filter(o -> StringUtil.isNotEmpty(o.getProductId()) && EnumUserType.INTERBANK_CUSTOMER.getCode()
//                .equals(o.getUserType()) && LimitProductIdConstants.PRODUCT_TYPE_FORTY_THREE.equals(o.getProductId()))
//            .map(CustLimitBo::getCustLimitAmountBo)
//            .map(bo -> equivalentToRmb(bo.realOccupyOpen(), bo.getCurrency()))
//            .reduce(BigDecimal.ZERO, BigDecimal::add));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-本币代客理财资金业务敞口
//        stringBuilder.append(custLimitBoList.stream()
//            .filter(o -> StringUtil.isNotEmpty(o.getProductId()) && EnumUserType.INTERBANK_CUSTOMER.getCode()
//                .equals(o.getUserType()) && LimitProductIdConstants.PRODUCT_TYPE_FORTY_FOUR.equals(o.getProductId()))
//            .map(CustLimitBo::getCustLimitAmountBo)
//            .map(bo -> equivalentToRmb(bo.realOccupyOpen(), bo.getCurrency()))
//            .reduce(BigDecimal.ZERO, BigDecimal::add));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-代开业务敞口
//        stringBuilder.append(custLimitBoList.stream()
//            .filter(o -> StringUtil.isNotEmpty(o.getProductId()) && EnumUserType.INTERBANK_CUSTOMER.getCode()
//                .equals(o.getUserType()) && LimitProductIdConstants.PRODUCT_TYPE_FORTY_FIVE.equals(o.getProductId()))
//            .map(CustLimitBo::getCustLimitAmountBo)
//            .map(bo -> equivalentToRmb(bo.realOccupyOpen(), bo.getCurrency()))
//            .reduce(BigDecimal.ZERO, BigDecimal::add));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-外币业务敞口
//        stringBuilder.append(custLimitBoList.stream()
//            .filter(o -> StringUtil.isNotEmpty(o.getProductId()) && EnumUserType.INTERBANK_CUSTOMER.getCode()
//                .equals(o.getUserType()) && LimitProductIdConstants.PRODUCT_TYPE_FORTY_SIX.equals(o.getProductId()))
//            .map(CustLimitBo::getCustLimitAmountBo)
//            .map(bo -> equivalentToRmb(bo.realOccupyOpen(), bo.getCurrency()))
//            .reduce(BigDecimal.ZERO, BigDecimal::add));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-单笔业务敞口
//        stringBuilder.append(custLimitBoList.stream()
//            .map(CustLimitBo::getCustLimitAmountBo)
//            .map(bo -> equivalentToRmb(bo.openAmount(), bo.getCurrency()))
//            .reduce(BigDecimal.ZERO, BigDecimal::add));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-低风险额度币种
//        stringBuilder.append(LimitCenterConstants.DEFAULT_CURRENCY);
////        stringBuilder.append(custLimitBoList.stream().map(CustLimitBo::getCustLimitAmountBo).map(
////            CustLimitAmountBo::getLowRiskCurrency).findFirst().orElse(null));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-低风险金额
//        stringBuilder.append(custLimitBoList.stream()
//            .map(CustLimitBo::getCustLimitAmountBo)
//            .map(CustLimitAmountBo::getLowRiskAmount)
//            .map(bo -> bo.getOperateAmount(LimitCenterConstants.DEFAULT_CURRENCY))
//            .reduce(BigDecimal.ZERO, BigDecimal::add));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-低风险已用金额
//        stringBuilder.append(custLimitBoList.stream()
//            .map(CustLimitBo::getCustLimitAmountBo)
//            .map(CustLimitAmountBo::getUsedLowRiskAmt)
//            .map(bo -> bo.getOperateAmount(LimitCenterConstants.DEFAULT_CURRENCY))
//            .reduce(BigDecimal.ZERO, BigDecimal::add));
//        stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//        // 数据拼接-剩余低风险额度
//        stringBuilder.append(custLimitBoList.stream()
//            .map(CustLimitBo::getCustLimitAmountBo)
//            .map(CustLimitAmountBo::leftLowRiskAmount)
//            .map(bo -> bo.getOperateAmount(LimitCenterConstants.DEFAULT_CURRENCY))
//            .reduce(BigDecimal.ZERO, BigDecimal::add));
//        return stringBuilder.toString();
//    }
//
//    /**
//     * 组转合同额度行信息
//     *
//     * @param custLimitIdList 额度编号列表
//     * @param productMap
//     * @return 合同额度信息数据列表
//     */
//    private List<String> generateContractLimitData(List<String> custLimitIdList, Map<String, String> productMap) {
//        if (CollectionUtil.isEmpty(custLimitIdList)) {
//            return null;
//        }
//
//        List<String> result = new ArrayList<>(custLimitIdList.size());
//        //合同额度信息
//        List<CustLimitBo> custLimitBoList = custLimitQueryCore.queryCustLimitBoList(custLimitIdList);
//        // 额度关联关系
//        List<LcCustLimitRelationDo> relationDoList = lcCustLimitRelationDao.selectByExample(
//                LcCustLimitRelationQuery.builder().currentNodeLimitIdList(custLimitIdList).build());
//        // key -> currentNodeLimitId value -> List<parentNodeLimitId>
//        Map<String, List<String>> parentLimitIdMap = CollectionUtil.isEmpty(relationDoList) ? Maps.newHashMap() :
//                relationDoList.stream().collect(Collectors.groupingBy(LcCustLimitRelationDo::getCurrentNodeLimitId,
//                        Collectors.mapping(LcCustLimitRelationDo::getParentNodeLimitId, Collectors.toList())));
//        List<String> parentIdList = relationDoList.stream().map(LcCustLimitRelationDo::getParentNodeLimitId).collect(Collectors.toList());
//        //合同父级
//        Map<String,CustLimitBo> parentMap = new HashMap<>();
//        //合同父级对象信息
//        Map<String, LcCustLimitObjectInfoDo> objectMap = new HashMap<>();
//        if(CollectionUtil.isNotEmpty(parentIdList)){
//            List<CustLimitBo> custLimitBos = custLimitQueryCore.queryCustLimitBoList(parentIdList);
//            parentMap =  custLimitBos.stream().collect(
//                    Collectors.toMap(CustLimitBo::getCustLimitId, Function.identity()));
//            // 额度对象信息
//            List<LcCustLimitObjectInfoDo> limitObjectInfoDoList = lcCustLimitObjectInfoDao.selectByExample(
//                    LcCustLimitObjectInfoQuery.builder().custLimitIdList(parentIdList).build());
//            objectMap =  limitObjectInfoDoList.stream().collect(
//                    Collectors.toMap(LcCustLimitObjectInfoKeyDo::getCustLimitId, Function.identity()));
//        }
//
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
//        Map<String, LcCustLimitObjectInfoDo> finalObjectMap = objectMap;
//        Map<String, CustLimitBo> finalParentMap = parentMap;
//        custLimitBoList.forEach(bo -> {
//            // 额度关系信息
//            List<String> parentLimitIdList = parentLimitIdMap.get(bo.getCustLimitId());
//            if (CollectionUtil.isEmpty(parentLimitIdList)) {
//                parentLimitIdList = Lists.newArrayList();
//                parentLimitIdList.add("");
//            }
//            //信贷要求必须有实占的
//            if(bo.getCustLimitAmountBo().getRealOccupyAmount().compareTo(BigDecimal.ZERO) <= 0){
//                return;
//            }
//            for (String parentLimitId : parentLimitIdList) {
//                // 额度对象信息
//                LcCustLimitObjectInfoDo objectInfo = finalObjectMap.get(parentLimitId);
//                //可能为空
//                CustLimitBo parentBo = finalParentMap.get(parentLimitId);
//                StringBuilder stringBuilder = new StringBuilder();
//                // 数据拼接-业务编号
//                stringBuilder.append(bo.getRelationId() == null ? "" : bo.getRelationId());
//                stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//                // 数据拼接-额度编号(父)
//                stringBuilder.append(parentBo == null ? "":parentBo.getCustLimitId());
//                stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//                // 数据拼接-合同总额度
//                stringBuilder.append(bo.getCustLimitAmountBo().getTotalAmount());
//                stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//                // 数据拼接-币种(父)
//                stringBuilder.append(parentBo == null ? "":parentBo.getCustLimitAmountBo().getCurrency());
//                stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//                // 数据拼接-额度产品编号(父)
//                stringBuilder.append(parentBo == null ? "":parentBo.getProductId());
//                stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//                // 数据拼接-客户编号(父)
//                stringBuilder.append(objectInfo == null ? "" : objectInfo.getUserId());
//                stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//                // 数据拼接-客户名称(父)
//                stringBuilder.append(objectInfo == null ? "" : objectInfo.getUserName());
//                stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//                // 数据拼接-证件号(父)
//                stringBuilder.append(objectInfo == null ? "" : objectInfo.getUserCertificateNo());
//                stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//                // 数据拼接-剩余可用额度(父)
//                stringBuilder.append(parentBo == null ? "":parentBo.getCustLimitAmountBo().availableAmount());
//                stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//                // 数据拼接-剩余暂封额度
//                stringBuilder.append(bo.getCustLimitAmountBo().getTmpForbidAmount());
//                stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//                //数据拼接-剩余占用额度
//                stringBuilder.append(bo.getCustLimitAmountBo().getRealOccupyAmount());
//                stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//                // 数据拼接-总层级额度产品
//                stringBuilder.append(productMap.get(bo.getNodeId()));
//                stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//
//                //额度起始日(父)
//                stringBuilder.append(parentBo == null ? "":sdf.format(parentBo.getEffectiveStartTime()));
//                stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//                //额度到期日(父)
//                stringBuilder.append(parentBo == null ? "":sdf.format(parentBo.getEffectiveEndTime()));
//                stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//                //是否循环(父)
//                stringBuilder.append(parentBo == null ? "":parentBo.getLimitUsageType().equals(EnumUsageType.CYCLE_CREDIT)?"1":"2");
//                stringBuilder.append(SymbolConstants.VERTICAL_LINE);
//                //额度设立业务合同编号(父)
//                stringBuilder.append(parentBo == null || parentBo.getRelationId() == null ? "":parentBo.getRelationId());
//
//                result.add(stringBuilder.toString());
//            }
//        });
//
//        return result;
//    }
//
//    /**
//     * 金额整合人民币
//     *
//     * @param amount 金额
//     * @param currency 币种
//     * @return 折人民币金额
//     */
//    private BigDecimal equivalentToRmb(BigDecimal amount, String currency) {
//        if (amount == null) {
//            return BigDecimal.ZERO;
//        }
//        if (StringUtil.isEmpty(currency)) {
//            return amount;
//        }
//        if (EnumCurrency.CNY.getCode().equals(currency)) {
//            return amount;
//        }
//        return exchangeRateCore.exchange(amount, currency, EnumCurrency.CNY.getCode());
//    }
//}
