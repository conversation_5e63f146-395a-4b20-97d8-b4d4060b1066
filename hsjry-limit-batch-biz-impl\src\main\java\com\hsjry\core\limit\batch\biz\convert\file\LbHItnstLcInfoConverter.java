/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.convert.file;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.hsjry.core.limit.batch.biz.entity.LbHItnstLcInfoData;
import com.hsjry.core.limit.batch.dal.dao.model.LbHItnstLcInfoDo;

import lombok.extern.slf4j.Slf4j;

/**
 * 国结系统历史表信用证信息文件数据转换器
 * 提供高性能的数据转换功能，支持批量处理和内存优化
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/11 11:41
 */
@Slf4j
public class LbHItnstLcInfoConverter {

    /** 默认初始容量 */
    private static final int DEFAULT_INITIAL_CAPACITY = 16;
    /** 负载因子 */
    private static final float LOAD_FACTOR = 0.75f;

    /**
     * Data转DO（高性能版本）
     *
     * @param data 源数据对象
     * @return 目标DO对象
     */
    public static LbHItnstLcInfoDo data2Do(LbHItnstLcInfoData data) {
        return LbHItnstLcInfoCnvs.INSTANCE.data2Do(data);
    }

    /**
     * DO转Data（高性能版本）
     *
     * @param model DO对象
     * @return 目标Data对象
     */
    public static LbHItnstLcInfoData do2Data(LbHItnstLcInfoDo model) {
        return LbHItnstLcInfoCnvs.INSTANCE.do2Data(model);
    }

    /**
     * Data列表转DO列表（高性能版本）
     * 使用并行流和预估容量优化性能
     *
     * @param dataList 源数据列表
     * @return 目标DO列表
     */
    public static List<LbHItnstLcInfoDo> dataListToDoList(List<LbHItnstLcInfoData> dataList) {
        if (Objects.isNull(dataList)) {
            return Collections.emptyList();
        }

        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (dataList.size() / LOAD_FACTOR) + 1, DEFAULT_INITIAL_CAPACITY);
        // 使用并行流提升性能
        return dataList.parallelStream().filter(Objects::nonNull).map(LbHItnstLcInfoConverter::data2Do).collect(
            Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
    }
}