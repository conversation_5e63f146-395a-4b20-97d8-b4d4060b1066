package com.hsjry.core.limit.batch.biz.job.job.copy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * 额度实例金额信息数据备份同步处理任务
 *
 * <AUTHOR>
 * @date 2025-07-09 12:31:05
 */
@Slf4j
@Service("lbCLimitAmtInfoBakSyncJob")
public class LbCLimitAmtInfoBakSyncJob extends AbstractBaseBatchJob {
    
    public LbCLimitAmtInfoBakSyncJob() {
        log.info("LbCLimitAmtInfoBakSyncJob Bean 正在创建...");
    }
    
    @Autowired
    @Qualifier("lbCLimitAmtInfoBakSyncBizImpl")
    private BaseOrdinaryBiz baseOrdinaryBiz;

    @Override
    public BaseOrdinaryBiz getBaseOrdinaryBiz() {
        return baseOrdinaryBiz;
    }

    /**
     * 设置基础业务逻辑对象
     * 
     * @param baseOrdinaryBiz 基础业务逻辑对象
     */
    public void setBaseOrdinaryBiz(BaseOrdinaryBiz baseOrdinaryBiz) {
        this.baseOrdinaryBiz = baseOrdinaryBiz;
    }
} 