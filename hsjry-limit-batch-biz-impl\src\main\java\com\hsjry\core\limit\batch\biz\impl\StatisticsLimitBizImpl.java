package com.hsjry.core.limit.batch.biz.impl;

import java.math.BigDecimal;
// import java.util.ArrayList;
// import java.util.HashMap;
// import java.util.List;
// import java.util.Map;
// import java.util.Set;
// import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

// import com.google.common.collect.Lists;
// import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.biz.StatisticsLimitBiz;
import com.hsjry.core.limit.batch.common.constants.LimitBatchConstants;
// import com.hsjry.core.limit.center.dal.dao.intf.ProjectOrganLimitStatisticsDao;
// import com.hsjry.core.limit.center.dal.dao.model.ProjectOrganLimitStatisticsDo;
// import com.hsjry.core.limit.center.dal.dao.query.ProjectOrganLimitStatisticsQuery;
// import com.hsjry.lang.business.date.BusinessDateUtil;
// import com.hsjry.lang.common.utils.CollectionUtil;

/**
 * 机构统计额度
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0 2024/1/12 13:59
 */
@Service
public class StatisticsLimitBizImpl implements StatisticsLimitBiz {
    @Autowired
    private RedisTemplate<String, Long> redisTemplate;
    // @Autowired
    // private ProjectOrganLimitStatisticsDao projectOrganLimitStatisticsDao;

    /** 总额度节点编号 */
    @Value("${statistics.total.limit.node.id:DGLN0001}")
    private String totalLimitNodeId;
    /** 授信额度节点编号 */
    @Value("${statistics.credit.limit.node.id:DGLN0002}")
    private String creditLimitNodeId;
    /** 非授信节点编号 */
    @Value("${statistics.non.credit.limit.node.id:DGLN0003}")
    private String nonCreditLimitNodeId;
    /** 合作方节点编号 */
    @Value("${statistics.other.limit.node.id:DGLN0004}")
    private String otherLimitNodeId;

    @Override
    public void updateDataAndClear() {
        // TODO 切换HNNX分支后先注释报错代码
        // Map<String, ProjectOrganLimitStatisticsDo> dataMap = new HashMap<>();
        // List<String> redisKeyList = Lists.newArrayList();
        // // 从redis获取金额
        // paramHandle(LimitBatchConstants.STATISTICS_TOTAL_AMOUNT, dataMap, redisKeyList);
        // paramHandle(LimitBatchConstants.STATISTICS_AVAILABLE_AMOUNT, dataMap, redisKeyList);
        // paramHandle(LimitBatchConstants.STATISTICS_LOW_RISK_AMOUNT, dataMap, redisKeyList);
        // paramHandle(LimitBatchConstants.STATISTICS_AVAILABLE_LOW_RISK, dataMap, redisKeyList);
        // // 更新数据库
        // List<String> organIdList = new ArrayList<>(dataMap.keySet());
        // List<ProjectOrganLimitStatisticsDo> existOrganIdList = projectOrganLimitStatisticsDao.selectByExample(
        //     ProjectOrganLimitStatisticsQuery.builder().organIdList(organIdList).build());
        // Map<String, ProjectOrganLimitStatisticsDo> existOrganIdMap = new HashMap<>();
        // if (CollectionUtil.isNotEmpty(existOrganIdList)) {
        //     existOrganIdMap = existOrganIdList.stream().collect(
        //         Collectors.toMap(ProjectOrganLimitStatisticsDo::getOrganId, o -> o));
        // }
        // List<ProjectOrganLimitStatisticsDo> insertList = Lists.newArrayList();
        // List<ProjectOrganLimitStatisticsDo> updateList = Lists.newArrayList();
        // for (Map.Entry<String, ProjectOrganLimitStatisticsDo> statisticsDoEntry : dataMap.entrySet()) {
        //     ProjectOrganLimitStatisticsDo existOrgan = existOrganIdMap.get(statisticsDoEntry.getKey());
        //     if (null == existOrgan) {
        //         insertList.add(statisticsDoEntry.getValue());
        //     } else {
        //         updateList.add(statisticsDoEntry.getValue());
        //     }
        // }

        // projectOrganLimitStatisticsDao.insertList(insertList);
        // projectOrganLimitStatisticsDao.updateByPrimaryKeyList(updateList);
        // 清除缓存
        // redisTemplate.delete(redisKeyList);
    }

    /**
     * 参数处理
     *
     * @param amountKey
     * @param dataMap
     */
    // private void paramHandle(String amountKey, Map<String, ProjectOrganLimitStatisticsDo> dataMap,
    //     List<String> redisKeyList) {
    //     Map<String, BigDecimal> amountMap = new HashMap<>();
    //     redisKeyList.add(amountKey);
    //     Set<Object> hashKeyList = redisTemplate.opsForHash().keys(amountKey);
    //     for (Object hashKey : hashKeyList) {
    //         Long value = redisTemplate.opsForValue().increment((String) hashKey, 0L);
    //         BigDecimal valueBigDecimal = long2BigDecimal(value);
    //         amountMap.put((String) hashKey, valueBigDecimal);
    //         redisKeyList.add((String) hashKey);
    //     }
    //
    //     for (Map.Entry<String, BigDecimal> entry : amountMap.entrySet()) {
    //         String key = entry.getKey();
    //         String[] keyArray = key.split("_");
    //         String organId = keyArray[0];
    //         String nodeId = keyArray[1];
    //         BigDecimal amount = entry.getValue();
    //         ProjectOrganLimitStatisticsDo projectOrganLimitStatisticsDo = dataMap.get(organId);
    //         if (null == projectOrganLimitStatisticsDo) {
    //             projectOrganLimitStatisticsDo = new ProjectOrganLimitStatisticsDo();
    //             projectOrganLimitStatisticsDo.setOrganId(organId);
    //             projectOrganLimitStatisticsDo.setTenantId(AppParamUtil.getTenantId());
    //             projectOrganLimitStatisticsDo.setCreateTime(BusinessDateUtil.getDate());
    //         }
    //         projectOrganLimitStatisticsDo.setUpdateTime(BusinessDateUtil.getDate());
    //         if (totalLimitNodeId.equals(nodeId)) {
    //             if (LimitBatchConstants.STATISTICS_TOTAL_AMOUNT.equals(amountKey)) {
    //                 // 总额度额度
    //                 projectOrganLimitStatisticsDo.setTotalAmount(amount);
    //             } else if (LimitBatchConstants.STATISTICS_AVAILABLE_AMOUNT.equals(amountKey)) {
    //                 // 总额度可用
    //                 projectOrganLimitStatisticsDo.setTotalAvailableAmount(amount);
    //             }
    //         } else if (creditLimitNodeId.equals(nodeId)) {
    //             if (LimitBatchConstants.STATISTICS_TOTAL_AMOUNT.equals(amountKey)) {
    //                 // 授信额度总额度
    //                 projectOrganLimitStatisticsDo.setCreditTotalAmt(amount);
    //             } else if (LimitBatchConstants.STATISTICS_AVAILABLE_AMOUNT.equals(amountKey)) {
    //                 // 授信额度可用
    //                 projectOrganLimitStatisticsDo.setCreditTotalAvailableAmt(amount);
    //
    //             } else if (LimitBatchConstants.STATISTICS_LOW_RISK_AMOUNT.equals(amountKey)) {
    //                 // 授信额度低风险
    //                 projectOrganLimitStatisticsDo.setCreditLowRiskAmt(amount);
    //
    //             } else if (LimitBatchConstants.STATISTICS_AVAILABLE_LOW_RISK.equals(amountKey)) {
    //                 // 授信额度可用低风险
    //                 projectOrganLimitStatisticsDo.setCreditLowRiskAvailableAmt(amount);
    //
    //             }
    //         } else if (nonCreditLimitNodeId.equals(nodeId)) {
    //             if (LimitBatchConstants.STATISTICS_TOTAL_AMOUNT.equals(amountKey)) {
    //                 // 非授信额度总额度
    //                 projectOrganLimitStatisticsDo.setNonCreditTotalAmt(amount);
    //
    //             } else if (LimitBatchConstants.STATISTICS_AVAILABLE_AMOUNT.equals(amountKey)) {
    //                 // 非授信额度可用
    //                 projectOrganLimitStatisticsDo.setNonCreditTotalAvailableAmt(amount);
    //             }
    //         } else if (otherLimitNodeId.equals(nodeId)) {
    //             if (LimitBatchConstants.STATISTICS_TOTAL_AMOUNT.equals(amountKey)) {
    //                 // 合作方额度总额度
    //                 projectOrganLimitStatisticsDo.setOtherTotalAmt(amount);
    //
    //             } else if (LimitBatchConstants.STATISTICS_AVAILABLE_AMOUNT.equals(amountKey)) {
    //                 // 合作方额度可用
    //                 projectOrganLimitStatisticsDo.setOtherTotalAvailableAmt(amount);
    //
    //             }
    //         }
    //         dataMap.put(organId, projectOrganLimitStatisticsDo);
    //     }
    // }

    /**
     * long转bigdecimal /10000
     *
     * @param number
     * @return
     */
    public static BigDecimal long2BigDecimal(Long number) {
        if (null == number) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(number / LimitBatchConstants.TEN_THOUSAND_L);
    }

}
