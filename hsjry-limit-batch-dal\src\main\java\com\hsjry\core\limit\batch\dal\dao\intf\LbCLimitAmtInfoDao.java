package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbCLimitAmtInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbCLimitAmtInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度实例金额信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-03 10:03:12
 */
public interface LbCLimitAmtInfoDao extends IBaseDao<LbCLimitAmtInfoDo> {
    /**
     * 分页查询额度实例金额信息
     *
     * @param lbCLimitAmtInfoQuery 条件
     * @return PageInfo<LbCLimitAmtInfoDo>
     */
    PageInfo<LbCLimitAmtInfoDo> selectPage(LbCLimitAmtInfoQuery lbCLimitAmtInfoQuery, PageParam pageParam);

    /**
     * 根据key查询额度实例金额信息
     *
     * @param custLimitId
     * @return
     */
    LbCLimitAmtInfoDo selectByKey(String custLimitId);

    /**
     * 根据key删除额度实例金额信息
     *
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custLimitId);

    /**
     * 查询额度实例金额信息信息
     *
     * @param lbCLimitAmtInfoQuery 条件
     * @return List<LbCLimitAmtInfoDo>
     */
    List<LbCLimitAmtInfoDo> selectByExample(LbCLimitAmtInfoQuery lbCLimitAmtInfoQuery);

    /**
     * 新增额度实例金额信息信息
     *
     * @param lbCLimitAmtInfo 条件
     * @return int>
     */
    int insertBySelective(LbCLimitAmtInfoDo lbCLimitAmtInfo);

    /**
     * 修改额度实例金额信息信息
     *
     * @param lbCLimitAmtInfo
     * @return
     */
    int updateBySelective(LbCLimitAmtInfoDo lbCLimitAmtInfo);

    /**
     * 修改额度实例金额信息信息
     *
     * @param lbCLimitAmtInfo
     * @param lbCLimitAmtInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbCLimitAmtInfoDo lbCLimitAmtInfo, LbCLimitAmtInfoQuery lbCLimitAmtInfoQuery);

    /**
     * 批量插入额度实例金额信息
     *
     * @param lbCLimitAmtInfoList 批量数据
     * @return int
     */
    @Override
    int insertList(List<LbCLimitAmtInfoDo> lbCLimitAmtInfoList);

    /**
     * 清空额度实例金额信息所有数据
     *
     * @return int
     */
    int deleteAll();

    /**
     * 查询分片数据
     *
     * @param query 查询条件
     * @return List<LbCLimitAmtInfoDo>
     */
    List<LbCLimitAmtInfoDo> selectShardList(LbCLimitAmtInfoQuery query);

    /**
     * 获取第一个对象，limit m，1
     *
     * @param query 查询条件
     * @return LbCLimitAmtInfoDo
     */
    LbCLimitAmtInfoDo selectFirstOne(LbCLimitAmtInfoQuery query);

    /**
     * 获取当前组的数据量
     *
     * @param query 查询条件
     * @return Integer
     */
    Integer selectCountByCurrentGroup(LbCLimitAmtInfoQuery query);

    /**
     * 查询已存在的custLimitId列表
     *
     * @param custLimitIds 要检查的custLimitId列表
     * @return 已存在的custLimitId列表
     */
    List<String> selectExistCustLimitIds(List<String> custLimitIds);
}
