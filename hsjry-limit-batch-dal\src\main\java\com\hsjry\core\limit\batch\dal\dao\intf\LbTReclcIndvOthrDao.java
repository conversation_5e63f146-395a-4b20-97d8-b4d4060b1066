package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIndvOthrDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcIndvOthrQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中个人额度中其他额度数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-08-21 13:10:51
 */
public interface LbTReclcIndvOthrDao extends IBaseDao<LbTReclcIndvOthrDo> {
    /**
     * 分页查询额度中心-中间表-额度重算中个人额度中其他额度
     *
     * @param lbTReclcIndvOthrQuery 条件
     * @return PageInfo<LbTReclcIndvOthrDo>
     */
    PageInfo<LbTReclcIndvOthrDo> selectPage(LbTReclcIndvOthrQuery lbTReclcIndvOthrQuery, PageParam pageParam);

    /**
     * 根据key查询额度中心-中间表-额度重算中个人额度中其他额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    LbTReclcIndvOthrDo selectByKey(String custNo, String custLimitId);

    /**
     * 根据key删除额度中心-中间表-额度重算中个人额度中其他额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custNo, String custLimitId);

    /**
     * 查询额度中心-中间表-额度重算中个人额度中其他额度信息
     *
     * @param lbTReclcIndvOthrQuery 条件
     * @return List<LbTReclcIndvOthrDo>
     */
    List<LbTReclcIndvOthrDo> selectByExample(LbTReclcIndvOthrQuery lbTReclcIndvOthrQuery);

    /**
     * 新增额度中心-中间表-额度重算中个人额度中其他额度信息
     *
     * @param lbTReclcIndvOthr 条件
     * @return int>
     */
    int insertBySelective(LbTReclcIndvOthrDo lbTReclcIndvOthr);

    /**
     * 修改额度中心-中间表-额度重算中个人额度中其他额度信息
     *
     * @param lbTReclcIndvOthr
     * @return
     */
    int updateBySelective(LbTReclcIndvOthrDo lbTReclcIndvOthr);

    /**
     * 修改额度中心-中间表-额度重算中个人额度中其他额度信息
     *
     * @param lbTReclcIndvOthr
     * @param lbTReclcIndvOthrQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTReclcIndvOthrDo lbTReclcIndvOthr, LbTReclcIndvOthrQuery lbTReclcIndvOthrQuery);

    // ==================== 其他额度重算相关方法 ====================

    /**
     * 2.1.清空其他额度中间表
     */
    int truncateOtherLimit();

    /**
     * 2.2.插入其他额度客户编号和额度编号
     */
    int insertOtherLimit();

    /**
     * 2.3.更新其他额度中间表金额信息
     */
    int mergeOtherLimitAmount();

    /**
     * 2.4.更新额度实例金额信息
     */
    int mergeOtherLimitInstance();
}
