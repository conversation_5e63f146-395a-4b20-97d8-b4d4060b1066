/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.biz;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.hsjry.base.common.fs.service.FileProcessService;
import com.hsjry.base.common.fs.service.FileProcessServiceFactory;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobConstant;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.util.JobUtil;
import com.hsjry.base.common.utils.IpUtil;
import com.hsjry.core.limit.batch.biz.PushShardingPathService;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.center.dal.dao.intf.LcSliceBatchSerialDao;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.LcSliceBatchSerialQuery;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 信贷本地文件合并(单点)
 * 该节点每个分片都处理完成后，合并成以ip为结尾的一个文件
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/4/17 16:04
 */
@Slf4j
@Service
public class MergeCreditFileLocalBizImpl implements BaseOrdinaryBiz {
    @Autowired
    private LcSliceBatchSerialDao lcSliceBatchSerialDao;
    @Autowired
    private PushShardingPathService pushShardingPathService;
    @Autowired
    protected FileProcessServiceFactory serviceFactory;

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.MERGE_CREDIT_LOCAL_FILE;
    }

    @Override
    public String initBatchSerialNo(JobInitDto jobInitDto) {
        return String.join(JobConstant.BATCH_SEPARATOR, jobInitDto.getTenantId(),
            jobInitDto.getBusinessDate().toString(), JobUtil.parseInpara(jobInitDto.getInPara()).getJobTradeCode());
    }

    private String ip;

    @PostConstruct
    public void initIp() {
        if (StringUtil.isBlank(ip)) {
            ip = IpUtil.getIPAddress();
        }
    }

    @Override
    public void execBaseJob(JobInitDto jobInitDto) {
        //1.获取并 校验 分片流水信息,以备拼装分片文件名
        List<LcSliceBatchSerialDo> sliceBatchSerialDoList = this.checkSliceBatchSerialList(jobInitDto);
        if (CollectionUtil.isEmpty(sliceBatchSerialDoList)) {
            log.info("未查询到分片数据！，查询条件批次总流水为【{}】，ip地址为【{}】", jobInitDto.getBatchSerialNo(), ip);
            return;
        }
        log.info("jobInitDto={}", GsonUtil.obj2Json(jobInitDto));
        String tradeCode = JSONObject.parseObject(jobInitDto.getInPara()).getString("JOB_TRADE_CODE");
        EnumJobTrade enumJobTrade = EnumJobTrade.find(tradeCode);
        Integer businessDate = jobInitDto.getBusinessDate();
        //2.合并文件并返回绝对路径
        String localMergeFilePath = pushShardingPathService.mergeLocalFile(sliceBatchSerialDoList, enumJobTrade,
            businessDate, ip);
        FileProcessService instance = serviceFactory.getInstance();
        File file = new File(localMergeFilePath);
        if (file.exists()) {
            byte[] bFile = null;
            try {
                bFile = Files.readAllBytes(Paths.get(localMergeFilePath));
            } catch (IOException e) {
                log.error("文件合并后上传失败，path={},e={}", localMergeFilePath, e);
                throw new HsjryBizException("999999", "文件合并后上传失败");
            }
            //3.上传到sftp，后续节点会下载下来合并后最终上传
            String remoteTmpPath = pushShardingPathService.getRemoteTmpFilePath(enumJobTrade, businessDate);
            String remoteTmpFileName = pushShardingPathService.getShardingFileName(enumJobTrade, businessDate, ip);
            String absRemoteTmpPath = remoteTmpPath + File.separator + remoteTmpFileName;
            log.info("远程文件上传路径={}", absRemoteTmpPath);
            serviceFactory.getInstance().uploadFile(absRemoteTmpPath, bFile);
        }
    }

    private List<LcSliceBatchSerialDo> checkSliceBatchSerialList(JobInitDto jobInitDto) {
        //查询 出  本机ip地址 当前批次流水的所有分片数据
        LcSliceBatchSerialQuery lcSliceBatchSerialQuery = LcSliceBatchSerialQuery.builder().batchSerialNo(
            jobInitDto.getBatchSerialNo()).execIp(ip).build();
        return lcSliceBatchSerialDao.selectByExample(lcSliceBatchSerialQuery);
    }
}
