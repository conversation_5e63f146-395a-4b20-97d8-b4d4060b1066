package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIbnkTotlDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcIbnkTotlQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中同业客户中总额度数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-08-21 12:16:19
 */
public interface LbTReclcIbnkTotlDao extends IBaseDao<LbTReclcIbnkTotlDo> {
    /**
     * 分页查询额度中心-中间表-额度重算中同业客户中总额度
     *
     * @param lbTReclcIbnkTotlQuery 条件
     * @return PageInfo<LbTReclcIbnkTotlDo>
     */
    PageInfo<LbTReclcIbnkTotlDo> selectPage(LbTReclcIbnkTotlQuery lbTReclcIbnkTotlQuery, PageParam pageParam);

    /**
     * 根据key查询额度中心-中间表-额度重算中同业客户中总额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    LbTReclcIbnkTotlDo selectByKey(String custNo, String custLimitId);

    /**
     * 根据key删除额度中心-中间表-额度重算中同业客户中总额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custNo, String custLimitId);

    /**
     * 查询额度中心-中间表-额度重算中同业客户中总额度信息
     *
     * @param lbTReclcIbnkTotlQuery 条件
     * @return List<LbTReclcIbnkTotlDo>
     */
    List<LbTReclcIbnkTotlDo> selectByExample(LbTReclcIbnkTotlQuery lbTReclcIbnkTotlQuery);

    /**
     * 新增额度中心-中间表-额度重算中同业客户中总额度信息
     *
     * @param lbTReclcIbnkTotl 条件
     * @return int>
     */
    int insertBySelective(LbTReclcIbnkTotlDo lbTReclcIbnkTotl);

    /**
     * 修改额度中心-中间表-额度重算中同业客户中总额度信息
     *
     * @param lbTReclcIbnkTotl
     * @return
     */
    int updateBySelective(LbTReclcIbnkTotlDo lbTReclcIbnkTotl);

    /**
     * 修改额度中心-中间表-额度重算中同业客户中总额度信息
     *
     * @param lbTReclcIbnkTotl
     * @param lbTReclcIbnkTotlQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTReclcIbnkTotlDo lbTReclcIbnkTotl, LbTReclcIbnkTotlQuery lbTReclcIbnkTotlQuery);

    // ==================== 同业客户总额度重算相关方法 ====================

    /**
     * 7.1.清空同业客户总额度中间表
     */
    int truncateTotalLimit();

    /**
     * 7.2.插入同业客户总额度客户编号和额度编号
     */
    int insertTotalLimit();

    /**
     * 7.3.更新同业客户总额度中间表金额信息
     */
    int mergeTotalLimitAmount();

    /**
     * 7.4.更新额度实例金额信息
     */
    int mergeTotalLimitInstance();
}
