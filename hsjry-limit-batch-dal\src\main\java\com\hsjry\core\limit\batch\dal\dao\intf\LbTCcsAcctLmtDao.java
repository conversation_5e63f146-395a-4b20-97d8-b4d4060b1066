package com.hsjry.core.limit.batch.dal.dao.intf;

import com.hsjry.core.limit.batch.dal.dao.model.LbTCcsAcctLmtDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTCcsAcctLmtQuery;

import java.util.List;

import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 信用卡-中间表-信用卡额度信息（记录信用卡账户额度及余额信息）数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbTCcsAcctLmtDao extends IBaseDao<LbTCcsAcctLmtDo> {
    /**
     * 分页查询信用卡-中间表-信用卡额度信息（记录信用卡账户额度及余额信息）
     *
     * @param lbTCcsAcctLmtQuery 条件
     * @return PageInfo<LbTCcsAcctLmtDo>
     */
    PageInfo<LbTCcsAcctLmtDo> selectPage(LbTCcsAcctLmtQuery lbTCcsAcctLmtQuery, PageParam pageParam);

    /**
     * 根据key查询信用卡-中间表-信用卡额度信息（记录信用卡账户额度及余额信息）
     *
     * @param xaccount
     * @param bank
     * @return
     */
    LbTCcsAcctLmtDo selectByKey(Integer xaccount, Integer bank);

    /**
     * 根据key删除信用卡-中间表-信用卡额度信息（记录信用卡账户额度及余额信息）
     *
     * @param xaccount
     * @param bank
     * @return
     */
    int deleteByKey(Integer xaccount, Integer bank);

    /**
     * 查询信用卡-中间表-信用卡额度信息（记录信用卡账户额度及余额信息）信息
     *
     * @param lbTCcsAcctLmtQuery 条件
     * @return List<LbTCcsAcctLmtDo>
     */
    List<LbTCcsAcctLmtDo> selectByExample(LbTCcsAcctLmtQuery lbTCcsAcctLmtQuery);

    /**
     * 新增信用卡-中间表-信用卡额度信息（记录信用卡账户额度及余额信息）信息
     *
     * @param lbTCcsAcctLmt 条件
     * @return int>
     */
    int insertBySelective(LbTCcsAcctLmtDo lbTCcsAcctLmt);

    /**
     * 修改信用卡-中间表-信用卡额度信息（记录信用卡账户额度及余额信息）信息
     *
     * @param lbTCcsAcctLmt
     * @return
     */
    int updateBySelective(LbTCcsAcctLmtDo lbTCcsAcctLmt);

    /**
     * 修改信用卡-中间表-信用卡额度信息（记录信用卡账户额度及余额信息）信息
     *
     * @param lbTCcsAcctLmt
     * @param lbTCcsAcctLmtQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTCcsAcctLmtDo lbTCcsAcctLmt, LbTCcsAcctLmtQuery lbTCcsAcctLmtQuery);

    /**
     * 批量插入信用卡额度信息数据
     * 用于信用卡额度信息处理的大批量数据插入
     *
     * @param lbTCcsAcctLmtList 待插入的数据列表
     * @return 插入的记录数
     */
    int insertList(List<LbTCcsAcctLmtDo> lbTCcsAcctLmtList);

    /**
     * 清空信用卡额度信息表所有数据
     * 用于信用卡额度信息处理的全量数据刷新
     *
     * @return 删除的记录数
     */
    int deleteAll();
}
