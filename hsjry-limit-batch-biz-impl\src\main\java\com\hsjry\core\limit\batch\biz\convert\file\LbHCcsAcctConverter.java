/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.convert.file;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.hsjry.core.limit.batch.biz.entity.LbHCcsAcctData;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCcsAcctDo;

import lombok.extern.slf4j.Slf4j;

/**
 * 信用卡-历史表-第一币种贷记帐户转换器
 * 提供高性能的数据转换功能，支持批量处理和内存优化
 * 
 * 🎯 核心功能：
 * - 文件数据实体与数据库对象之间的高效转换
 * - 支持286字段的完整映射转换
 * - 批量处理优化，适用于大数据量场景
 * - 内存使用优化，预估容量避免动态扩容
 * 
 * ⚡ 性能特性：
 * - MapStruct底层支持，零反射开销
 * - 并行流处理，提升批量转换效率
 * - 预估初始容量，减少内存分配开销
 * - 空值安全处理，避免NPE异常
 * 
 * 📊 适用场景：
 * - 信用卡历史表文件同步作业
 * - 大批量数据导入导出
 * - 数据仓库ETL处理
 * - 系统间数据交换
 *
 * <AUTHOR>
 * @version V4.0.1
 * @since 4.0.1 2025/1/21
 */
@Slf4j
@Component
public class LbHCcsAcctConverter {

    /** 默认初始容量 */
    private static final int DEFAULT_INITIAL_CAPACITY = 16;
    /** 负载因子 */
    private static final float LOAD_FACTOR = 0.75f;
    /** 批处理大小 */
    private static final int BATCH_SIZE = 1000;

    /**
     * Data转DO（高性能版本）
     * 使用MapStruct进行快速转换
     *
     * @param data 源数据对象
     * @return 目标DO对象
     */
    public static LbHCcsAcctDo data2Do(LbHCcsAcctData data) {
        return LbHCcsAcctCnvs.INSTANCE.data2Do(data);
    }

    /**
     * DO转Data（高性能版本）
     * 使用MapStruct进行快速转换
     *
     * @param dataObject DO对象
     * @return 目标Data对象
     */
    public static LbHCcsAcctData do2Data(LbHCcsAcctDo dataObject) {
        return LbHCcsAcctCnvs.INSTANCE.do2Data(dataObject);
    }

    /**
     * Data列表转DO列表（高性能版本）
     * 使用并行流和预估容量优化性能
     * 
     * 🔧 优化策略：
     * - 空值检查避免不必要的处理
     * - 预估集合容量减少扩容开销
     * - 并行流处理提升转换效率
     * - 分批处理避免内存溢出
     *
     * @param dataList 源数据列表
     * @return 目标DO列表
     */
    public static List<LbHCcsAcctDo> dataListToDoList(List<LbHCcsAcctData> dataList) {
        if (Objects.isNull(dataList) || dataList.isEmpty()) {
            log.debug("输入数据列表为空，返回空列表");
            return Collections.emptyList();
        }

        log.debug("开始转换Data列表到DO列表，数据量: {}", dataList.size());
        
        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (dataList.size() / LOAD_FACTOR) + 1, DEFAULT_INITIAL_CAPACITY);
        
        try {
            List<LbHCcsAcctDo> result;
            
            // 根据数据量选择处理策略
            if (dataList.size() <= BATCH_SIZE) {
                // 小数据量使用MapStruct批量转换
                result = LbHCcsAcctCnvs.INSTANCE.dataListToDoList(dataList);
            } else {
                // 大数据量使用并行流转换
                result = dataList.parallelStream()
                    .filter(Objects::nonNull)
                    .map(LbHCcsAcctConverter::data2Do)
                    .collect(Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
            }
            
            log.debug("转换完成，结果数量: {}", result.size());
            return result;
            
        } catch (Exception e) {
            log.error("Data列表转DO列表时发生异常, 数据量: {}", dataList.size(), e);
            throw new RuntimeException("数据转换失败", e);
        }
    }

    /**
     * DO列表转Data列表（高性能版本）
     * 使用并行流和预估容量优化性能
     * 
     * 🔧 优化策略：
     * - 空值检查避免不必要的处理
     * - 预估集合容量减少扩容开销
     * - 并行流处理提升转换效率
     * - 异常处理确保系统稳定性
     *
     * @param doList DO列表
     * @return 目标Data列表
     */
    public static List<LbHCcsAcctData> doListToDataList(List<LbHCcsAcctDo> doList) {
        if (Objects.isNull(doList) || doList.isEmpty()) {
            log.debug("输入DO列表为空，返回空列表");
            return Collections.emptyList();
        }

        log.debug("开始转换DO列表到Data列表，数据量: {}", doList.size());
        
        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (doList.size() / LOAD_FACTOR) + 1, DEFAULT_INITIAL_CAPACITY);
        
        try {
            List<LbHCcsAcctData> result;
            
            // 根据数据量选择处理策略
            if (doList.size() <= BATCH_SIZE) {
                // 小数据量使用MapStruct批量转换
                result = LbHCcsAcctCnvs.INSTANCE.doListToDataList(doList);
            } else {
                // 大数据量使用并行流转换
                result = doList.parallelStream()
                    .filter(Objects::nonNull)
                    .map(LbHCcsAcctConverter::do2Data)
                    .collect(Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
            }
            
            log.debug("转换完成，结果数量: {}", result.size());
            return result;
            
        } catch (Exception e) {
            log.error("DO列表转Data列表时发生异常, 数据量: {}", doList.size(), e);
            throw new RuntimeException("数据转换失败", e);
        }
    }

    /**
     * 获取转换器性能统计信息
     * 用于监控和性能调优
     *
     * @return 性能统计信息
     */
    public static String getPerformanceInfo() {
        return String.format("LbHCcsAcctConverter性能配置 - 默认容量: %d, 负载因子: %.2f, 批处理大小: %d",
            DEFAULT_INITIAL_CAPACITY, LOAD_FACTOR, BATCH_SIZE);
    }
} 