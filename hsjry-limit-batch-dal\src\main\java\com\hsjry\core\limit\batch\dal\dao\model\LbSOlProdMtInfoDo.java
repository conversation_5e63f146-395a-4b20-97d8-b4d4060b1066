package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度产品信息Do
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
@Table(name = "lb_s_ol_prod_mt_info")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LbSOlProdMtInfoDo extends LbSOlProdMtInfoKeyDo implements Serializable {
    private static final long serialVersionUID = 1942415996337979395L;
    /** 冻结额度：当前被冻结不可用的额度金额 */
    @Column(name = "frozen_amount")
    private java.math.BigDecimal frozenAmount;
    /** 更新时间：记录最后修改时的系统时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 创建时间：记录创建时的系统时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 所属组织id：额度所属的组织或机构ID */
    @Column(name = "own_organ_id")
    private String ownOrganId;
    /** 操作人id：最后操作该记录的用户ID */
    @Column(name = "operator_id")
    private String operatorId;
    /** 生效结束时间：额度失效的日期时间 */
    @Column(name = "effective_end_time")
    private java.util.Date effectiveEndTime;
    /** 生效起始时间：额度开始生效的日期时间 */
    @Column(name = "effective_start_time")
    private java.util.Date effectiveStartTime;
    /** 支用次数限制：0表示无限制 */
    @Column(name = "loan_times_limit")
    private Integer loanTimesLimit;
    /** 已使用支用次数：-1表示未设置 */
    @Column(name = "use_loan_times")
    private Integer useLoanTimes;
    /** 已使用额度：历史累计使用的额度金额 */
    @Column(name = "used_amount")
    private java.math.BigDecimal usedAmount;
    /** 使用中额度：已使用但尚未还款的额度金额 */
    @Column(name = "using_amount")
    private java.math.BigDecimal usingAmount;
    /** 总额度：客户获得的最高可用额度金额 */
    @Column(name = "total_amount")
    private java.math.BigDecimal totalAmount;
    /** 状态：额度当前状态（正常/冻结/注销等） */
    @Column(name = "status")
    private String status;
    /** 额度类型：1-循环额度 2-非循环额度 */
    @Column(name = "credit_type")
    private String creditType;
    /** 产品名称：额度产品的展示名称 */
    @Column(name = "product_name")
    private String productName;
    /** 产品编号：关联产品信息的唯一标识 */
    @Column(name = "product_id")
    private String productId;
    /** 手机号码：客户的联系手机号 */
    @Column(name = "user_mobile")
    private String userMobile;
    /** 证件类型：标识证件类型（身份证/护照等） */
    @Column(name = "certificate_type")
    private String certificateType;
    /** 证件号码：客户的有效证件号码 */
    @Column(name = "certificate_no")
    private String certificateNo;
    /** 客户类型：标识客户类型（个人/企业等） */
    @Column(name = "user_type")
    private String userType;
    /** 客户姓名：客户的真实姓名 */
    @Column(name = "user_name")
    private String userName;
    /** 客户编号：关联客户信息的唯一标识 */
    @Column(name = "user_id")
    private String userId;
}
