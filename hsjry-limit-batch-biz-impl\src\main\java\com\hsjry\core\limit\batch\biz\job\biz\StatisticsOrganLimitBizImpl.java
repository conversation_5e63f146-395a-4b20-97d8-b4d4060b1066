/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.biz;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.core.limit.batch.biz.StatisticsLimitBiz;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;

import lombok.extern.slf4j.Slf4j;

/**
 * 机构统计额度
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0 2024/1/12 14:02
 */
@Service
@Slf4j
public class StatisticsOrganLimitBizImpl implements BaseOrdinaryBiz {

    @Autowired
    private StatisticsLimitBiz statisticsLimitBiz;

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.STATISTICS_ORGAN_LIMIT;
    }

    @Override
    public void execBaseJob(JobInitDto jobInitDto) {
        log.info("========执行机构统计额度批量开始========" + jobInitDto.getBusinessDate());
        statisticsLimitBiz.updateDataAndClear();
        log.info("========执行机构统计额度批量结束========" + jobInitDto.getBusinessDate());
    }
}
