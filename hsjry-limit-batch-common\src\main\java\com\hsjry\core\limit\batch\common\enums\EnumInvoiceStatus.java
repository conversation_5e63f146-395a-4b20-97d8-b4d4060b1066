package com.hsjry.core.limit.batch.common.enums;

import com.hsjry.lang.common.stereotype.enums.IEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 借据状态
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0 2024/1/23 16:15
 */
@Getter
@AllArgsConstructor
public enum EnumInvoiceStatus implements IEnum {
    /** 正常 */
    NORMAL("010", "正常"),
    /** 结清 */
    SETTLE("020", "结清"),
    ;

    /** 状态码 */
    private String code;

    /** 状态描述 */
    private String description;

    /**
     * 根据编码查找枚举
     *
     * @param code 编码
     * @return {@link EnumInvoiceStatus } 实例
     **/
    public static EnumInvoiceStatus find(String code) {
        for (EnumInvoiceStatus instance : EnumInvoiceStatus.values()) {
            if (instance.getCode()
                .equals(code)) {
                return instance;
            }
        }
        return null;
    }
}