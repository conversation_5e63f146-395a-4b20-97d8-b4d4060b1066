package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.core.limit.batch.dal.dao.intf.LbTReclcCorpCprsvCrdtDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTReclcCorpCprsvCrdtMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpCprsvCrdtDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpCprsvCrdtExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpCprsvCrdtKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcCorpCprsvCrdtQuery;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中对公客户中综合授信额度数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-08-21 11:31:28
 */
@Repository
public class LbTReclcCorpCprsvCrdtDaoImpl
    extends AbstractBaseDaoImpl<LbTReclcCorpCprsvCrdtDo, LbTReclcCorpCprsvCrdtMapper>
    implements LbTReclcCorpCprsvCrdtDao {
    /**
     * 分页查询
     *
     * @param lbTReclcCorpCprsvCrdt 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTReclcCorpCprsvCrdtDo> selectPage(LbTReclcCorpCprsvCrdtQuery lbTReclcCorpCprsvCrdt,
        PageParam pageParam) {
        LbTReclcCorpCprsvCrdtExample example = buildExample(lbTReclcCorpCprsvCrdt);
        return PageHelper.<LbTReclcCorpCprsvCrdtDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询额度中心-中间表-额度重算中对公客户中综合授信额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public LbTReclcCorpCprsvCrdtDo selectByKey(String custNo, String custLimitId) {
        LbTReclcCorpCprsvCrdtKeyDo lbTReclcCorpCprsvCrdtKeyDo = new LbTReclcCorpCprsvCrdtKeyDo();
        lbTReclcCorpCprsvCrdtKeyDo.setCustNo(custNo);
        lbTReclcCorpCprsvCrdtKeyDo.setCustLimitId(custLimitId);
        return getMapper().selectByPrimaryKey(lbTReclcCorpCprsvCrdtKeyDo);
    }

    /**
     * 根据key删除额度中心-中间表-额度重算中对公客户中综合授信额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public int deleteByKey(String custNo, String custLimitId) {
        LbTReclcCorpCprsvCrdtKeyDo lbTReclcCorpCprsvCrdtKeyDo = new LbTReclcCorpCprsvCrdtKeyDo();
        lbTReclcCorpCprsvCrdtKeyDo.setCustNo(custNo);
        lbTReclcCorpCprsvCrdtKeyDo.setCustLimitId(custLimitId);
        return getMapper().deleteByPrimaryKey(lbTReclcCorpCprsvCrdtKeyDo);
    }

    /**
     * 查询额度中心-中间表-额度重算中对公客户中综合授信额度信息
     *
     * @param lbTReclcCorpCprsvCrdt 条件
     * @return List<LbTReclcCorpCprsvCrdtDo>
     */
    @Override
    public List<LbTReclcCorpCprsvCrdtDo> selectByExample(LbTReclcCorpCprsvCrdtQuery lbTReclcCorpCprsvCrdt) {
        return getMapper().selectByExample(buildExample(lbTReclcCorpCprsvCrdt));
    }

    /**
     * 新增额度中心-中间表-额度重算中对公客户中综合授信额度信息
     *
     * @param lbTReclcCorpCprsvCrdt 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTReclcCorpCprsvCrdtDo lbTReclcCorpCprsvCrdt) {
        if (lbTReclcCorpCprsvCrdt == null) {
            return -1;
        }

        return getMapper().insertSelective(lbTReclcCorpCprsvCrdt);
    }

    /**
     * 修改额度中心-中间表-额度重算中对公客户中综合授信额度信息
     *
     * @param lbTReclcCorpCprsvCrdt
     * @return
     */
    @Override
    public int updateBySelective(LbTReclcCorpCprsvCrdtDo lbTReclcCorpCprsvCrdt) {
        if (lbTReclcCorpCprsvCrdt == null) {
            return -1;
        }
        return getMapper().updateByPrimaryKeySelective(lbTReclcCorpCprsvCrdt);
    }

    @Override
    public int updateBySelectiveByExample(LbTReclcCorpCprsvCrdtDo lbTReclcCorpCprsvCrdt,
        LbTReclcCorpCprsvCrdtQuery lbTReclcCorpCprsvCrdtQuery) {
        return getMapper().updateByExampleSelective(lbTReclcCorpCprsvCrdt, buildExample(lbTReclcCorpCprsvCrdtQuery));
    }

    /**
     * 构建额度中心-中间表-额度重算中对公客户中综合授信额度Example信息
     *
     * @param lbTReclcCorpCprsvCrdt
     * @return
     */
    public LbTReclcCorpCprsvCrdtExample buildExample(LbTReclcCorpCprsvCrdtQuery lbTReclcCorpCprsvCrdt) {
        LbTReclcCorpCprsvCrdtExample example = new LbTReclcCorpCprsvCrdtExample();
        LbTReclcCorpCprsvCrdtExample.Criteria criteria = example.createCriteria();
        if (lbTReclcCorpCprsvCrdt != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbTReclcCorpCprsvCrdt.getCustNo())) {
                criteria.andCustNoEqualTo(lbTReclcCorpCprsvCrdt.getCustNo());
            }
            if (StringUtil.isNotEmpty(lbTReclcCorpCprsvCrdt.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lbTReclcCorpCprsvCrdt.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lbTReclcCorpCprsvCrdt.getTemplateNodeId())) {
                criteria.andTemplateNodeIdEqualTo(lbTReclcCorpCprsvCrdt.getTemplateNodeId());
            }
            if (StringUtil.isNotEmpty(lbTReclcCorpCprsvCrdt.getLimitStatus())) {
                criteria.andLimitStatusEqualTo(lbTReclcCorpCprsvCrdt.getLimitStatus());
            }
            if (null != lbTReclcCorpCprsvCrdt.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbTReclcCorpCprsvCrdt.getTotalAmount());
            }
            if (null != lbTReclcCorpCprsvCrdt.getPreOccupyAmount()) {
                criteria.andPreOccupyAmountEqualTo(lbTReclcCorpCprsvCrdt.getPreOccupyAmount());
            }
            if (null != lbTReclcCorpCprsvCrdt.getRealOccupyAmount()) {
                criteria.andRealOccupyAmountEqualTo(lbTReclcCorpCprsvCrdt.getRealOccupyAmount());
            }
            if (null != lbTReclcCorpCprsvCrdt.getLowRiskAmount()) {
                criteria.andLowRiskAmountEqualTo(lbTReclcCorpCprsvCrdt.getLowRiskAmount());
            }
            if (null != lbTReclcCorpCprsvCrdt.getPreOccupyLowRiskAmt()) {
                criteria.andPreOccupyLowRiskAmtEqualTo(lbTReclcCorpCprsvCrdt.getPreOccupyLowRiskAmt());
            }
            if (null != lbTReclcCorpCprsvCrdt.getRealOccupyLowRiskAmt()) {
                criteria.andRealOccupyLowRiskAmtEqualTo(lbTReclcCorpCprsvCrdt.getRealOccupyLowRiskAmt());
            }
        }
        buildExampleExt(lbTReclcCorpCprsvCrdt, criteria);
        return example;
    }

    /**
     * 构建额度中心-中间表-额度重算中对公客户中综合授信额度ExampleExt方法
     *
     * @param lbTReclcCorpCprsvCrdt
     * @return
     */
    public void buildExampleExt(LbTReclcCorpCprsvCrdtQuery lbTReclcCorpCprsvCrdt,
        LbTReclcCorpCprsvCrdtExample.Criteria criteria) {

        //自定义实现
    }

    // ==================== 综合授信额度重算相关方法实现 ====================

    /**
     * 2.1.清空综合授信额度中间表
     */
    @Override
    public int truncateComprehensiveCreditLimit() {
        return getMapper().truncateComprehensiveCreditLimit();
    }

    /**
     * 2.2.插入综合授信额度客户编号和额度编号
     */
    @Override
    public int insertComprehensiveCreditLimit() {
        return getMapper().insertComprehensiveCreditLimit();
    }

    /**
     * 2.3.更新综合授信额度中间表金额信息
     */
    @Override
    public int mergeComprehensiveCreditLimitAmount() {
        return getMapper().mergeComprehensiveCreditLimitAmount();
    }

    /**
     * 2.4.更新额度实例金额信息
     */
    @Override
    public int mergeComprehensiveCreditLimitInstance() {
        return getMapper().mergeComprehensiveCreditLimitInstance();
    }

}
