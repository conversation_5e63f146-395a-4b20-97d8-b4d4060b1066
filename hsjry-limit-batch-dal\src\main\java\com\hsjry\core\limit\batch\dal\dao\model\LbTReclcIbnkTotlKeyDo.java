package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度中心-中间表-额度重算中同业客户中总额度主键
 *
 * <AUTHOR>
 * @date 2025-08-21 12:16:19
 */
@Table(name = "lb_t_reclc_ibnk_totl")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LbTReclcIbnkTotlKeyDo implements Serializable {

    private static final long serialVersionUID = 1958503429970264065L;
    /** 客户编号 */
    @Id
    @Column(name = "cust_no")
    private String custNo;
    /** 额度编号 */
    @Id
    @Column(name = "cust_limit_id")
    private String custLimitId;
}