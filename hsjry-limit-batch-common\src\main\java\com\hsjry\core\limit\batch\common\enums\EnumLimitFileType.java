package com.hsjry.core.limit.batch.common.enums;

import com.hsjry.base.common.fs.enums.EnumFileSuffix;
import com.hsjry.base.common.job.dto.IEnumFileType;
import com.hsjry.lang.common.stereotype.enums.IEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/4/12 16:04
 */
@Getter
@AllArgsConstructor
public enum EnumLimitFileType implements IEnumFileType {
    /** 额度对账文件 */
    LIMIT_RECONCILE_FILE("LIMIT_RECONCILE_FILE", "额度对账文件", "yyyyMMdd_LIMIT_RECONCILE_FILE", EnumFileSuffix.CSV),
    ;

    /** 状态码 */
    private String code;
    /** 描述 */
    private String name;
    /** 文件名 */
    private String fileName;
    /** 文件后缀 */
    private EnumFileSuffix fileSuffix;

    /**
     * 根据编码查找枚举
     *
     * @param code 编码
     * @return {@link EnumLimitFileType } 实例
     **/
    public static EnumLimitFileType find(String code) {
        for (EnumLimitFileType instance : EnumLimitFileType.values()) {
            if (instance.getCode()
                .equals(code)) {
                return instance;
            }
        }
        return null;
    }

    @Override
    public String getFileName(String date) {
        return null;
    }
}
