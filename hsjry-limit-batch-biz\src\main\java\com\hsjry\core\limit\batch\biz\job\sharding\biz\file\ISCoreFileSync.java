package com.hsjry.core.limit.batch.biz.job.sharding.biz.file;

import com.hsjry.core.limit.batch.biz.job.entity.SharedBo;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;

/**
 * 核心产品文件同步接口
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/11/7 10:27
 */
public interface ISCoreFileSync {

    /**
     * 获取作业交易类型
     *
     * @return 作业交易类型
     */
    EnumJobTrade getJobTrade();

    /**
     * 准备并执行文件同步
     *
     * @param sharedBo 共享业务对象
     */
    void prepare(SharedBo sharedBo);
}
