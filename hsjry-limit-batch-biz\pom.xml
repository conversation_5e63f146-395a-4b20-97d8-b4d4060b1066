<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hsjry-limit-batch</artifactId>
        <groupId>com.hsjry.core</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hsjry-limit-batch-biz</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.hsjry.base.common</groupId>
            <artifactId>job</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hsjry.core</groupId>
            <artifactId>hsjry-limit-batch-dal</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hsjry.core</groupId>
            <artifactId>hsjry-limit-batch-facade</artifactId>
        </dependency>
    </dependencies>
</project>
