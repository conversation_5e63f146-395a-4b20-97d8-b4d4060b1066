package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.core.limit.batch.dal.dao.intf.LbTReclcIbnkTotlDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTReclcIbnkTotlMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIbnkTotlDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIbnkTotlExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIbnkTotlKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcIbnkTotlQuery;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中同业客户中总额度数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-08-21 12:16:19
 */
@Repository
public class LbTReclcIbnkTotlDaoImpl extends AbstractBaseDaoImpl<LbTReclcIbnkTotlDo, LbTReclcIbnkTotlMapper>
    implements LbTReclcIbnkTotlDao {
    /**
     * 分页查询
     *
     * @param lbTReclcIbnkTotl 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTReclcIbnkTotlDo> selectPage(LbTReclcIbnkTotlQuery lbTReclcIbnkTotl, PageParam pageParam) {
        LbTReclcIbnkTotlExample example = buildExample(lbTReclcIbnkTotl);
        return PageHelper.<LbTReclcIbnkTotlDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询额度中心-中间表-额度重算中同业客户中总额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public LbTReclcIbnkTotlDo selectByKey(String custNo, String custLimitId) {
        LbTReclcIbnkTotlKeyDo lbTReclcIbnkTotlKeyDo = new LbTReclcIbnkTotlKeyDo();
        lbTReclcIbnkTotlKeyDo.setCustNo(custNo);
        lbTReclcIbnkTotlKeyDo.setCustLimitId(custLimitId);
        return getMapper().selectByPrimaryKey(lbTReclcIbnkTotlKeyDo);
    }

    /**
     * 根据key删除额度中心-中间表-额度重算中同业客户中总额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public int deleteByKey(String custNo, String custLimitId) {
        LbTReclcIbnkTotlKeyDo lbTReclcIbnkTotlKeyDo = new LbTReclcIbnkTotlKeyDo();
        lbTReclcIbnkTotlKeyDo.setCustNo(custNo);
        lbTReclcIbnkTotlKeyDo.setCustLimitId(custLimitId);
        return getMapper().deleteByPrimaryKey(lbTReclcIbnkTotlKeyDo);
    }

    /**
     * 查询额度中心-中间表-额度重算中同业客户中总额度信息
     *
     * @param lbTReclcIbnkTotl 条件
     * @return List<LbTReclcIbnkTotlDo>
     */
    @Override
    public List<LbTReclcIbnkTotlDo> selectByExample(LbTReclcIbnkTotlQuery lbTReclcIbnkTotl) {
        return getMapper().selectByExample(buildExample(lbTReclcIbnkTotl));
    }

    /**
     * 新增额度中心-中间表-额度重算中同业客户中总额度信息
     *
     * @param lbTReclcIbnkTotl 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTReclcIbnkTotlDo lbTReclcIbnkTotl) {
        if (lbTReclcIbnkTotl == null) {
            return -1;
        }

        return getMapper().insertSelective(lbTReclcIbnkTotl);
    }

    /**
     * 修改额度中心-中间表-额度重算中同业客户中总额度信息
     *
     * @param lbTReclcIbnkTotl
     * @return
     */
    @Override
    public int updateBySelective(LbTReclcIbnkTotlDo lbTReclcIbnkTotl) {
        if (lbTReclcIbnkTotl == null) {
            return -1;
        }

        return getMapper().updateByPrimaryKeySelective(lbTReclcIbnkTotl);
    }

    @Override
    public int updateBySelectiveByExample(LbTReclcIbnkTotlDo lbTReclcIbnkTotl,
        LbTReclcIbnkTotlQuery lbTReclcIbnkTotlQuery) {
        return getMapper().updateByExampleSelective(lbTReclcIbnkTotl, buildExample(lbTReclcIbnkTotlQuery));
    }

    /**
     * 构建额度中心-中间表-额度重算中同业客户中总额度Example信息
     *
     * @param lbTReclcIbnkTotl
     * @return
     */
    public LbTReclcIbnkTotlExample buildExample(LbTReclcIbnkTotlQuery lbTReclcIbnkTotl) {
        LbTReclcIbnkTotlExample example = new LbTReclcIbnkTotlExample();
        LbTReclcIbnkTotlExample.Criteria criteria = example.createCriteria();
        if (lbTReclcIbnkTotl != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbTReclcIbnkTotl.getCustNo())) {
                criteria.andCustNoEqualTo(lbTReclcIbnkTotl.getCustNo());
            }
            if (StringUtil.isNotEmpty(lbTReclcIbnkTotl.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lbTReclcIbnkTotl.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lbTReclcIbnkTotl.getTemplateNodeId())) {
                criteria.andTemplateNodeIdEqualTo(lbTReclcIbnkTotl.getTemplateNodeId());
            }
            if (StringUtil.isNotEmpty(lbTReclcIbnkTotl.getLimitStatus())) {
                criteria.andLimitStatusEqualTo(lbTReclcIbnkTotl.getLimitStatus());
            }
            if (null != lbTReclcIbnkTotl.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbTReclcIbnkTotl.getTotalAmount());
            }
            if (null != lbTReclcIbnkTotl.getPreOccupyAmount()) {
                criteria.andPreOccupyAmountEqualTo(lbTReclcIbnkTotl.getPreOccupyAmount());
            }
            if (null != lbTReclcIbnkTotl.getRealOccupyAmount()) {
                criteria.andRealOccupyAmountEqualTo(lbTReclcIbnkTotl.getRealOccupyAmount());
            }
            if (null != lbTReclcIbnkTotl.getLowRiskAmount()) {
                criteria.andLowRiskAmountEqualTo(lbTReclcIbnkTotl.getLowRiskAmount());
            }
            if (null != lbTReclcIbnkTotl.getPreOccupyLowRiskAmt()) {
                criteria.andPreOccupyLowRiskAmtEqualTo(lbTReclcIbnkTotl.getPreOccupyLowRiskAmt());
            }
            if (null != lbTReclcIbnkTotl.getRealOccupyLowRiskAmt()) {
                criteria.andRealOccupyLowRiskAmtEqualTo(lbTReclcIbnkTotl.getRealOccupyLowRiskAmt());
            }
            if (StringUtil.isNotEmpty(lbTReclcIbnkTotl.getBlngLglpsnCoreInsNo())) {
                criteria.andBlngLglpsnCoreInsNoEqualTo(lbTReclcIbnkTotl.getBlngLglpsnCoreInsNo());
            }
        }
        buildExampleExt(lbTReclcIbnkTotl, criteria);
        return example;
    }

    /**
     * 构建额度中心-中间表-额度重算中同业客户中总额度ExampleExt方法
     *
     * @param lbTReclcIbnkTotl
     * @return
     */
    public void buildExampleExt(LbTReclcIbnkTotlQuery lbTReclcIbnkTotl, LbTReclcIbnkTotlExample.Criteria criteria) {

        //自定义实现
    }

    // ==================== 同业客户总额度重算相关方法实现 ====================

    /**
     * 7.1.清空同业客户总额度中间表
     */
    @Override
    public int truncateTotalLimit() {
        return getMapper().truncateTotalLimit();
    }

    /**
     * 7.2.插入同业客户总额度客户编号和额度编号
     */
    @Override
    public int insertTotalLimit() {
        return getMapper().insertTotalLimit();
    }

    /**
     * 7.3.更新同业客户总额度中间表金额信息
     */
    @Override
    public int mergeTotalLimitAmount() {
        return getMapper().mergeTotalLimitAmount();
    }

    /**
     * 7.4.更新额度实例金额信息
     */
    @Override
    public int mergeTotalLimitInstance() {
        return getMapper().mergeTotalLimitInstance();
    }

}
