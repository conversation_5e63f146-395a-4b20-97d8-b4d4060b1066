package com.hsjry.core.limit.batch.biz.job.sharding.biz.amtlimit;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSONObject;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.limit.EnumLimitEntityStatus;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.limit.AbstractShardingPrepareBiz;
import com.hsjry.core.limit.batch.common.constants.LimitBatchConstants;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.common.enums.EnumLimitBatchErrorCode;
import com.hsjry.core.limit.center.dal.dao.intf.EntityInfoBatchDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcAmtLimitRuleDao;
import com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitRuleDo;
import com.hsjry.core.limit.center.dal.dao.model.LcEntityInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.EntityInfoBatchQuery;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.stereotype.enums.EnumBool;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.extern.slf4j.Slf4j;
/**
 * 限额存量数据重算
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0 2023/3/22 14:21
 */
@Service
@Slf4j
public class AmtLimitStockCalImpl extends AbstractShardingPrepareBiz<EntityInfoBatchQuery>
    implements JobCoreBusiness<LcEntityInfoDo> {
    @Autowired
    private EntityInfoBatchDao entityInfoBatchDao;
    @Autowired
    private LcAmtLimitRuleDao lcAmtLimitRuleDao;
    // @Autowired
    // private IBusinessEntityCore iBusinessEntityCore;
    // @Autowired
    // private IAmtLimitRuleCore iAmtLimitRuleCore;
    // @Autowired
    // private IAmtLimitServiceCore iAmtLimitServiceCore;

    @Override
    public Integer selectCountByCurrentGroupFromDb(EntityInfoBatchQuery query) {
        return entityInfoBatchDao.selectCountByCurrentGroup(query);
    }

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.AMT_LIMIT_STOCK_CAL;
    }

    @Override
    public ShardingResult<LcEntityInfoDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        ShardingResult<LcEntityInfoDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }
        String ruleId = JSONObject.parseObject(jobInitDto.getInPara())
            .getString(LimitBatchConstants.RULE_ID);
        if (StringUtil.isBlank(ruleId)) {
            throw new HsjryBizException(EnumLimitBatchErrorCode.NO_AMT_LIMIT_RULE_STOCK.getCode(),
                EnumLimitBatchErrorCode.NO_AMT_LIMIT_RULE_STOCK.getDescription(), ruleId);
        }
        LcAmtLimitRuleDo lcAmtLimitRuleDo = lcAmtLimitRuleDao.selectByKey(ruleId);
        if (null == lcAmtLimitRuleDo) {
            throw new HsjryBizException(EnumLimitBatchErrorCode.AMT_LIMIT_RULE_NOT_EXIST.getCode(),
                EnumLimitBatchErrorCode.AMT_LIMIT_RULE_NOT_EXIST.getDescription(), ruleId);
        }
        //原始查询条件
        EntityInfoBatchQuery entityInfoBatchQuery = GsonUtil.json2Obj(jobShared.getExtParam(),
            EntityInfoBatchQuery.class);
        EntityInfoBatchQuery query = EntityInfoBatchQuery.builder()
            .offset(jobShared.getOffset())
            .limit(jobShared.getLimit())
            .status(entityInfoBatchQuery.getStatus())
            .build();
        log.info("=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");
        List<LcEntityInfoDo> list = entityInfoBatchDao.selectShardList(query);
        shardingResult.setShardingResultList(list);
        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LcEntityInfoDo> shardingResult) {
        List<LcEntityInfoDo> lcEntityInfoDoList = shardingResult.getShardingResultList();
        String ruleId = JSONObject.parseObject(shardingResult.getJobShared()
            .getInPara())
            .getString(LimitBatchConstants.RULE_ID);

        if (CollectionUtils.isEmpty(lcEntityInfoDoList)) {
            // 更新同步存量业务标记为是
            LcAmtLimitRuleDo lcAmtLimitRuleDo = new LcAmtLimitRuleDo();
            lcAmtLimitRuleDo.setRuleId(ruleId);
            lcAmtLimitRuleDo.setStockBusinessFlag(EnumBool.YES.getCode());
            lcAmtLimitRuleDao.updateBySelective(lcAmtLimitRuleDo);
            log.info("=========分片执行结束:" + shardingResult.getJobShared()
                .getBatchNum() + "数量为空=========");
            return;
        }
        handle(lcEntityInfoDoList, ruleId);

    }

    /**
     * 额度处理
     *
     * @param lcEntityInfoDoList
     */
    private void handle(List<LcEntityInfoDo> lcEntityInfoDoList, String ruleId) {
        for (LcEntityInfoDo lcEntityInfoDo : lcEntityInfoDoList) {
            // // 获取实体信息
            // BusinessEntityBo businessEntityBo = iBusinessEntityCore.locateEntity(lcEntityInfoDo.getEntityApplyId(),
            //     lcEntityInfoDo.getEntityRelationId(), lcEntityInfoDo.getSystemSign());
            // //查询流水
            // iBusinessEntityCore.supplementSuccessSerial(businessEntityBo);
            // businessEntityBo.setFirstTimeFlag(true);
            // //获取命中规则
            // List<AmtLimitRuleBo> limitRuleBoList = iAmtLimitRuleCore.hitRule(businessEntityBo);
            // if(CollectionUtil.isEmpty(limitRuleBoList)){
            //     continue;
            // }
            // List<AmtLimitRuleBo> amtLimitRuleBoList = limitRuleBoList.stream()
            //     .filter(o -> o.getRuleId()
            //         .equals(ruleId))
            //     .collect(Collectors.toList());
            // if(CollectionUtil.isEmpty(amtLimitRuleBoList)){
            //     continue;
            // }
            // AmtLimitRuleBo amtLimitRuleBo = amtLimitRuleBoList.get(0);
            // List<CheckResultDto> checkResultDtoList = iAmtLimitServiceCore.reTry(businessEntityBo, amtLimitRuleBo);
            // if(CollectionUtil.isNotEmpty(checkResultDtoList)){
            //     List<CheckResultDto> failList = checkResultDtoList.stream()
            //         .filter(checkResultDto -> !checkResultDto.getCheckResult())
            //         .collect(Collectors.toList());
            //     if (CollectionUtil.isNotEmpty(failList)) {
            //         throw new HsjryBizException(EnumLimitBatchErrorCode.AMT_LIMIT_STOCK_CAL_FAIL.getCode(),
            //             StringUtil.join(failList.stream()
            //                 .map(CheckResultDto::getErrorMsg)
            //                 .collect(Collectors.toList()), SystemCharConstant.COMMA), this.getClass());
            //     }
            // }
        }
    }


    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        log.info("====================== 接入业务{}分片逻辑 start ================================================",
            getJobTrade().getDescription());
        List<JobShared> jobSharedList = new ArrayList<>();
        //sql 批处理数量 暂定为分片数量，不放大
        Integer batchFixNum = jobInitDto.getFixNum();
        //当前分组的最大值，为下次 批处理的最小值
        LcEntityInfoDo maxLimitInfoDo = new LcEntityInfoDo();
        //构造查询条件 查询当前 分批处理的 排序 最大 对象
        EntityInfoBatchQuery query = EntityInfoBatchQuery.builder()
            .status(EnumLimitEntityStatus.UN_SETTLE.getCode())
            .offset(batchFixNum - 1)
            .limit(1)
            .build();
        //分片流水
        int batchNum = 0;
        while (maxLimitInfoDo != null) {
            query.setEntityId(maxLimitInfoDo.getEntityId());
            maxLimitInfoDo = entityInfoBatchDao.selectFirstOne(query);
            //统计分片 数量
            batchNum = countBatchNum(batchFixNum, query, maxLimitInfoDo, batchNum, jobInitDto, jobSharedList,
                query.getEntityId(), false);
        }
        log.info("====================== 接入业务{}分片逻辑 end ================================================",
            getJobTrade().getDescription());
        return jobSharedList;
    }
}
