/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.job.file;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * 网贷系统-历史表-合同信息文件的同步处理任务
 * 负责H_OL_CTR_INFO文件的批量同步处理
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/22 16:00
 */
@Slf4j
@Service("lbHOlCtrInfoSyncJob")
public class LbHOlCtrInfoSyncJob extends AbstractBaseBatchJob {
    
    public LbHOlCtrInfoSyncJob() {
        log.info("LbHOlCtrInfoSyncJob Bean初始化完成");
    }

    @Autowired
    @Qualifier("lbHOlCtrInfoSyncBizImpl")
    private BaseOrdinaryBiz lbHOlCtrInfoSyncBizImpl;

    @Override
    public BaseOrdinaryBiz getBaseOrdinaryBiz() {
        return lbHOlCtrInfoSyncBizImpl;
    }
} 