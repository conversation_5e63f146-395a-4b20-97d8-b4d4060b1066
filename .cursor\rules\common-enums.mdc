---
description: 项目中使用的枚举类型和常量定义指南
alwaysApply: true
---
# 常用枚举和常量

## 枚举类型

项目中定义了多种枚举类型，位于 `hsjry-limit-batch-common/src/main/java/com/hsjry/core/limit/batch/common/enums/` 目录下：

### 核心业务枚举
- [EnumJobTrade.java](mdc:hsjry-limit-batch-common/src/main/java/com/hsjry/core/limit/batch/common/enums/EnumJobTrade.java) - 作业交易类型枚举（6.0KB，135行）
- [EnumCoreCurrency.java](mdc:hsjry-limit-batch-common/src/main/java/com/hsjry/core/limit/batch/common/enums/EnumCoreCurrency.java) - 核心货币类型枚举
- [EnumCalObject.java](mdc:hsjry-limit-batch-common/src/main/java/com/hsjry/core/limit/batch/common/enums/EnumCalObject.java) - 计算对象枚举

### 错误处理枚举
- [EnumBatchJobError.java](mdc:hsjry-limit-batch-common/src/main/java/com/hsjry/core/limit/batch/common/enums/EnumBatchJobError.java) - 批处理作业错误枚举
- [EnumLimitBatchErrorCode.java](mdc:hsjry-limit-batch-common/src/main/java/com/hsjry/core/limit/batch/common/enums/EnumLimitBatchErrorCode.java) - 限额批处理错误代码枚举（2.9KB，72行）

### 文件和发票相关枚举
- [EnumLimitFileType.java](mdc:hsjry-limit-batch-common/src/main/java/com/hsjry/core/limit/batch/common/enums/EnumLimitFileType.java) - 限额文件类型枚举
- [EnumInvoiceStatus.java](mdc:hsjry-limit-batch-common/src/main/java/com/hsjry/core/limit/batch/common/enums/EnumInvoiceStatus.java) - 发票状态枚举
- [EnumInvoiceOperateType.java](mdc:hsjry-limit-batch-common/src/main/java/com/hsjry/core/limit/batch/common/enums/EnumInvoiceOperateType.java) - 发票操作类型枚举
- [EnumInvoiceOperateStatus.java](mdc:hsjry-limit-batch-common/src/main/java/com/hsjry/core/limit/batch/common/enums/EnumInvoiceOperateStatus.java) - 发票操作状态枚举
- [EnumInvoiceCurrency.java](mdc:hsjry-limit-batch-common/src/main/java/com/hsjry/core/limit/batch/common/enums/EnumInvoiceCurrency.java) - 发票货币枚举

### 其他枚举
- [EnumSerialModalName.java](mdc:hsjry-limit-batch-common/src/main/java/com/hsjry/core/limit/batch/common/enums/EnumSerialModalName.java) - 序列模态名称枚举

## 常量类

### 核心常量
[LimitBatchConstants.java](mdc:hsjry-limit-batch-common/src/main/java/com/hsjry/core/limit/batch/common/constants/LimitBatchConstants.java) - 包含批处理相关的常量：
- `AMT_LIMIT_DETAIL_SHARD_NUM = 10` - 限额明细分片数量
- `RULE_ID` - 规则编号
- `STATISTICS_TOTAL_AMOUNT` - 总额度
- `STATISTICS_AVAILABLE_AMOUNT` - 可用总额度
- `STATISTICS_LOW_RISK_AMOUNT` - 低风险额度
- `STATISTICS_AVAILABLE_LOW_RISK` - 可用低风险
- `TEN_THOUSAND` - BigDecimal 类型的 10000
- `TEN_THOUSAND_L` - Long 类型的 10000L

### 其他常量
- [CharsetConstants.java](mdc:hsjry-limit-batch-common/src/main/java/com/hsjry/core/limit/batch/common/constants/CharsetConstants.java) - 字符集常量
- [LimitProductIdConstants.java](mdc:hsjry-limit-batch-common/src/main/java/com/hsjry/core/limit/batch/common/constants/LimitProductIdConstants.java) - 限额产品ID常量

