package com.hsjry.core.limit.batch.biz.convert.file;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.hsjry.core.limit.batch.biz.entity.LbHCorePprodData;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCorePprodDo;

import lombok.extern.slf4j.Slf4j;

/**
 * 核心系统产品定义表转换器
 * 提供高性能的数据转换功能，支持批量处理和内存优化
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/21 10:30
 */
@Slf4j
@Component
public class LbHCorePprodConverter {

    /** 默认初始容量 */
    private static final int DEFAULT_INITIAL_CAPACITY = 16;
    /** 负载因子 */
    private static final float LOAD_FACTOR = 0.75f;
    /** 批处理大小 */
    private static final int BATCH_SIZE = 1000;

    /**
     * Data转DO（高性能版本）
     *
     * @param data 源数据对象
     * @return 目标DO对象
     */
    public static LbHCorePprodDo data2Do(LbHCorePprodData data) {
        return LbHCorePprodCnvs.INSTANCE.data2Do(data);
    }

    /**
     * DO转Data（高性能版本）
     *
     * @param dataObject DO对象
     * @return 目标Data对象
     */
    public static LbHCorePprodData do2Data(LbHCorePprodDo dataObject) {
        return LbHCorePprodCnvs.INSTANCE.do2Data(dataObject);
    }

    /**
     * Data列表转DO列表（高性能版本）
     * 使用并行流和预估容量优化性能
     *
     * @param dataList 源数据列表
     * @return 目标DO列表
     */
    public List<LbHCorePprodDo> dataListToDoList(List<LbHCorePprodData> dataList) {
        if (Objects.isNull(dataList)) {
            return Collections.emptyList();
        }
        
        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (dataList.size() / LOAD_FACTOR) + 1, DEFAULT_INITIAL_CAPACITY);
        // 使用并行流提升性能
        return dataList.parallelStream().filter(Objects::nonNull).map(LbHCorePprodConverter::data2Do).collect(
            Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
    }
} 