package com.hsjry.core.limit.batch.dal.dao.mapper;

import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpCoPrtnDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 额度中心-中间表-额度重算中对公客户中合作方额度mapper
 *
 * <AUTHOR>
 * @date 2025-08-21 11:31:28
 */
public interface LbTReclcCorpCoPrtnMapper extends CommonMapper<LbTReclcCorpCoPrtnDo> {

    // ==================== 合作方额度重算相关方法 ====================

    /**
     * 5.1.清空合作方额度中间表
     */
    int truncateCoPartnerLimit();

    /**
     * 5.2.插入合作方额度客户编号和额度编号
     */
    int insertCoPartnerLimit();

    /**
     * 5.3.更新合作方额度中间表金额信息
     */
    int mergeCoPartnerLimitAmount();

    /**
     * 5.4.更新额度实例金额信息
     */
    int mergeCoPartnerLimitInstance();
}