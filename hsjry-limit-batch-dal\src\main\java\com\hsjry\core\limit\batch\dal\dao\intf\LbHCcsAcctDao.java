package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbHCcsAcctDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbHCcsAcctQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 信用卡-历史表-第一币种贷记帐户数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 03:51:17
 */
public interface LbHCcsAcctDao extends IBaseDao<LbHCcsAcctDo> {
    /**
     * 分页查询信用卡-历史表-第一币种贷记帐户
     *
     * @param lbHCcsAcctQuery 条件
     * @return PageInfo<LbHCcsAcctDo>
     */
    PageInfo<LbHCcsAcctDo> selectPage(LbHCcsAcctQuery lbHCcsAcctQuery, PageParam pageParam);

    /**
     * 根据key查询信用卡-历史表-第一币种贷记帐户
     *
     * @param xaccount
     * @param bank
     * @param dataDate
     * @return
     */
    LbHCcsAcctDo selectByKey(Integer xaccount, Integer bank, String dataDate);

    /**
     * 根据key删除信用卡-历史表-第一币种贷记帐户
     *
     * @param xaccount
     * @param bank
     * @param dataDate
     * @return
     */
    int deleteByKey(Integer xaccount, Integer bank, String dataDate);

    /**
     * 查询信用卡-历史表-第一币种贷记帐户信息
     *
     * @param lbHCcsAcctQuery 条件
     * @return List<LbHCcsAcctDo>
     */
    List<LbHCcsAcctDo> selectByExample(LbHCcsAcctQuery lbHCcsAcctQuery);

    /**
     * 新增信用卡-历史表-第一币种贷记帐户信息
     *
     * @param lbHCcsAcct 条件
     * @return int>
     */
    int insertBySelective(LbHCcsAcctDo lbHCcsAcct);

    /**
     * 修改信用卡-历史表-第一币种贷记帐户信息
     *
     * @param lbHCcsAcct
     * @return
     */
    int updateBySelective(LbHCcsAcctDo lbHCcsAcct);

    /**
     * 修改信用卡-历史表-第一币种贷记帐户信息
     *
     * @param lbHCcsAcct
     * @param lbHCcsAcctQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbHCcsAcctDo lbHCcsAcct, LbHCcsAcctQuery lbHCcsAcctQuery);

    /**
     * 清空信用卡历史表所有数据
     *
     * @return int
     */
    int deleteAll();

    int deleteByDataDate(String dataDate);

    /**
     * 批量插入信用卡历史表数据
     *
     * @param lbHCcsAcctList 待插入的数据列表
     * @return 插入的记录数
     */
    int insertList(List<LbHCcsAcctDo> lbHCcsAcctList);
}
