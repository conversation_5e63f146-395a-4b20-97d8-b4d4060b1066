/*
 * yunrong.cn Inc. Copyright (c) 2014-2019 All Rights Reserved
 */

package com.hsjry.core.limit.batch.core.impl.slice;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.dto.StatisticsData;
import com.hsjry.base.common.model.enums.limit.EnumLimitHandlerStatus;
import com.hsjry.base.common.utils.NumberUtils;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.utils.ExceptionUtil;
import com.hsjry.core.limit.batch.core.slice.SliceBatchSerialCore;
import com.hsjry.core.limit.center.dal.dao.intf.LcSliceBatchSerialDao;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.LcSliceBatchSerialQuery;
import com.hsjry.lang.common.exception.JobBizException;
import com.hsjry.lang.common.stereotype.enums.EnumBool;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version V3.0
 * @since 3.0.1 2020/10/17 16:01
 */
@Service
@Slf4j
public class SliceBatchSerialCoreImpl implements SliceBatchSerialCore {

    @Autowired
    private LcSliceBatchSerialDao lcSliceBatchSerialDao;

    /**
     * 更新或插入 分片流水
     * 校验主键是否存在 批次总流水 批次号
     * 若数据库中存在记录，进行更新操作，若不存在 进行插入操作
     *
     * @param lcSliceBatchSerialDo 分片流水信息
     */
    @Override
    public void updateSuccessSliceBatchSerial(LcSliceBatchSerialDo lcSliceBatchSerialDo) {
        //字段校验
        ExceptionUtil.nullBizException(lcSliceBatchSerialDo, "分片流水信息", this.getClass());
        ExceptionUtil.stringBlankBizException(lcSliceBatchSerialDo.getBatchSerialNo(), "批次总流水", this.getClass());
        ExceptionUtil.nullBizException(lcSliceBatchSerialDo.getBatchNum(), "批次号", this.getClass());
        log.info("开始更新分片任务成功，批次总流水：{}，批次号：{}", lcSliceBatchSerialDo.getBatchSerialNo(),
            lcSliceBatchSerialDo.getBatchNum());
        log.info("开始更新分片任务成功，成功条数：{}", lcSliceBatchSerialDo.getSharedPassCount());
        if (lcSliceBatchSerialDo.getSharedStatus()
            .equals(EnumLimitHandlerStatus.SUCCESS.getCode())) {
            log.error("分片流水记录状态异常！");
            throw new JobBizException(EnumBatchJobError.SHARDING_SERIAL_STATUS_ERROR.getCode(),
                EnumBatchJobError.SHARDING_SERIAL_STATUS_ERROR.getDescription(), getClass());
        }
        //只有当状态 为不成功的时候 才要更新 失败状态
        if (!EnumLimitHandlerStatus.SUCCESS.getCode()
            .equals(lcSliceBatchSerialDo.getSharedStatus())) {
            lcSliceBatchSerialDo.setSharedStatus(EnumLimitHandlerStatus.SUCCESS.getCode());
            lcSliceBatchSerialDao.updateBySelective(lcSliceBatchSerialDo);
        }
    }

    @Override
    public void updateFailSliceBatchSerial(LcSliceBatchSerialDo lcSliceBatchSerialDo) {
        //字段校验
        ExceptionUtil.nullBizException(lcSliceBatchSerialDo, "分片流水信息", this.getClass());
        ExceptionUtil.stringBlankBizException(lcSliceBatchSerialDo.getBatchSerialNo(), "批次总流水", this.getClass());
        ExceptionUtil.nullBizException(lcSliceBatchSerialDo.getBatchNum(), "批次号", this.getClass());
        log.info("开始更新分片任务失败，批次总流水：{}，批次号：{}", lcSliceBatchSerialDo.getBatchSerialNo(),
            lcSliceBatchSerialDo.getBatchNum());
        log.info("开始更新分片任务失败，失败条数：{}", lcSliceBatchSerialDo.getSharedPassCount());
        if (lcSliceBatchSerialDo.getSharedStatus()
            .equals(EnumLimitHandlerStatus.FAIL.getCode())) {
            log.error("分片流水记录状态异常！");
            return;
        }
        //只有当状态 不为失败的时候 才要更新 失败状态
        if (!EnumLimitHandlerStatus.FAIL.getCode()
            .equals(lcSliceBatchSerialDo.getSharedStatus())) {
            lcSliceBatchSerialDo.setSharedStatus(EnumLimitHandlerStatus.FAIL.getCode());
            lcSliceBatchSerialDao.updateBySelective(lcSliceBatchSerialDo);
        }
    }

    @Override
    public StatisticsData statisticsDataByPage(String batchSerialNo, Integer batchPageSize) {
        //初始化 统计项
        StatisticsData statisticsDataBo = this.initStatisticsDataBo();

        LcSliceBatchSerialQuery lcSliceBatchSerialQuery = LcSliceBatchSerialQuery.builder()
            .batchSerialNo(batchSerialNo)
            .build();
        int startPageNum = 1;
        PageParam pageParam = PageParam.Builder.getInstance()
            .addPageNum(startPageNum)
            .addPageSize(batchPageSize)
            .build();
        boolean hasNextPage;
        do {
            PageInfo<LcSliceBatchSerialDo> lcSliceBatchSerialDoPageInfo = lcSliceBatchSerialDao.selectPage(
                lcSliceBatchSerialQuery, pageParam);
            //检查分页数据
            checkPageData(lcSliceBatchSerialQuery, lcSliceBatchSerialDoPageInfo);
            List<LcSliceBatchSerialDo> assetSliceBatchSerialDoList = lcSliceBatchSerialDoPageInfo.getList();
            //统计 amount 数据
            statisticsData(statisticsDataBo, assetSliceBatchSerialDoList);
            //下次分页 条件
            hasNextPage = lcSliceBatchSerialDoPageInfo.isHasNextPage();
            pageParam.setPageNum(++startPageNum);
        } while (hasNextPage);
        return statisticsDataBo;
    }

    /**
     * 初始化 统计项
     *
     * @return 统计项
     */
    @NonNull
    private StatisticsData initStatisticsDataBo() {
        //IP统计项
        Set<String> execIpSet = new HashSet<>();
        //资产发生额核对文件 统计内容
        Map<String, BigDecimal> sumMap = new TreeMap<>();
        StatisticsData statisticsDataBo = new StatisticsData();
        statisticsDataBo.setStatisticsData(sumMap);
        statisticsDataBo.setExecIpSet(execIpSet);
        statisticsDataBo.setSuccessDataCount(0);
        return statisticsDataBo;
    }

    /**
     * 检查分页数据 若未查询到数据 异常处理
     *
     * @param lcSliceBatchSerialQuery 分片流水查询条件
     * @param lcSliceBatchSerialDoPageInfo 分片数据信息
     */
    private void checkPageData(LcSliceBatchSerialQuery lcSliceBatchSerialQuery,
        PageInfo<LcSliceBatchSerialDo> lcSliceBatchSerialDoPageInfo) {
        if (lcSliceBatchSerialDoPageInfo == null || lcSliceBatchSerialDoPageInfo.getTotal() < 1) {
            log.info("未查询到分片数据！，查询条件批次总流水为【{}】,{}", lcSliceBatchSerialQuery.getBatchSerialNo(),
                lcSliceBatchSerialQuery.getBatchNum());
            throw new JobBizException(EnumBatchJobError.SHARDING_INFO_ERROR.getCode(),
                EnumBatchJobError.SHARDING_INFO_ERROR.getDescription(), getClass());
        }
    }

    /**
     * 多个分片流水 统计 amount 数据
     *
     * @param statisticsDataBo 统计内容
     * @param assetSliceBatchSerialDoList 待统计数据
     */
    private void statisticsData(final StatisticsData statisticsDataBo,
        List<LcSliceBatchSerialDo> assetSliceBatchSerialDoList) {
        if (CollectionUtil.isNotEmpty(assetSliceBatchSerialDoList)) {
            assetSliceBatchSerialDoList.forEach(assetSliceBatchSerialDo -> {
                //统计每一分片的数据
                statisticsSliceShardingDate(statisticsDataBo, assetSliceBatchSerialDo);
            });
        }
    }

    /**
     * 统计每一分片的数据
     * 校验数据状态
     *
     * @param statisticsDataBo 统计数据
     * @param assetSliceBatchSerialDo 单个分片流水数据
     */
    private void statisticsSliceShardingDate(StatisticsData statisticsDataBo,
        LcSliceBatchSerialDo assetSliceBatchSerialDo) {
        //校验每一条分片流水的状态
        if (!EnumLimitHandlerStatus.SUCCESS.getCode()
            .equals(assetSliceBatchSerialDo.getSharedStatus())) {
            log.info("线上文件汇总统计时校验分片流水数据状态异常！，分片流水为{}", GsonUtil.obj2Json(assetSliceBatchSerialDo));
            throw new JobBizException(EnumBatchJobError.SHARDING_INFO_ERROR.getCode(),
                EnumBatchJobError.SHARDING_INFO_ERROR.getDescription(), getClass());
        }
        //日终时，发生额文件中，对账数据的统计项，汇总
        statisticsSliceSharding(statisticsDataBo.getStatisticsData(), assetSliceBatchSerialDo);
        //统计 execIp
        statisticsDataBo.getExecIpSet()
            .add(assetSliceBatchSerialDo.getExecIp());
        //当前任务批次处理成功的数据量
        setSuccessDataCount(statisticsDataBo, assetSliceBatchSerialDo);
    }

    /**
     * 单个分片 数据累计统计
     *
     * @param sumMap 原始统计项
     * @param assetSliceBatchSerialDo 分片数据
     */
    private void statisticsSliceSharding(final Map<String, BigDecimal> sumMap,
        LcSliceBatchSerialDo assetSliceBatchSerialDo) {
        String json = assetSliceBatchSerialDo.getSharedStatistics();
        StatisticsData statisticsDataBo = GsonUtil.json2Obj(json, StatisticsData.class);
        Optional.ofNullable(statisticsDataBo)
            .ifPresent(bo -> bo.getStatisticsData()
                .forEach((k, v) -> {
                    putSumMap(sumMap, k, v);
                }));
    }

    /**
     * 统计项累加 若存在，叠加重新赋值
     *
     * @param sumMap 原始统计项
     * @param key 统计项key
     * @param value 统计汇总
     */
    private void putSumMap(final Map<String, BigDecimal> sumMap, String key, BigDecimal value) {
        if (sumMap.get(key) == null) {
            sumMap.put(key, value);
        } else {
            sumMap.put(key, sumMap.get(key)
                .add(value));
        }
    }

    /**
     * （累加）统计 数据分片实际成功处理的条数 总量
     *
     * @param statisticsDataBo 批次流水分片统计
     * @param assetSliceBatchSerialDo 分片流水
     */
    private void setSuccessDataCount(final StatisticsData statisticsDataBo,
        LcSliceBatchSerialDo assetSliceBatchSerialDo) {
        int number = NumberUtils.getIntValue(statisticsDataBo.getSuccessDataCount()) + NumberUtils.getIntValue(
            assetSliceBatchSerialDo.getSharedPassCount());
        statisticsDataBo.setSuccessDataCount(number);
    }

    @Override
    public void updateSliceSerialFinishFlag(LcSliceBatchSerialDo creditSliceBatchSerialDo, EnumBool finishFlag) {
        LcSliceBatchSerialDo updateDo = new LcSliceBatchSerialDo();
        updateDo.setTenantId(creditSliceBatchSerialDo.getTenantId());
        updateDo.setBatchNum(creditSliceBatchSerialDo.getBatchNum());
        updateDo.setBatchSerialNo(creditSliceBatchSerialDo.getBatchSerialNo());
        updateDo.setFinishFlag(finishFlag.getCode());
        lcSliceBatchSerialDao.updateByPrimaryKeySelective(updateDo);
    }
}
