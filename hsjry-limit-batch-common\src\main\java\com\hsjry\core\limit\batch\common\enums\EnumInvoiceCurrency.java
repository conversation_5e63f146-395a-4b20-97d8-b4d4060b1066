package com.hsjry.core.limit.batch.common.enums;

import com.hsjry.lang.common.stereotype.enums.IEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 借据币种映射
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0 2024/3/19 15:24
 */
@Getter
@AllArgsConstructor
public enum EnumInvoiceCurrency implements IEnum {
    /** 人民币 */
    CNY("01", "CNY","人民币"),
    /** 英镑 */
    GBP("02", "GBP","英镑"),
    /** 港币 */
    HKD("03", "HKD","港币"),
    /** 美元 */
    USD("04", "USD","美元"),
    /** 日元 */
    JPY("10", "JPY","日元"),
    /** 欧元 */
    EUR("14", "EUR","欧元"),
    ;

    /** 状态码 */
    private String code;

    /** 目标码 */
    private String targetCode;

    /** 状态描述 */
    private String description;

    /**
     * 根据编码查找枚举
     *
     * @param code 编码
     * @return {@link EnumInvoiceCurrency } 实例
     **/
    public static EnumInvoiceCurrency find(String code) {
        for (EnumInvoiceCurrency instance : EnumInvoiceCurrency.values()) {
            if (instance.getCode()
                .equals(code)) {
                return instance;
            }
        }
        return null;
    }
}