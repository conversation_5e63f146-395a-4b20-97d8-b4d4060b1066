package com.hsjry.core.limit.batch.common.dto.file;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * [信用卡-落地表-第一币种贷记帐户]Dto
 * 
 * 🏆 完整的285个字段定义，与LB_S_CCS_ACCT表结构完全一致
 * 
 * 📋 更新历史：
 * - V4.0.0: 初始版本，仅包含25个主要字段
 * - V4.0.1: 完整升级，支持全部285个字段，确保数据完整性
 * 
 * 🔧 技术特性：
 * - 字段完整性：285/285 (100% 覆盖)
 * - 类型映射：与DO类字段类型完全一致
 * - 性能优化：针对大字段量场景优化内存使用
 * - 数据验证：支持完整的业务规则验证
 * 
 * 📊 主要字段分类：
 * - 主键字段：xaccount, bank
 * - 账户基本信息：business, category, custrNbr等 (12个)
 * - 逾期金额字段：age1-age6 (6个)
 * - 授权相关字段：authCash, authOver等 (5个)
 * - 余额相关字段：balFree, balInt等 (6个)
 * - 自扣还款账号：bankacct1-4, bankcode1-4 (8个)
 * - 分行和费用：branch, cardFees等 (6个)
 * - 预借现金字段：cash1st, cashAdvce等 (5个)
 * - 客户和账户状态：classCode, closeCode等 (12个)
 * - 周期相关字段：cycleNew, cyclePrv等 (6个)
 * - 历史最高金额：hiCredit, hiDebit等 (12个)
 * - 利息相关字段：intCash, intRate等 (14个)
 * - 分期付款字段：mpLimit, mpBal等 (9个)
 * - 交易笔数字段：nbrCashad-nbrTrans (7个)
 * - 积分相关字段：pointAdj, pointCum等 (9个)
 * - 对账单相关：stmBalnce, stmMindue等 (19个)
 * - 产品相关字段：prodLevel, prodNbr等 (5个)
 * - 临时额度字段：tempLimit, tlmtBeg等 (5个)
 * - 不计息余额细分：balNint01-10, stmNint01-10 (20个)
 * - 扩展字段：currNum2, wrofFlag等 (其余字段)
 * 
 * 🎯 使用场景：
 * - 信用卡账户文件数据传输
 * - 批量数据处理和转换
 * - 系统间数据同步
 * - 数据仓库ETL处理
 *
 * <AUTHOR>
 * @version V4.0.1
 * @since 2023/11/7 15:39
 */
@Data
@NoArgsConstructor
public class LbSCcsAcctDto implements Serializable {
    private static final long serialVersionUID = -8985151400912122688L;

    // 主键字段
    /** 账号 */
    private Integer xaccount;
    /** 银行 */
    private Integer bank;
    
    // 基本信息字段
    /** 公司编号（公司卡/商务卡使用） */
    private String business;
    /** 帐户类别 */
    private Integer category;
    /** 帐户拥有者证件号码 */
    private String custrNbr;
    /** 帐单日 */
    private Integer cycleNbr;
    /** 开户日期 */
    private Integer dayOpened;
    /** 帐户名称 */
    private String accName1;
    /** 帐户年费/管理费代码 */
    private String accType;
    /** 最近地址修改日期 */
    private Integer addChgday;
    /** 帐单地址类型 */
    private String addrType;
    /** 第二帐单地址类型 */
    private String addrType2;
    
    // 逾期金额字段
    /** 逾期金额1 */
    private BigDecimal age1;
    /** 逾期金额2 */
    private BigDecimal age2;
    /** 逾期金额3 */
    private BigDecimal age3;
    /** 逾期金额4 */
    private BigDecimal age4;
    /** 逾期金额5 */
    private BigDecimal age5;
    /** 逾期金额6 */
    private BigDecimal age6;
    
    // 授权相关字段
    /** 发卡专案代码 */
    private String appSource;
    /** 本期预授权的预借现金总额（没有用） */
    private BigDecimal authCash;
    /** 超额授权允许的百分比 */
    private BigDecimal authOver;
    /** 帐户超额授权允许开关 */
    private String authovYn;
    /** 授权未请款金额（用此字段） */
    private BigDecimal authsAmt;
    
    // 余额相关字段
    /** 复利余额 */
    private BigDecimal balCmpint;
    /** 消费余额（未出账单组成） */
    private BigDecimal balFree;
    /** 日记息余额（未出账单组成） */
    private BigDecimal balInt;
    /** 日记息余额符号 */
    private String balIntflag;
    /** 不记息余额（未出账单组成） */
    private BigDecimal balNoint;
    /** 利息余额（未出账单组成） */
    private BigDecimal balOrint;
    
    // 自扣还款账号字段
    /** 自扣还款账号1 */
    private String bankacct1;
    /** 自扣还款账号关联代码1 */
    private String bankcode1;
    /** 自扣还款账号2 */
    private String bankacct2;
    /** 自扣还款账号关联代码2 */
    private String bankcode2;
    /** 自扣还款账号3 */
    private String bankacct3;
    /** 自扣还款账号关联代码3 */
    private String bankcode3;
    /** 自扣还款账号4 */
    private String bankacct4;
    /** 自扣还款账号关联代码4 */
    private String bankcode4;
    
    // 分行和费用字段
    /** 分行 */
    private Integer branch;
    /** 最近修改分行日期 */
    private Integer branchDay;
    /** 预借现金比例 */
    private BigDecimal caPcnt;
    /** 年费 */
    private BigDecimal cardFees;
    /** 已挂失卡片数量 */
    private Integer cardsCanc;
    /** 帐户关联卡片数量 */
    private Integer cardsIssd;
    
    // 预借现金字段
    /** 第一次交易为预借现金日期 */
    private Integer cash1st;
    /** 预借现金手续费 */
    private BigDecimal cashAdfee;
    /** 预借现金金额 */
    private BigDecimal cashAdvce;
    /** 今日预借现金金额 */
    private BigDecimal cashTday;
    /** 第一次预借现金日期 */
    private Integer cash1stac;
    
    // 客户和账户状态字段
    /** 客户类别更改日期 */
    private Integer classChdy;
    /** 客户类别 */
    private String classCode;
    /** 最近关账日期 */
    private Integer closeChdy;
    /** 帐户状态（关帐代码） */
    private String closeCode;
    /** 账户进入COLL队列日期 */
    private Integer collsDay;
    /** 信用处理日期（暂无用） */
    private Integer crdactDay;
    /** 下次信用处理日期（暂无用） */
    private Integer crdnxtDay;
    /** 信用有效金额（暂无用） */
    private BigDecimal credActiv;
    /** 信用调整金额（暂无用） */
    private BigDecimal credAdj;
    /** 最近信用额度修改日期 */
    private Integer credChday;
    /** 信用额度 */
    private Integer credLimit;
    /** 信用凭证金额（暂无用） */
    private BigDecimal credVouch;
    /** 信用额度（暂无用） */
    private BigDecimal credlimX;
    /** 货币代码 */
    private Integer currNum;
    /** 最近截止日期修改日期 */
    private Integer cutoffDay;
    
    // 周期相关字段
    /** 帐单日修改次数 */
    private Integer cyChgcnt;
    /** 最近帐单日修改日期 */
    private Integer cyChgday;
    /** 新帐单日生效日期 */
    private Integer cyEffday;
    /** 本年度帐单日修改次数 */
    private Integer cyYrcnt;
    /** 新帐单日 */
    private Integer cycleNew;
    /** 前次帐单日 */
    private Integer cyclePrv;
    
    // 调整和费用字段
    /** 调借金额（没有用） */
    private BigDecimal debitAdj;
    /** 拒付处理日期 */
    private Integer dishnrday;
    /** 应付责任贷项余额（没有用） */
    private BigDecimal dutyCredt;
    /** 应付责任借项余额（没有用） */
    private BigDecimal dutyDebit;
    
    // 汇率相关字段
    /** 汇率代码 */
    private String exchCode;
    /** 汇率标志 */
    private String exchFlag;
    /** 汇率百分比 */
    private BigDecimal exchPerc;
    /** 汇率处理日期 */
    private Integer exchRtdt;
    /** 月费 */
    private Integer feeMonth;
    /** 费用及稅款合计 */
    private BigDecimal feesTaxes;
    /** 固定兑换金额 */
    private BigDecimal fixExamt;
    /** 担保标志 */
    private String guarnFlag;
    
    // 历史最高金额字段
    /** 历史最高预借现金金额 */
    private BigDecimal hiCashadv;
    /** 历史最高预借现金金额月份 */
    private Integer hiCasmmyy;
    /** 历史最高信用金额月份 */
    private Integer hiCrdmmyy;
    /** 历史最高信用金额 */
    private BigDecimal hiCredit;
    /** 历史最高借记金额 */
    private BigDecimal hiDebit;
    /** 历史最高借记金额月份 */
    private Integer hiDebmmyy;
    /** 历史最高分期付款金额 */
    private BigDecimal hiMpPur;
    /** 历史最高分期付款金额月份 */
    private Integer hiMpmmyy;
    /** 历史最高超限金额 */
    private BigDecimal hiOlimit;
    /** 历史最高超限金额月份 */
    private Integer hiOlimmyy;
    /** 历史最高消费金额 */
    private BigDecimal hiPurchse;
    /** 历史最高消费金额月份 */
    private Integer hiPurmmyy;
    
    // 利息相关字段
    /** 预借现金利息率 */
    private BigDecimal intCash;
    /** 复利利息处理次数（暂无用） */
    private Integer intChdcmp;
    /** 最近利息修改日期 */
    private Integer intChdy;
    /** 应收利息 */
    private BigDecimal intChgd;
    /** 复利金额 */
    private BigDecimal intCmpond;
    /** 利率代码 */
    private Integer intCode;
    /** 现有不计息金额（暂无用） */
    private BigDecimal intCunot;
    /** 现有复利金额（暂无用） */
    private BigDecimal intCurcmp;
    /** 已赚得利息（暂无用） */
    private BigDecimal intEarned;
    /** 应计利息（暂无用） */
    private BigDecimal intNotion;
    /** 利息处理日期 */
    private Integer intProcdy;
    /** 当前利率 */
    private BigDecimal intRate;
    /** 信贷利率 */
    private BigDecimal intRatecr;
    /** 利息截止日期 */
    private Integer intUptody;
    
    // 语言和交易字段
    /** 语言代码 */
    private String langCode;
    /** 最近交易日期 */
    private Integer lastTrday;
    /** 最近一次授权日期 */
    private Integer lastauthdy;
    /** 最近一次爭议款产生日期 */
    private Integer lastntdate;
    /** 最近一次缴款金额 */
    private BigDecimal lastpayamt;
    /** 最近一次缴款日期 */
    private Integer lastpayday;
    /** 转销戶的帐戶余额 */
    private BigDecimal losses;
    
    // 月份和监控字段
    /** 当前帐单月份 */
    private Integer monthNbr;
    /** 最近一次监控代码修改日期 */
    private Integer montrChdy;
    /** 监控代码 */
    private String montrCode;
    
    // 分期付款字段
    /** 今日分期付款金额 */
    private BigDecimal mpAmTdy;
    /** 分期付款授权总额 */
    private BigDecimal mpAuths;
    /** 分期付款目前余额 */
    private BigDecimal mpBal;
    /** 分期付款本期分摊金额 */
    private BigDecimal mpBilAmt;
    /** 分期付款信用额度 */
    private Integer mpLimit;
    /** 分期付款当日发生笔数 */
    private Integer mpNoTdy;
    /** 分期付款目前剩余本金 */
    private BigDecimal mpRemPpl;
    /** 分期付款额度修改日期 */
    private Integer mplmChday;
    /** 当前逾期期数 */
    private Integer mthsOdue;
    
    // 交易笔数字段
    /** 本次帐期的预借现金笔数 */
    private Integer nbrCashad;
    /** 本次帐期的费用及稅款笔数 */
    private Integer nbrFeedty;
    /** 本次帐期帐戶已超额後的交易次数 */
    private Integer nbrOlimit;
    /** 本次帐期的其他费用笔数 */
    private Integer nbrOthers;
    /** 本次帐期的缴款笔数 */
    private Integer nbrPaymnt;
    /** 本次帐期的一般消费笔数 */
    private Integer nbrPurch;
    /** 本次帐期的所有交易笔数 */
    private Integer nbrTrans;
    
    // OCT和逾期字段
    /** 进入OCT队列次数 */
    private Integer octCount;
    /** 进入OCT队列的日期 */
    private Integer octDayin;
    /** 贷款逾期状态标志 */
    private Integer odueFlag;
    /** 已逾期但未达到逾期最小金额的金额 */
    private BigDecimal odueHeld;
    /** 当期超限状态标志 */
    private Integer olflag;
    /** 其它费用总额 */
    private BigDecimal otherFees;
    
    // 还款相关字段
    /** 已全额还款标志 */
    private String payFlag;
    /** 第一次缴款的标示 */
    private String pay1stInd;
    /** 当期已还款金额 */
    private BigDecimal paymtClrd;
    /** 当日还款金额 */
    private BigDecimal paymtTday;
    /** 未出帐单的还款在途金额（暂无用） */
    private BigDecimal paymtUncl;
    /** 已收滞纳金金额 */
    private BigDecimal penChrg;
    /** 应收滞纳金金额 */
    private BigDecimal penchgAcc;
    
    // 积分相关字段
    /** 积分调整 */
    private Integer pointAdj;
    /** 积分调整符号 */
    private String ptAdjflag;
    /** 积分换礼次数 */
    private Integer pointClm;
    /** 累计积分 */
    private Integer pointCum;
    /** 累计积分符号 */
    private String ptCumflag;
    /** 累计积分2 */
    private Integer pointCum2;
    /** 赚取积分 */
    private Integer pointEar;
    /** 冻结积分 */
    private Integer pointFrez;
    /** 冻结积分日期 */
    private Integer pointFzda;
    
    // 邮寄日期字段
    /** 最近对帐单邮寄日期 */
    private Integer postDd;
    /** 上次分行日期 */
    private Integer prevBrday;
    /** 上次分行 */
    private Integer prevBrnch;
    /** 一般消费金额 */
    private BigDecimal purchases;
    
    // 查询相关字段
    /** 查询金额 */
    private BigDecimal queryAmt;
    /** 查询代码 */
    private String queryCode;
    /** 查询对帐单 */
    private String queryStmt;
    
    // 申请相关字段
    /** 最近申请修改日期 */
    private Integer reclaChdy;
    /** 申请代码 */
    private String reclaCode;
    /** 回收金额 */
    private BigDecimal recvryAmt;
    
    // 还款设定字段
    /** 还款金额 */
    private BigDecimal repayAmt;
    /** 还款金额（另一个） */
    private BigDecimal repayAmtx;
    /** 还款代码 */
    private String repayCode;
    /** 还款代码（另一个） */
    private String repayCodex;
    /** 还款日 */
    private Integer repayDay;
    /** 还款百分比 */
    private BigDecimal repayPct;
    /** 还款百分比（另一个） */
    private BigDecimal repayPctx;
    /** 最近还款修改日期 */
    private Integer repyChgdy;
    /** 信用积分 */
    private Integer scorePts;
    
    // 对帐单相关字段
    /** 对帐单总数 */
    private Integer statements;
    /** 帐单超限金额 */
    private BigDecimal stmAmtOl;
    /** 帐单消费余额 */
    private BigDecimal stmBalfre;
    /** 帐单日记息余额 */
    private BigDecimal stmBalint;
    /** 帐单日记息余额符号 */
    private String stmbalintflag;
    /** 当前余额 */
    private BigDecimal stmBalnce;
    /** 帐单余额符号 */
    private String stmBalflag;
    /** 帐单利息余额 */
    private BigDecimal stmBalori;
    /** 帐单结单日期 */
    private Integer stmClosdy;
    /** 对帐单代码 */
    private String stmCode;
    /** 分期付款金额 */
    private BigDecimal stmInstl;
    /** 最小还款额 */
    private BigDecimal stmMindue;
    /** 帐单免息余额 */
    private BigDecimal stmNoint;
    /** 帐单超限状态标志 */
    private Integer stmOlflag;
    /** 帐单逾期金额 */
    private BigDecimal stmOverdu;
    /** 帐单未清偿金额 */
    private BigDecimal stmPayUn;
    /** 帐单查询金额 */
    private BigDecimal stmQryamt;
    /** 帐单还款金额 */
    private BigDecimal stmRepay;
    /** 对帐单到期日 */
    private Integer stmtDd;
    /** 对帐单提取代码 */
    private String stmtPull;
    
    // 今日金额字段
    /** 当日金额 */
    private BigDecimal todayAmt;
    /** 当日金额符号 */
    private String todayAmtflag;
    /** 当日相关金额 */
    private BigDecimal todayRel;
    /** 未清偿百分比 */
    private BigDecimal unclPct;
    
    // 转销相关字段
    /** 转销修改日期 */
    private Integer wroffChdy;
    /** 转销代码 */
    private String wroffCode;
    
    // 产品相关字段
    /** 卡片级别 */
    private Integer prodLevel;
    /** 卡片最高卡种产品编号 */
    private Integer prodNbr;
    /** 帐户卡片前次状态 */
    private String acctPrsts;
    /** 帐户卡片状态 */
    private String acctSts;
    /** 首次申请队列代码 */
    private String appApque;
    
    // 积分扩展字段
    /** 当期奖励积分 */
    private Integer pointEnc;
    /** 当期合作单位转入积分 */
    private Integer pointImp;
    /** 当期合作单位转出积分 */
    private Integer pointExp;
    /** 当期合作单位转出积分符号 */
    private String ptExpflag;
    
    // 风控字段
    /** 进入风控队列次数 */
    private Integer riskCount;
    /** 进入风控队列日期 */
    private Integer riskDayin;
    
    // 分期付款扩展字段
    /** 分期付款未出帐单余额 */
    private BigDecimal balMp;
    /** 分期付款未出帐单余额符号 */
    private String balMpflag;
    /** 分期付款已出帐单余额 */
    private BigDecimal stmBalmp;
    /** 分期付款已出帐单余额符号 */
    private String stmBmflag;
    /** 临时额度调整原因 */
    private String lmtRsn;
    /** 联名卡卡片积分累计 */
    private Integer pointCard;
    /** 联名卡卡片积分累计符号 */
    private String ptCdflag;
    
    // 临时额度字段
    /** 临时额度 */
    private Integer tempLimit;
    /** 临时额度生效日期 */
    private Integer tlmtBeg;
    /** 临时额度失效日期 */
    private Integer tlmtEnd;
    /** 临时额度序号 */
    private Integer tlmtNo;
    /** 销卡原因 */
    private String canclResn;
    
    // 影子字段
    /** 影子滞纳金 */
    private BigDecimal shadowPen;
    /** 影子利息 */
    private BigDecimal shadowInt;
    /** 影子复利 */
    private BigDecimal shadowCmp;
    
    // 不计息余额细分字段(1-10)
    /** 本期应收费用1 */
    private BigDecimal balNint01;
    /** 本期应收费用2 */
    private BigDecimal balNint02;
    /** 本期应收费用3 */
    private BigDecimal balNint03;
    /** 本期应收费用4 */
    private BigDecimal balNint04;
    /** 本期应收费用5 */
    private BigDecimal balNint05;
    /** 本期应收费用6 */
    private BigDecimal balNint06;
    /** 本期应收费用7 */
    private BigDecimal balNint07;
    /** 本期应收费用8 */
    private BigDecimal balNint08;
    /** 本期应收费用9 */
    private BigDecimal balNint09;
    /** 本期应收费用10 */
    private BigDecimal balNint10;
    
    // 上期应收费用细分字段(1-10)
    /** 上期应收费用1 */
    private BigDecimal stmNint01;
    /** 上期应收费用2 */
    private BigDecimal stmNint02;
    /** 上期应收费用3 */
    private BigDecimal stmNint03;
    /** 上期应收费用4 */
    private BigDecimal stmNint04;
    /** 上期应收费用5 */
    private BigDecimal stmNint05;
    /** 上期应收费用6 */
    private BigDecimal stmNint06;
    /** 上期应收费用7 */
    private BigDecimal stmNint07;
    /** 上期应收费用8 */
    private BigDecimal stmNint08;
    /** 上期应收费用9 */
    private BigDecimal stmNint09;
    /** 上期应收费用10 */
    private BigDecimal stmNint10;
    
    // 扩展字段
    /** 外币币种 */
    private Integer currNum2;
    /** 核销处理标记 */
    private Integer wrofFlag;
    /** 账户分层代码1 */
    private String layercoder1;
    /** 账户分层代码2 */
    private String layercoder2;
    /** 商户类型和商户代码交易控制选项 */
    private String mcntrlYn;
    /** 不上报征信数据原因 */
    private String ncredRsn;
    /** 基本额度 */
    private Integer bscCred;
    /** 客户参考资料编号 */
    private String custrRef;
    /** 征信机构代码 */
    private String pbcBrnch;
    /** 停止分期业务 */
    private Integer stopmpYn;
    /** 综合授信额度 */
    private Integer credLmt2;
    /** 表外费用 */
    private BigDecimal balCmpfee;
    /** 本期大额分期余额 */
    private BigDecimal balLmp;
    /** 上期大额分期余额 */
    private BigDecimal stmLmp;
    /** 到期年月 */
    private Integer outMonth;
    /** 记录变更日期 */
    private Integer etlDay;
    /** 止付原因 */
    private Integer stpay;
    /** 不允许调高账户额度标识 */
    private String adjFlag;
    /** 用卡专案代码 */
    private String useSource;
    /** 当前利率代码失效期 */
    private Integer itcdEnddy;
    /** 账户新利率代码 */
    private Integer intCdnew;
    /** 新利率代码生效期 */
    private Integer itcnBegdy;
    /** 新利率代码失效期 */
    private Integer itcnEnddy;
    /** 利率代码有效期 */
    private Integer intEffect;
    /** 打标日期 */
    private Integer markDay;
    /** 上上期消费计息金额 */
    private BigDecimal pstmBalfr;
    /** 上上期分期计息金额 */
    private BigDecimal pstmBalmp;
    /** 上上期表内利息计息金额 */
    private BigDecimal pstmBalor;
    /** 上上期表外利息计息金额 */
    private BigDecimal pbalCmpin;
    /** 账户预借现金最高限额 */
    private Integer maxCash;
    /** 当日还款限额 */
    private BigDecimal paytdyLmt;
    /** 开户时间 */
    private String glbDtime;
    /** 取现应收利息 */
    private BigDecimal intChgdC;
    /** 本期取现利息余额 */
    private BigDecimal balOrintc;
    /** 上期取现利息余额 */
    private BigDecimal stmBalorc;
    /** 开通大额子额度功能 */
    private Integer sublmtL;
    /** 本期现金分期余额 */
    private BigDecimal balBmp;
    /** 本期现金分期余额符号位 */
    private String balBmpFlg;
    /** 上期现金分期余额 */
    private BigDecimal stmBmp;
    /** 上期现金分期余额符号位 */
    private String stmBmpFlg;
    /** 其他费用总额符号位 */
    private String otherFeesFlg;
    /** 单独核算利息子余额细分项 */
    private String orintsArr;
    /** 单独核算不计息表外利息 */
    private BigDecimal balNcmpis;
    /** 上上期单独核算不计息表内利息 */
    private BigDecimal pstmNoris;
    /** 上上期单独核算不计息表外利息 */
    private BigDecimal pbalNcmps;
    /** 是否开通超限功能 */
    private String overamtYn;
    /** 功能标志 */
    private String infoFlag;
    /** 最小还款额按比例计算部分 */
    private Integer mindueTyp;
    /** 费用及稅款合计符号位 */
    private String feesTaxesFlg;
} 