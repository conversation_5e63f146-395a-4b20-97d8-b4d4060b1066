package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度中心-中间表-对公客户信息Do
 *
 * <AUTHOR>
 * @date 2025-08-11 11:39:54
 */
@Table(name = "lb_t_corp_cust_info")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LbTCorpCustInfoDo extends LbTCorpCustInfoKeyDo implements Serializable {
    private static final long serialVersionUID = 1954870385359126528L;
    /** 客户类型 */
    @Column(name = "cust_typ")
    private String custTyp;
    /** 客户名称 */
    @Column(name = "cust_nm")
    private String custNm;
    /** 证件类型 */
    @Column(name = "cert_typ")
    private String certTyp;
    /** 证件号码 */
    @Column(name = "cert_no")
    private String certNo;
    /** 操作人编号 */
    @Column(name = "operator_id")
    private String operatorId;
    /** 所属组织编号 */
    @Column(name = "own_organ_id")
    private String ownOrganId;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
    /** 所属行业类型 */
    @Column(name = "belg_inds_typ")
    private String belgIndsTyp;
    /** 所属集团名称 */
    @Column(name = "belg_grp_nm")
    private String belgGrpNm;
    /** 所属集团编号 */
    @Column(name = "belg_grp_no")
    private String belgGrpNo;
    /** 国民经济行业分类-小类 */
    @Column(name = "industry_category")
    private String industryCategory;
    /** 国民经济行业分类-大类 */
    @Column(name = "vocation_level_two")
    private String vocationLevelTwo;
    /** 国民经济行业分类-中类 */
    @Column(name = "vocation_level_three")
    private String vocationLevelThree;
}
