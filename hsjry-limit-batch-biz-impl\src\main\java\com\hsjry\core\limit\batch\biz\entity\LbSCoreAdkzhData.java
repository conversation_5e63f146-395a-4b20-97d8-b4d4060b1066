/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.entity;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 核心系统-落地表-贷款账户主表文件数据实体
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/21 11:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LbSCoreAdkzhData {

    /** 法人代码 */
    private String faredm;

    /** 贷款账号 */
    private String daikzh;

    /** 贷款借据号 */
    private String dkjeju;

    /** 合同编号 */
    private String htngbh;

    /** 客户号 */
    private String kehhao;

    /** 客户名 */
    private String kehzwm;

    /** 营业机构 */
    private String yngyjg;

    /** 账务机构 */
    private String zhngjg;

    /** 产品代码 */
    private String chapdm;

    /** 产品名称 */
    private String chapmc;

    /** 会计类别 */
    private String dkkjlb;

    /** 开户日期 */
    private String kaihrq;

    /** 起息日期 */
    private String qixirq;

    /** 到期日期 */
    private String daoqrq;

    /** 期限 */
    private String qixian;

    /** 贷款形态,0-正常,1-逾期,2-呆滞,3-呆账 */
    private String daikxt;

    /** 应计非应计状态,0-应计,1-非应计 */
    private String yngjzt;

    /** 贷款账户状态,0-正常,1-销户,2-已核销,3-准销户,4-录入,5-已置换,6-冻结 */
    private String dkzhzt;

    /** 多笔贷款扣款顺序 */
    private BigDecimal dbkksx;

    /** 货币代号 */
    private String huobdh;

    /** 合同金额 */
    private BigDecimal htngje;

    /** 借据金额 */
    private BigDecimal jiejje;

    /** 已发放金额 */
    private BigDecimal yfngje;

    /** 冻结可放金额 */
    private BigDecimal djkfje;

    /** 可发放金额 */
    private BigDecimal kfngje;

    /** 正常本金 */
    private BigDecimal zhchbj;

    /** 逾期本金 */
    private BigDecimal yuqibj;

    /** 呆滞本金 */
    private BigDecimal dzhibj;

    /** 呆账本金 */
    private BigDecimal daizbj;

    /** 贷款基金 */
    private BigDecimal daikjj;

    /** 应收应计利息 */
    private BigDecimal ysyjlx;

    /** 催收应计利息 */
    private BigDecimal csyjlx;

    /** 应收欠息 */
    private BigDecimal yinsqx;

    /** 催收欠息 */
    private BigDecimal cuisqx;

    /** 应收应计罚息 */
    private BigDecimal ysyjfx;

    /** 催收应计罚息 */
    private BigDecimal csyjfx;

    /** 应收罚息 */
    private BigDecimal yinsfx;

    /** 催收罚息 */
    private BigDecimal cuisfx;

    /** 应计复息 */
    private BigDecimal yjfuxi;

    /** 复息 */
    private BigDecimal fuxiii;

    /** 应计贴息 */
    private BigDecimal yinjtx;

    /** 应收贴息 */
    private BigDecimal yinstx;

    /** 待摊利息 */
    private BigDecimal dtlixi;

    /** 核销本金 */
    private BigDecimal hexibj;

    /** 核销利息 */
    private BigDecimal hexilx;

    /** 置换本金 */
    private BigDecimal zhhabj;

    /** 置换利息 */
    private BigDecimal zhhalx;

    /** 利息收入 */
    private BigDecimal lixisr;

    /** 应收费用 */
    private BigDecimal yinsfy;

    /** 费用收入 */
    private BigDecimal feiysr;

    /** 应收罚金 */
    private BigDecimal yinsfj;

    /** 罚金收入 */
    private BigDecimal fajnsr;

    /** 准备金 */
    private BigDecimal zhunbj;

    /** 最后财务交易日 */
    private String zhjyrq;

    /** 止息日期 */
    private String zhixrq;

    /** 备注 */
    private String beizhu;

    /** 明细序号 */
    private BigDecimal mxxhao;

    /** 开户机构 */
    private String kaihjg;

    /** 开户柜员 */
    private String kaihgy;

    /** 维护日期 */
    private String weihrq;

    /** 维护柜员 */
    private String weihgy;

    /** 销户日期 */
    private String xiohrq;

    /** 销户柜员 */
    private String xiohgy;

    /** 维护机构 */
    private String weihjg;

    /** 维护时间 */
    private BigDecimal weihsj;

    /** 时间戳 */
    private BigDecimal shjnch;

    /** 记录状态 */
    private String jiluzt;
} 