package com.hsjry.core.limit.batch.dal.dao.intf;

import com.hsjry.core.limit.batch.dal.dao.model.LbSElcblDsctLmtOcpDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSElcblDsctLmtOcpQuery;

import java.util.List;

import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 电票系统-历史表-日终贴现额度占用同步数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbSElcblDsctLmtOcpDao extends IBaseDao<LbSElcblDsctLmtOcpDo> {
    /**
     * 分页查询电票系统-历史表-日终贴现额度占用同步
     *
     * @param lbSElcblDsctLmtOcpQuery 条件
     * @return PageInfo<LbSElcblDsctLmtOcpDo>
     */
    PageInfo<LbSElcblDsctLmtOcpDo> selectPage(LbSElcblDsctLmtOcpQuery lbSElcblDsctLmtOcpQuery, PageParam pageParam);

    /**
     * 根据key查询电票系统-历史表-日终贴现额度占用同步
     *
     * @param billNumb
     * @param billRangeStart
     * @param billRangeEnd
     * @return
     */
    LbSElcblDsctLmtOcpDo selectByKey(String billNumb, String billRangeStart, String billRangeEnd);

    /**
     * 根据key删除电票系统-历史表-日终贴现额度占用同步
     *
     * @param billNumb
     * @param billRangeStart
     * @param billRangeEnd
     * @return
     */
    int deleteByKey(String billNumb, String billRangeStart, String billRangeEnd);

    /**
     * 查询电票系统-历史表-日终贴现额度占用同步信息
     *
     * @param lbSElcblDsctLmtOcpQuery 条件
     * @return List<LbSElcblDsctLmtOcpDo>
     */
    List<LbSElcblDsctLmtOcpDo> selectByExample(LbSElcblDsctLmtOcpQuery lbSElcblDsctLmtOcpQuery);

    /**
     * 新增电票系统-历史表-日终贴现额度占用同步信息
     *
     * @param lbSElcblDsctLmtOcp 条件
     * @return int>
     */
    int insertBySelective(LbSElcblDsctLmtOcpDo lbSElcblDsctLmtOcp);

    /**
     * 修改电票系统-历史表-日终贴现额度占用同步信息
     *
     * @param lbSElcblDsctLmtOcp
     * @return
     */
    int updateBySelective(LbSElcblDsctLmtOcpDo lbSElcblDsctLmtOcp);

    /**
     * 修改电票系统-历史表-日终贴现额度占用同步信息
     *
     * @param lbSElcblDsctLmtOcp
     * @param lbSElcblDsctLmtOcpQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbSElcblDsctLmtOcpDo lbSElcblDsctLmtOcp,
        LbSElcblDsctLmtOcpQuery lbSElcblDsctLmtOcpQuery);
}
