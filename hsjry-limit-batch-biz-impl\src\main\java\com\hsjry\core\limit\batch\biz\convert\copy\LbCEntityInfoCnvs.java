package com.hsjry.core.limit.batch.biz.convert.copy;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.hsjry.core.limit.batch.dal.dao.model.LbCEntityInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcEntityInfoDo;

/**
 * 实体信息转换器
 *
 * <AUTHOR>
 * @date 2025-07-09 12:31:05
 */
@Mapper(componentModel = "spring")
public interface LbCEntityInfoCnvs {

    LbCEntityInfoCnvs INSTANCE = Mappers.getMapper(LbCEntityInfoCnvs.class);

    /**
     * 源DO转换为目标DO（主要转换方法）
     * 从LcEntityInfoDo转换为LbCEntityInfoDo
     */
    LbCEntityInfoDo do2Copy(LcEntityInfoDo model);
} 