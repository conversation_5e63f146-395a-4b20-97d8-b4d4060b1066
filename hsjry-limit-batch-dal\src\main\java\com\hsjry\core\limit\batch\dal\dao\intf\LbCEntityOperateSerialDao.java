package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbCEntityOperateSerialDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbCEntityOperateSerialQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 实体操作流水数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-03 10:03:12
 */
public interface LbCEntityOperateSerialDao extends IBaseDao<LbCEntityOperateSerialDo> {
    /**
     * 分页查询实体操作流水
     *
     * @param lbCEntityOperateSerialQuery 条件
     * @return PageInfo<LbCEntityOperateSerialDo>
     */
    PageInfo<LbCEntityOperateSerialDo> selectPage(LbCEntityOperateSerialQuery lbCEntityOperateSerialQuery,
        PageParam pageParam);

    /**
     * 根据key查询实体操作流水
     *
     * @param leosSerialNo
     * @return
     */
    LbCEntityOperateSerialDo selectByKey(String leosSerialNo);

    /**
     * 根据key删除实体操作流水
     *
     * @param leosSerialNo
     * @return
     */
    int deleteByKey(String leosSerialNo);

    /**
     * 查询实体操作流水信息
     *
     * @param lbCEntityOperateSerialQuery 条件
     * @return List<LbCEntityOperateSerialDo>
     */
    List<LbCEntityOperateSerialDo> selectByExample(LbCEntityOperateSerialQuery lbCEntityOperateSerialQuery);

    /**
     * 新增实体操作流水信息
     *
     * @param lbCEntityOperateSerial 条件
     * @return int>
     */
    int insertBySelective(LbCEntityOperateSerialDo lbCEntityOperateSerial);

    /**
     * 修改实体操作流水信息
     *
     * @param lbCEntityOperateSerial
     * @return
     */
    int updateBySelective(LbCEntityOperateSerialDo lbCEntityOperateSerial);

    /**
     * 批量插入实体操作流水信息
     *
     * @param lbCEntityOperateSerialList 批量数据
     * @return int
     */
    int insertList(List<LbCEntityOperateSerialDo> lbCEntityOperateSerialList);

    /**
     * 清空实体操作流水信息表所有数据
     *
     * @return int
     */
    int deleteAll();

    /**
     * 修改实体操作流水信息
     *
     * @param lbCEntityOperateSerial
     * @param lbCEntityOperateSerialQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbCEntityOperateSerialDo lbCEntityOperateSerial,
        LbCEntityOperateSerialQuery lbCEntityOperateSerialQuery);
}
