package com.hsjry.core.limit.batch.biz.job.job.copy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * 实体操作流水数据备份同步任务
 * 
 * 负责将实体操作流水数据从源表同步到备份表
 * 源表：lc_entity_operate_serial (limit-center模块)
 * 目标表：lb_c_entity_operate_serial (limit-batch模块)
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/11 11:41
 */
@Slf4j
@Service("lbCEntityOperateSerialBakSyncJob")
public class LbCEntityOperateSerialBakSyncJob extends AbstractBaseBatchJob {
    
    public LbCEntityOperateSerialBakSyncJob() {
        log.info("LbCEntityOperateSerialBakSyncJob Bean初始化完成");
    }

    @Autowired
    @Qualifier("lbCEntityOperateSerialBakSyncBizImpl")
    private BaseOrdinaryBiz baseOrdinaryBiz;

    @Override
    public BaseOrdinaryBiz getBaseOrdinaryBiz() {
        return baseOrdinaryBiz;
    }
} 