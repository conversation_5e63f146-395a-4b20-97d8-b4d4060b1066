package com.hsjry.core.limit.batch.biz.job.job.file;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * 核心产品定义历史表文件的同步处理任务
 * 负责H_CORE_PPROD文件的批量同步处理
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/21 10:30
 */
@Slf4j
@Service("lbHCorePprodFileSyncJob")
public class LbHCorePprodFileSyncJob extends AbstractBaseBatchJob {
    
    public LbHCorePprodFileSyncJob() {
        log.info("LbHCorePprodFileSyncJob Bean初始化完成");
    }

    @Autowired
    @Qualifier("lbHCorePprodFileSyncBizImpl")
    private BaseOrdinaryBiz lbHCorePprodFileSyncBizImpl;

    @Override
    public BaseOrdinaryBiz getBaseOrdinaryBiz() {
        return lbHCorePprodFileSyncBizImpl;
    }
} 