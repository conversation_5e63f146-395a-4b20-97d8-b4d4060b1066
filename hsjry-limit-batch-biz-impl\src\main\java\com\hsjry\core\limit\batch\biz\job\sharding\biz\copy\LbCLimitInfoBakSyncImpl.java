package com.hsjry.core.limit.batch.biz.job.sharding.biz.copy;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.biz.convert.copy.LbCLimitInfoConverter;
import com.hsjry.base.common.model.enums.limit.EnumLimitHandlerStatus;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.limit.AbstractShardingPrepareBiz;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.dal.dao.intf.LbCLimitInfoDao;
import com.hsjry.core.limit.batch.dal.dao.model.LbCLimitInfoDo;
import com.hsjry.core.limit.center.dal.dao.intf.CustLimitInfoBatchDao;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.CustLimitInfoQuery;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
/**
 * 额度实例信息数据同步到[核心系统-落地表-额度实例信息表]
 * 从源表lc_cust_limit_info同步数据到目标表lb_c_limit_info
 *
 * <AUTHOR>
 * @version V3.0.5
 * @since 2025/7/510:32
 */
@Slf4j
@Service("lbCLimitInfoBakSyncImpl")
@RequiredArgsConstructor
public class LbCLimitInfoBakSyncImpl extends AbstractShardingPrepareBiz<CustLimitInfoQuery>
    implements JobCoreBusiness<LcCustLimitInfoDo> {

    private final LbCLimitInfoDao lbCLimitInfoDao;
    private final CustLimitInfoBatchDao custLimitInfoBatchDao;

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.C_LIMIT_INFO_BAK_SYNC;
    }

    @Override
    public Integer selectCountByCurrentGroupFromDb(CustLimitInfoQuery query) {
        Integer count = custLimitInfoBatchDao.selectCountByCurrentGroup(query);
        return count != null ? count : 0;
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);
        log.info(prefixLog + "====================== 分片逻辑 start ================================================");

        List<JobShared> jobSharedList = new ArrayList<>();
        // SQL批处理数量,暂定为分片数量
        Integer batchFixNum = jobInitDto.getFixNum();
        // 当前分组的最大值,为下次批处理的最小值
        LcCustLimitInfoDo maxLimitInfoDo = new LcCustLimitInfoDo();

        // 构造查询条件,查询当前分批处理的排序最大对象
        CustLimitInfoQuery query = CustLimitInfoQuery.builder().tenantId(AppParamUtil.getTenantId())//
            .offset(batchFixNum - 1).limit(1).build();

        // 分片流水
        int batchNum = 0;
        while (maxLimitInfoDo != null) {
            query.setCustLimitId(maxLimitInfoDo.getCustLimitId());
            maxLimitInfoDo = custLimitInfoBatchDao.selectFirstOne(query);
            // 统计分片数量
            batchNum = countBatchNum(batchFixNum, query, maxLimitInfoDo, batchNum, jobInitDto, jobSharedList,
                query.getCustLimitId(), false);
        }

        log.info(prefixLog + "====================== 分片逻辑 end ================================================");
        log.info(prefixLog + "额度实例信息同步分片任务生成完成,共[{}]个分片", jobSharedList.size());
        return jobSharedList;
    }

    @Override
    public ShardingResult<LcCustLimitInfoDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info(prefixLog + "开始查询分片数据,分片号:[{}]", batchNum);

        ShardingResult<LcCustLimitInfoDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }

        // 创建查询条件
        CustLimitInfoQuery query = CustLimitInfoQuery.builder().tenantId(AppParamUtil.getTenantId())//
            .offset(jobShared.getOffset()).limit(jobShared.getLimit()).build();

        log.info(prefixLog + "=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");

        // 查询分片数据
        List<LcCustLimitInfoDo> dataList = custLimitInfoBatchDao.selectShardList(query);
        shardingResult.setShardingResultList(dataList);

        log.info(prefixLog + "分片数据查询完成,分片号:[{}],数据量:[{}]", batchNum,
            CollectionUtil.isEmpty(dataList) ? 0 : dataList.size());

        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LcCustLimitInfoDo> shardingResult) {
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        Integer dataSize = shardingResult.getShardingResultList().size();

        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        List<LcCustLimitInfoDo> dataList = shardingResult.getShardingResultList();

        if (CollectionUtil.isEmpty(dataList)) {
            log.info(prefixLog + "=========分片执行结束:[{}]数量为空===========", batchNum);
            return;
        }

        log.info(prefixLog + "=========分片执行开始:[{}]数量为[{}]===========", batchNum, dataSize);

        try {
            LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();
            // 只在第一个分片中清空目标表
            if (sliceBatchSerialDo.getBatchNum() == 1) {
                log.info(prefixLog + "第一个分片,开始清空目标表lb_c_limit_info");
                lbCLimitInfoDao.deleteAll();
                log.info(prefixLog + "目标表lb_c_limit_info清空完成");
            }
            // 更新分片流水前，初始化执行状态，确保不为空
            if (sliceBatchSerialDo.getSharedStatus() == null) {
                sliceBatchSerialDo.setSharedStatus(EnumLimitHandlerStatus.IN_HANDLE.getCode());
            }
            // 数据转换和插入
            List<LbCLimitInfoDo> targetDataList = convertSourceToTarget(dataList);
            if (CollectionUtil.isNotEmpty(targetDataList)) {
                lbCLimitInfoDao.insertList(targetDataList);
                log.info(prefixLog + "成功插入[{}]条数据到目标表", targetDataList.size());
            }

            // 更新分片流水成功
            normalUpdateSliceSerial(dataSize, sliceBatchSerialDo);

        } catch (Exception e) {
            log.error(prefixLog + "分片执行异常:[{}]", batchNum, e);
            throw new HsjryBizException(EnumBatchJobError.SYSTEM_ERR.getCode(),
                EnumBatchJobError.SYSTEM_ERR.getDescription());
        }
        log.info(prefixLog + "=========分片执行结束:[{}]数量为[{}]===========", batchNum, dataSize);
    }

    /**
     * 数据转换:从LcCustLimitInfoDo转换为LbCLimitInfoDo
     */
    private List<LbCLimitInfoDo> convertSourceToTarget(List<LcCustLimitInfoDo> sourceList) {
        if (CollectionUtil.isEmpty(sourceList)) {
            return Collections.emptyList();
        }
        return LbCLimitInfoConverter.doList2CopyList(sourceList);
    }
}
