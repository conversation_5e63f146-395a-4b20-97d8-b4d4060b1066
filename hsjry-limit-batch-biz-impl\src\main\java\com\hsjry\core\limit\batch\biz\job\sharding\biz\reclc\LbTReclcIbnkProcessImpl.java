package com.hsjry.core.limit.batch.biz.job.sharding.biz.reclc;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.limit.AbstractShardingPrepareBiz;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTReclcIbnkLglpsnLvlDao;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTReclcIbnkTotlDao;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitObjectInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.CustLimitObjectInfoQuery;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 额度中心-同业客户额度重算
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Slf4j
@Service("lbTReclcIbnkProcessImpl")
@RequiredArgsConstructor
public class LbTReclcIbnkProcessImpl extends AbstractShardingPrepareBiz<CustLimitObjectInfoQuery>
    implements JobCoreBusiness<LcCustLimitObjectInfoDo> {

    private final LbTReclcIbnkLglpsnLvlDao lbTReclcIbnkLglpsnLvlDao;
    private final LbTReclcIbnkTotlDao lbTReclcIbnkTotlDao;

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.LB_T_RECLC_IBNK_PROCESS;
    }

    @Override
    public Integer selectCountByCurrentGroupFromDb(CustLimitObjectInfoQuery query) {
        return 0;
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);
        log.info(prefixLog + "====================== 分片逻辑 start ================================================");
        List<JobShared> jobSharedList = new ArrayList<>();
        log.info(prefixLog + "====================== 分片逻辑 end ================================================");
        log.info(prefixLog + "同业客户额度重算分片任务生成完成,共{}个分片",
            CollectionUtil.isEmpty(jobSharedList) ? 0 : jobSharedList.size());
        return jobSharedList;
    }

    @Override
    public ShardingResult<LcCustLimitObjectInfoDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {

        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info(prefixLog + "开始查询分片数据,分片号:{}", jobShared.getBatchNum());

        ShardingResult<LcCustLimitObjectInfoDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }
        // 创建查询条件
        CustLimitObjectInfoQuery query = CustLimitObjectInfoQuery.builder().tenantId(AppParamUtil.getTenantId())//
            .offset(jobShared.getOffset()).limit(jobShared.getLimit()).build();
        log.info(prefixLog + "=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");

        // 查询分片数据
        List<LcCustLimitObjectInfoDo> dataList = Lists.newArrayList();
        shardingResult.setShardingResultList(dataList);

        log.info("分片数据查询完成,分片号:{},数据量:{}", jobShared.getBatchNum(),
            CollectionUtil.isEmpty(dataList) ? 0 : dataList.size());

        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LcCustLimitObjectInfoDo> shardingResult) {
        List<LcCustLimitObjectInfoDo> shardingDataList = shardingResult.getShardingResultList();
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        Integer dataSize = shardingResult.getShardingResultList().size();

        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);
        log.info(prefixLog + "=========分片执行开始:[{}]数量为[{}]===========", batchNum, dataSize);

        try {
            LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();

            // 1.处理法人层额度重算
            log.info(prefixLog + "步骤1:开始处理法人层额度重算");
            // 1.1.清空法人层额度中间表
            int truncateLegalPersonLevelCount = lbTReclcIbnkLglpsnLvlDao.truncateLegalPersonLevelLimit();
            log.info(prefixLog + "步骤1.1:清空法人层额度中间表完成,影响记录数:[{}]", truncateLegalPersonLevelCount);

            // 1.2.插入法人层额度客户编号和额度编号
            int insertLegalPersonLevelCount = lbTReclcIbnkLglpsnLvlDao.insertLegalPersonLevelLimit();
            log.info(prefixLog + "步骤1.2:插入法人层额度客户编号和额度编号完成,影响记录数:[{}]",
                insertLegalPersonLevelCount);

            // 1.3.更新法人层额度中间表金额信息
            int mergeLegalPersonLevelAmountCount = lbTReclcIbnkLglpsnLvlDao.mergeLegalPersonLevelLimitAmount();
            log.info(prefixLog + "步骤1.3:更新法人层额度中间表金额信息完成,影响记录数:[{}]",
                mergeLegalPersonLevelAmountCount);

            // 1.4.更新额度实例金额信息
            int mergeLegalPersonLevelInstanceCount = lbTReclcIbnkLglpsnLvlDao.mergeLegalPersonLevelLimitInstance();
            log.info(prefixLog + "步骤1.4:更新额度实例金额信息完成,影响记录数:[{}]",
                mergeLegalPersonLevelInstanceCount);

            // 2.处理同业客户总额度重算
            log.info(prefixLog + "步骤2:开始处理同业客户总额度重算");
            // 2.1.清空同业客户总额度中间表
            int truncateTotalCount = lbTReclcIbnkTotlDao.truncateTotalLimit();
            log.info(prefixLog + "步骤2.1:清空同业客户总额度中间表完成,影响记录数:[{}]", truncateTotalCount);

            // 2.2.插入同业客户总额度客户编号和额度编号
            int insertTotalCount = lbTReclcIbnkTotlDao.insertTotalLimit();
            log.info(prefixLog + "步骤2.2:插入同业客户总额度客户编号和额度编号完成,影响记录数:[{}]", insertTotalCount);

            // 2.3.更新同业客户总额度中间表金额信息
            int mergeTotalAmountCount = lbTReclcIbnkTotlDao.mergeTotalLimitAmount();
            log.info(prefixLog + "步骤2.3:更新同业客户总额度中间表金额信息完成,影响记录数:[{}]", mergeTotalAmountCount);

            // 2.4.更新额度实例金额信息
            int mergeTotalInstanceCount = lbTReclcIbnkTotlDao.mergeTotalLimitInstance();
            log.info(prefixLog + "步骤2.4:更新额度实例金额信息完成,影响记录数:[{}]", mergeTotalInstanceCount);

            // 更新分片流水成功
            normalUpdateSliceSerial(0, sliceBatchSerialDo);

        } catch (Exception e) {
            log.error(prefixLog + "分片执行异常:[{}]", batchNum, e);
            throw new HsjryBizException(EnumBatchJobError.SYSTEM_ERR.getCode(),
                EnumBatchJobError.SYSTEM_ERR.getDescription());
        }

        log.info(prefixLog + "=========分片执行结束:[{}]===========", batchNum);
    }
}
