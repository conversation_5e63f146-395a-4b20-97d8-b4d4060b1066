<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>hsjry-limit-batch</artifactId>
        <groupId>com.hsjry.core</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>hsjry-limit-batch-common</artifactId>
    <dependencies>
        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hsjry.base.common</groupId>
            <artifactId>model</artifactId>
            <version>4.1.0-HNNS-SNAPSHOT</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>com.hsjry.base.common</groupId>
            <artifactId>job</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hsjry.base.common</groupId>
            <artifactId>file-server</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.ethz.ganymed</groupId>
            <artifactId>ganymed-ssh2</artifactId>
            <version>262</version>
        </dependency>
    </dependencies>

</project>
