系统基本原则：
所有的业务功能，以树的形式向下延申。
树自上而下，不需要逆向调用。
复杂业务，自上而下拆分，合拼业务，自下而上合并。
层级说明原则：
Biz，入参（DTO），出参（DTO）。
BIz，完整的功能单位以biz接口提供。
Biz，保留核心生命周期。biz内部可以整合，相互调用，抽象出非核心生命周期，以sharedBiz为子层级，sharedBiz权限和Biz同级，但不对外暴露。
sharedBiz，入参（DTO、BO、DO），出参（DTO、BO、DO）。
sharedBiz，高度抽象后，可以降层自core。
sharedBiz，对Biz功能抽象，并非完整功能，为Biz子层，存在于BizImpl中。
core，入参（DTO、BO），出参（DTO、BO）。
core，sharedBiz高度抽象后的沉淀层，独立处理由biz拆分后的子功能点。
core 核心应该是对bo的操作，dto在biz层，进行拆分（拆分，多个是bo，如果只是对do的操作，就忽略core层，直接调用dal）
core，定义BO，和converter。
dal，入参（DO），出参（DO）。
dal，作为数据层，存在两部分：dao，数据库操作，提供对数据库表的增删改查操作；interation，外部调用，调用接口提供服务，需要转换外部DTO为内部DO。
dao，查询类服务请求以*Query为名的对象封装。更新类服务请求以*Update为名的对象封装。
integration，调用对象（DTO）需要转换为内部使用的对象（DO）使用。
系统业务成长原则：
自上向下拆分，自下向上合并原则。

BIz，外部接口点扩展，使用plugin模块适配，当业务成长到一定复杂程度，plugin接口回归至Biz，减少调用层级。
Biz，内部功能扩展，使用sharedBiz优先拆分，当业务成长到一定复杂程度，sharedBiz接口降层至core，降低复杂功能节点的职能。
core，内部多个职能降低时，可以多个接口合并，上升至Biz，降低调用层级。当单个职能降低至库表操作时，上升至Biz，直接调用dal，降低调用层级。
dal，dao保证职能单一，降低接口复杂度，减轻数据库压力。
dal，dao业务复杂度上升，需要拆分，将职能业务，上升至core。
系统调用原则：
简单业务，直接调用，尽量减少层级调用。复杂业务，拆分，均衡个子节点职能。

Biz，优先调用dal，其次core，再者sharedBiz，最后其他Biz（不推荐）
core，优先dal，其次core，（高度复杂上升sharedBiz，保证调用清晰，职能均衡）。
dal，提供服务，尽量单一，（复杂操作，拆分接口，上升core封装）。
日志
java类添加注解@Slf4j
输出格式eg. log.info("参数名称1:{},参数名称2:{}",param1,param2);
debug级别日志需要添加if(log.isDebugEnabled()){}
单元测试
说明：
后缀名以Test结尾。
新建测试类继承AbstractBaseTest,需要实现prepareData、execute、clearData方法。
清除测试中数据库，需要在本测试类添加注解@Rollback、@Transactional。
清除所有的测试数据添加clearAllData方法。
SQL
使用通用mapper

通过 代码生成器 生成
如果是第一次对表生成，建议使用 DO、example、DAO、mapper、xml 文件
如果是后续继续生成，覆盖DO、example、xml文件中的column、 DAO文件不要覆盖
自定义sql
单表个性化操作不再维护到ext类的文件中，ext类文件供连表查询使用，历史遗留的ext文件需要逐步迁移回通用mapper.xml文件中
单表个性化查询，需要自定义返回字段，且多个sql公用，可以提供公共返回对象，id 命名规范 查询响应Do_Column_List，查询响应DO 由驼峰转为下划线规则。如果只有一个，返回字段直接定义在对应的sql语句中。
如果sql查询返回的DO需要自定义，需要自定义resultMap，id 命名规范：查询响应Do + Map
查询规范
交易内部查询结果不存在，允许抛异常
外部查询结果不存在,单个对象返回null;集合对象返回空的集合