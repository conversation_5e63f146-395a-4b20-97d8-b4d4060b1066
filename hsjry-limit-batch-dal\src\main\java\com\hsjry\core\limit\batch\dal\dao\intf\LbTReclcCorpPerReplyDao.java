package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpPerReplyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcCorpPerReplyQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中对公客户中单笔单批额度数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-08-21 11:31:28
 */
public interface LbTReclcCorpPerReplyDao extends IBaseDao<LbTReclcCorpPerReplyDo> {
    /**
     * 分页查询额度中心-中间表-额度重算中对公客户中单笔单批额度
     *
     * @param lbTReclcCorpPerReplyQuery 条件
     * @return PageInfo<LbTReclcCorpPerReplyDo>
     */
    PageInfo<LbTReclcCorpPerReplyDo> selectPage(LbTReclcCorpPerReplyQuery lbTReclcCorpPerReplyQuery,
        PageParam pageParam);

    /**
     * 根据key查询额度中心-中间表-额度重算中对公客户中单笔单批额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    LbTReclcCorpPerReplyDo selectByKey(String custNo, String custLimitId);

    /**
     * 根据key删除额度中心-中间表-额度重算中对公客户中单笔单批额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custNo, String custLimitId);

    /**
     * 查询额度中心-中间表-额度重算中对公客户中单笔单批额度信息
     *
     * @param lbTReclcCorpPerReplyQuery 条件
     * @return List<LbTReclcCorpPerReplyDo>
     */
    List<LbTReclcCorpPerReplyDo> selectByExample(LbTReclcCorpPerReplyQuery lbTReclcCorpPerReplyQuery);

    /**
     * 新增额度中心-中间表-额度重算中对公客户中单笔单批额度信息
     *
     * @param lbTReclcCorpPerReply 条件
     * @return int>
     */
    int insertBySelective(LbTReclcCorpPerReplyDo lbTReclcCorpPerReply);

    /**
     * 修改额度中心-中间表-额度重算中对公客户中单笔单批额度信息
     *
     * @param lbTReclcCorpPerReply
     * @return
     */
    int updateBySelective(LbTReclcCorpPerReplyDo lbTReclcCorpPerReply);

    /**
     * 修改额度中心-中间表-额度重算中对公客户中单笔单批额度信息
     *
     * @param lbTReclcCorpPerReply
     * @param lbTReclcCorpPerReplyQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTReclcCorpPerReplyDo lbTReclcCorpPerReply,
        LbTReclcCorpPerReplyQuery lbTReclcCorpPerReplyQuery);

    // ==================== 单笔单批额度重算相关方法 ====================

    /**
     * 3.1.清空单笔单批额度中间表
     */
    int truncateSingleBatchLimit();

    /**
     * 3.2.插入单笔单批额度客户编号和额度编号
     */
    int insertSingleBatchLimit();

    /**
     * 3.3.更新单笔单批额度中间表金额信息
     */
    int mergeSingleBatchLimitAmount();

    /**
     * 3.4.更新额度实例金额信息
     */
    int mergeSingleBatchLimitInstance();
}
