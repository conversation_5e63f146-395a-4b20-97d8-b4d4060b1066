package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbCLimitRelationDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbCLimitRelationDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbCLimitObjectInfoQuery;
import com.hsjry.core.limit.batch.dal.dao.query.LbCLimitRelationQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度实例关联数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-03 10:03:12
 */
public interface LbCLimitRelationDao extends IBaseDao<LbCLimitRelationDo> {
    /**
     * 分页查询额度实例关联
     *
     * @param lbCLimitRelationQuery 条件
     * @return PageInfo<LbCLimitRelationDo>
     */
    PageInfo<LbCLimitRelationDo> selectPage(LbCLimitRelationQuery lbCLimitRelationQuery, PageParam pageParam);

    /**
     * 根据key查询额度实例关联
     *
     * @param limitRelationId
     * @return
     */
    LbCLimitRelationDo selectByKey(String limitRelationId);

    /**
     * 根据key删除额度实例关联
     *
     * @param limitRelationId
     * @return
     */
    int deleteByKey(String limitRelationId);

    /**
     * 查询额度实例关联信息
     *
     * @param lbCLimitRelationQuery 条件
     * @return List<LbCLimitRelationDo>
     */
    List<LbCLimitRelationDo> selectByExample(LbCLimitRelationQuery lbCLimitRelationQuery);

    /**
     * 新增额度实例关联信息
     *
     * @param lbCLimitRelation 条件
     * @return int>
     */
    int insertBySelective(LbCLimitRelationDo lbCLimitRelation);

    /**
     * 修改额度实例关联信息
     *
     * @param lbCLimitRelation
     * @return
     */
    int updateBySelective(LbCLimitRelationDo lbCLimitRelation);

    /**
     * 修改额度实例关联信息
     *
     * @param lbCLimitRelation
     * @param lbCLimitRelationQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbCLimitRelationDo lbCLimitRelation, LbCLimitRelationQuery lbCLimitRelationQuery);

    /**
     * 批量插入额度实例所属对象信息
     *
     * @param lbCLimitRelationList 批量数据
     * @return int
     */
    @Override
    int insertList(List<LbCLimitRelationDo> lbCLimitRelationList);

    /**
     * 清空额度实例所属对象信息所有数据
     *
     * @return int
     */
    int deleteAll();

    /**
     * 查询分片数据
     *
     * @param query 查询条件
     * @return List<LbCLimitRelationDo>
     */
    List<LbCLimitRelationDo> selectShardList(LbCLimitRelationQuery query);

    /**
     * 获取第一个对象，limit m，1
     *
     * @param query 查询条件
     * @return LbCLimitRelationDo
     */
    LbCLimitRelationDo selectFirstOne(LbCLimitRelationQuery query);

    /**
     * 获取当前组的数据量
     *
     * @param query 查询条件
     * @return Integer
     */
    Integer selectCountByCurrentGroup(LbCLimitRelationQuery query);
}
