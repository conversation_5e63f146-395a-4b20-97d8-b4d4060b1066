package com.hsjry.core.limit.batch.biz.job.job.copy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * 备份表-额度实例流水信息数据备份同步处理任务
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/16 10:00
 */
@Slf4j
@Service("lbCLimitOperateSerialBakSyncJob")
public class LbCLimitOperateSerialBakSyncJob extends AbstractBaseBatchJob {
    public LbCLimitOperateSerialBakSyncJob() {
        log.info("LbCLimitOperateSerialBakSyncJob Bean初始化完成");
    }

    @Autowired
    @Qualifier("lbCLimitOperateSerialBakSyncBizImpl")
    private BaseOrdinaryBiz baseOrdinaryBiz;

    @Override
    public BaseOrdinaryBiz getBaseOrdinaryBiz() {
        return baseOrdinaryBiz;
    }
} 