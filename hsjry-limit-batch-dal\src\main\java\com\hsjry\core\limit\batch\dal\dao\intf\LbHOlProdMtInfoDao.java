package com.hsjry.core.limit.batch.dal.dao.intf;

import com.hsjry.core.limit.batch.dal.dao.model.LbHOlProdMtInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbHOlProdMtInfoQuery;

import java.util.List;

import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度产品信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbHOlProdMtInfoDao extends IBaseDao<LbHOlProdMtInfoDo> {
    /**
     * 分页查询额度产品信息
     *
     * @param lbHOlProdMtInfoQuery 条件
     * @return PageInfo<LbHOlProdMtInfoDo>
     */
    PageInfo<LbHOlProdMtInfoDo> selectPage(LbHOlProdMtInfoQuery lbHOlProdMtInfoQuery, PageParam pageParam);

    /**
     * 根据key查询额度产品信息
     *
     * @param creditLimitId
     * @return
     */
    LbHOlProdMtInfoDo selectByKey(String creditLimitId);

    /**
     * 根据key删除额度产品信息
     *
     * @param creditLimitId
     * @return
     */
    int deleteByKey(String creditLimitId);

    /**
     * 查询额度产品信息信息
     *
     * @param lbHOlProdMtInfoQuery 条件
     * @return List<LbHOlProdMtInfoDo>
     */
    List<LbHOlProdMtInfoDo> selectByExample(LbHOlProdMtInfoQuery lbHOlProdMtInfoQuery);

    /**
     * 新增额度产品信息信息
     *
     * @param lbHOlProdMtInfo 条件
     * @return int>
     */
    int insertBySelective(LbHOlProdMtInfoDo lbHOlProdMtInfo);

    /**
     * 修改额度产品信息信息
     *
     * @param lbHOlProdMtInfo
     * @return
     */
    int updateBySelective(LbHOlProdMtInfoDo lbHOlProdMtInfo);

    /**
     * 修改额度产品信息信息
     *
     * @param lbHOlProdMtInfo
     * @param lbHOlProdMtInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbHOlProdMtInfoDo lbHOlProdMtInfo, LbHOlProdMtInfoQuery lbHOlProdMtInfoQuery);
}
