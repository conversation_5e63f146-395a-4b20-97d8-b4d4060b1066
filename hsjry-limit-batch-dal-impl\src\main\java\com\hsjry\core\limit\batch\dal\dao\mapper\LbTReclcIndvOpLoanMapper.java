package com.hsjry.core.limit.batch.dal.dao.mapper;

import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIndvOpLoanDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 额度中心-中间表-额度重算中个人额度中经营贷额度mapper
 *
 * <AUTHOR>
 * @date 2025-08-21 13:10:51
 */
public interface LbTReclcIndvOpLoanMapper extends CommonMapper<LbTReclcIndvOpLoanDo> {

    // ==================== 经营贷额度重算相关方法 ====================

    /**
     * 1.1.清空经营贷额度中间表
     */
    int truncateOpLoanLimit();

    /**
     * 1.2.插入经营贷额度客户编号和额度编号
     */
    int insertOpLoanLimit();

    /**
     * 1.3.更新经营贷额度中间表金额信息
     */
    int mergeOpLoanLimitAmount();

    /**
     * 1.4.更新额度实例金额信息
     */
    int mergeOpLoanLimitInstance();
}