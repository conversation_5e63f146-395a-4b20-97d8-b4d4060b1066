package com.hsjry.core.limit.batch.biz.job.biz;

import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.core.limit.batch.biz.ReCalPlanBiz;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.common.utils.SSHClient;
import com.hsjry.lang.common.exception.HsjryRpcException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
/**
 * 大数据下档
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2024/5/21 10:25
 */
@Service
@Slf4j
public class BigDataSupplyBizImpl implements BaseOrdinaryBiz {
    @Value("${db.server.ip:**************}")
    private String dbServerIp;
    /**
     * 必须是数据库用户，否则执行disql会报错
     */
    @Value("${db.server.user:dmdba}")
    private String dbServerUser;
    @Value("${db.server.password:L90ad!12ki80}")
    private String dbServerPassword;
    @Value("${db.server.port:22}")
    private Integer dbServerPort;
    @Value("${db.server.shell.path:sh /home/<USER>/loan/bigdata_export.sh }")
    private String dbServerShellPath;
    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.BIG_DATA_SUPPLY;
    }

    @Override
    public void execBaseJob(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        try {
            SSHClient sshClient = new SSHClient(dbServerIp,dbServerUser,dbServerPassword,dbServerPort);
            int result = sshClient.exec(dbServerShellPath + " " + businessDate.intValue());
            if(result != 0){
                throw new Exception("大数据下档，脚本执行出现问题，请及时关注！");
            }
        } catch (Exception e) {
            log.error("大数据下档报错e={}",e);
            throw new HsjryRpcException("999999","脚本执行出现问题，请及时关注！");
        }
    }
}
