package com.hsjry.core.limit.batch.common.dto.file;

import java.io.Serializable;

import lombok.Builder;
import lombok.Data;

/**
 * 实体信息dto
 *
 * <AUTHOR>
 * @date 2025-07-09 12:31:05
 */
@Data
@Builder
public class LbCEntityInfoDto implements Serializable {
    private static final long serialVersionUID = 1942924465449140228L;
    /** 额度编号 */
    private String custLimitId;
    /** 预发放低风险金额编号 */
    private String preGrantLowRiskAmtId;
    /** 发放低风险 */
    private java.math.BigDecimal lowRisk;
    /** 发放低风险金额编号 */
    private String lowRiskAmountId;
    /** 低风险余额 */
    private java.math.BigDecimal leftLowRisk;
    /** 低风险余额金额编号 */
    private String leftLowRiskAmountId;
    /** 低风险币种 */
    private String lowRiskCurrency;
    /** 是否限额管控;EnumBool   Y-是、N-否 */
    private String amtLimitFlag;
    /** 产品编号 */
    private String productId;
    /** 预发放低风险 */
    private java.math.BigDecimal preGrantLowRisk;
    /** 所属对象编号 */
    private String limitObjectId;
    /** 实体唯一标识 */
    private String uniqueIdentifier;
    /** 更新汇率版本 */
    private java.math.BigDecimal nextExchangeRateVersion;
    /** 关联同业金融产品编号 */
    private String ibFinancialProdRelId;
    /** 所属对象类型 EnumLimitObjectType:001-客户、002-金融产品 */
    private String limitObjectType;
    /** 卖空标识;EnumBool   Y-是、N-否 */
    private String shortSellingFlag;
    /** 金额分配模式 EnumAmountAssignMode:001-主动分配、002-自动分配-按比例分配 */
    private String amountAssignMode;
    /** 汇率版本 */
    private java.math.BigDecimal exchangeRateVersion;
    /** 创建时间 */
    private java.util.Date createTime;
    /** 更新时间 */
    private java.util.Date updateTime;
    /** 实体编号 */
    private String entityId;
    /** 实体类型;EnumLimitCenterEntityType:001-借据、002-信用证、003-保函、004-垫款、005-贴现、006-贷款承诺 */
    private String entityType;
    /** 状态;EnumEntityStatus(010-未结清,020-结清,030-已冲账,040-已撤销) */
    private String status;
    /** 实体申请编号 */
    private String entityApplyId;
    /** 实体业务编号 */
    private String entityRelationId;
    /** 实体所属系统 */
    private String systemSign;
    /** 租户号 */
    private String tenantId;
    /** 额度汇率重选标记;EnumBool   Y-是、N-否 */
    private String custLimitExRateRecalFlag;
    /** 限额汇率重算标记;EnumBool   Y-是、N-否 */
    private String amtLimitExRateRecalFlag;
    /** 发放时间 */
    private java.util.Date grantDateTime;
    /** 预发放金额 */
    private java.math.BigDecimal preGrantAmount;
    /** 发放金额 */
    private java.math.BigDecimal amount;
    /** 当前余额 */
    private java.math.BigDecimal leftAmount;
    /** 金额币种 */
    private String amountCurrency;
}
