package com.hsjry.core.limit.batch.biz.impl.trigger;

import org.springframework.stereotype.Service;

import com.hsjry.core.limit.batch.biz.trigger.TriggerJobBiz;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.facade.dto.trigger.TriggerJobDto;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.DateUtil;
import com.xxl.job.core.biz.model.TriggerJobParam;
import com.xxl.job.core.executor.XxlJobExecutor;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version V3.0
 * @since 3.0.1 2021/3/1 15:54
 */
@Service
@Slf4j
public class TriggerJobBizImpl implements TriggerJobBiz {
    @Override
    public void executeTriggerJob(TriggerJobDto jobDto) {
        if (jobDto == null) {
            throw new HsjryBizException(EnumBatchJobError.TRIGGER_JOB_ID_IS_NULL.getCode(),
                EnumBatchJobError.TRIGGER_JOB_ID_IS_NULL.getDescription(), this.getClass());
        }
        TriggerJobParam jobParam = new TriggerJobParam();
        jobParam.setJobId(jobDto.getJobId());
        jobParam.setExecuteBizDate(DateUtil.getDate(DateUtil.getNowDate(), DateUtil.DATE_FORMAT_2));
        jobParam.setExecutorParam(jobDto.getExecutorParam());
        XxlJobExecutor.triggerJob(jobParam);
    }
}
