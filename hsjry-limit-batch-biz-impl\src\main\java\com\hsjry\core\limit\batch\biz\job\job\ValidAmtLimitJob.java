/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.job;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/6 14:50
 */
@Service
@Slf4j
public class ValidAmtLimitJob extends AbstractBaseBatchJob {
    @Autowired
    @Qualifier("validAmtLimitBizImpl")
    private BaseOrdinaryBiz baseOrdinaryBiz;

    @Override
    public BaseOrdinaryBiz getBaseOrdinaryBiz() {
        return baseOrdinaryBiz;
    }
}
