/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.entity;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 资金系统落地表日终业务额度使用情况文件数据实体
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/15 10:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LbSCptlBizLmtUseSttnData {

    /** 客户编号 */
    private String userId;

    /** 客户类型 */
    private String userType;

    /** 客户名称 */
    private String userName;

    /** 证件类型 */
    private String userCertificateKind;

    /** 证件号码 */
    private String userCertificateNo;

    /** 产品编号 */
    private String productId;

    /** 产品名称 */
    private String productName;

    /** 额度类型名称 */
    private String custLimitTypeName;

    /** 已用额度 */
    private BigDecimal usedAmount;
    /** 核心机构号 */
    private String coreInstNo;
} 