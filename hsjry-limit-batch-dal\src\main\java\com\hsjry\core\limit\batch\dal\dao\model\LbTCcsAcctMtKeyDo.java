package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 信用卡-中间表-信用卡额度信息主键
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
@Table(name = "lb_t_ccs_acct_mt")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LbTCcsAcctMtKeyDo implements Serializable {

    private static final long serialVersionUID = 1942415996337979414L;
    /** 账号 */
    @Id
    @Column(name = "xaccount")
    private Long xaccount;
    /** 银行 */
    @Id
    @Column(name = "bank")
    private Integer bank;
}