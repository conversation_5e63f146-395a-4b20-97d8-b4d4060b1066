package com.hsjry.core.limit.batch.dal.dao.mapper;

import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpComCrdtDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 额度中心-中间表-额度重算中对公客户中一般授信额度mapper
 *
 * <AUTHOR>
 * @date 2025-08-21 11:31:28
 */
public interface LbTReclcCorpComCrdtMapper extends CommonMapper<LbTReclcCorpComCrdtDo> {

    // ==================== 一般授信额度重算相关方法 ====================

    /**
     * 1.1.清空一般授信额度中间表
     */
    int truncateCommonCreditLimit();

    /**
     * 1.2.插入一般授信额度客户编号和额度编号
     */
    int insertCommonCreditLimit();

    /**
     * 1.3.更新一般授信额度中间表金额信息
     */
    int mergeCommonCreditLimitAmount();

    /**
     * 1.4.更新额度实例金额信息
     */
    int mergeCommonCreditLimitInstance();
}