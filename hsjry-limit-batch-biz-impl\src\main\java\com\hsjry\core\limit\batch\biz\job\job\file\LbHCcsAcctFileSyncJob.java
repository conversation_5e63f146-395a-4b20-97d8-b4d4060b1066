/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.job.file;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * 信用卡-历史表-第一币种贷记帐户文件的同步处理任务
 * 负责H_CCS_ACCT文件的批量同步处理
 * 
 * 🎯 核心职责：
 * - 作为H_CCS_ACCT文件同步作业的入口点
 * - 继承AbstractBaseBatchJob提供标准批处理能力
 * - 委托给LbHCcsAcctFileSyncBizImpl执行具体业务逻辑
 * - 支持分片处理，适应大数据量文件同步场景
 * 
 * 🔄 执行流程：
 * 1. 作业调度器触发任务执行
 * 2. AbstractBaseBatchJob提供基础批处理框架
 * 3. 委托给业务实现类处理文件同步逻辑
 * 4. 支持分片并行处理提升性能
 * 
 * 📊 适用场景：
 * - 信用卡历史表数据文件定时同步
 * - 大批量信用卡账户数据导入
 * - 数据仓库ETL处理中的源数据同步
 * - 系统间信用卡数据交换
 *
 * <AUTHOR>
 * @version V4.0.1
 * @since 4.0.1 2025/1/21
 */
@Slf4j
@Service("lbHCcsAcctFileSyncJob")
public class LbHCcsAcctFileSyncJob extends AbstractBaseBatchJob {
    
    /**
     * 构造函数
     * 记录Bean初始化完成日志，便于监控和排错
     */
    public LbHCcsAcctFileSyncJob() {
        log.info("LbHCcsAcctFileSyncJob Bean初始化完成");
    }

    /**
     * 注入信用卡历史表文件同步业务实现类
     * 使用@Qualifier确保注入正确的实现类
     */
    @Autowired
    @Qualifier("lbHCcsAcctFileSyncBizImpl")
    private BaseOrdinaryBiz lbHCcsAcctFileSyncBizImpl;

    /**
     * 获取基础业务逻辑对象
     * AbstractBaseBatchJob框架会调用此方法获取具体的业务实现
     * 
     * @return 信用卡历史表文件同步业务实现类
     */
    @Override
    public BaseOrdinaryBiz getBaseOrdinaryBiz() {
        return lbHCcsAcctFileSyncBizImpl;
    }
} 