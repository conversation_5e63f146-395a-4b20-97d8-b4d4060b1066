/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.core;

import java.util.Date;

import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/4/17 9:41
 */
public interface IDefinePathCore {
    /**
     * 获取本地路径
     *
     * @param jobTrade
     * @param date
     * @param tenantId
     * @return
     */
    String getLocalPath(EnumJobTrade jobTrade,String tenantId, Date date);

    /**
     * 获取远程路径
     *
     * @param jobTrade
     * @param date
     * @param tenantId
     * @return
     */
    String getRemotePath(EnumJobTrade jobTrade,String tenantId, Date date);
}
