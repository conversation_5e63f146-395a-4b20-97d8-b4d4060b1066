package com.hsjry.core.limit.batch.biz.convert.copy;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.hsjry.core.limit.batch.common.dto.file.LbCEntityOperateSerialDto;
import com.hsjry.core.limit.batch.dal.dao.model.LbCEntityOperateSerialDo;
import com.hsjry.core.limit.center.dal.dao.model.LcEntityOperateSerialDo;
import com.hsjry.lang.common.utils.DateUtil;
import com.hsjry.lang.common.utils.StringUtil;

/**
 * 实体操作流水转换类
 *
 * <AUTHOR>
 * @date 2025-07-09 12:31:05
 */
public class LbCEntityOperateSerialConverter {

    /**
     * DTO转DO
     *
     * @param dto DTO对象
     * @return DO对象
     */
    public static LbCEntityOperateSerialDo dtoToDo(LbCEntityOperateSerialDto dto) {
        return LbCEntityOperateSerialCnvs.INSTANCE.dtoToDo(dto);
    }

    /**
     * DO转DTO
     *
     * @param model DO对象
     * @return DTO对象
     */
    public static LbCEntityOperateSerialDto doToDto(LbCEntityOperateSerialDo model) {
        return LbCEntityOperateSerialCnvs.INSTANCE.do2Dto(model);
    }

    /**
     * DTO列表转DO列表
     *
     * @param dtoList DTO列表
     * @return DO列表
     */
    public static List<LbCEntityOperateSerialDo> dtoListToDoList(List<LbCEntityOperateSerialDto> dtoList) {
        if (dtoList == null) {
            return null;
        }

        List<LbCEntityOperateSerialDo> doList = Lists.newArrayList();
        for (LbCEntityOperateSerialDto dto : dtoList) {
            doList.add(dtoToDo(dto));
        }
        return doList;
    }

    /**
     * DO列表转DTO列表
     *
     * @param doList DO列表
     * @return DTO列表
     */
    public static List<LbCEntityOperateSerialDto> doListToDtoList(List<LbCEntityOperateSerialDo> doList) {
        if (doList == null) {
            return null;
        }

        List<LbCEntityOperateSerialDto> dtoList = Lists.newArrayList();
        for (LbCEntityOperateSerialDo model : doList) {
            dtoList.add(doToDto(model));
        }
        return dtoList;
    }

    /**
     * 字符串数组转DTO
     *
     * @param fields 字符串数组
     * @return DTO对象
     */
    public static LbCEntityOperateSerialDto fieldsToDto(String[] fields) {
        if (fields == null || fields.length == 0) {
            return null;
        }

        return LbCEntityOperateSerialDto.builder()
            .tenantId(safeGet(fields, 0))
            .leosSerialNo(safeGet(fields, 1))
            .systemSign(safeGet(fields, 2))
            .custNo(safeGet(fields, 3))
            .preGrantExpiryDateStr(safeGet(fields, 4))
            .failReason(safeGet(fields, 5))
            .operateStatus(safeGet(fields, 6))
            .operateType(safeGet(fields, 7))
            .lowRiskCurrency(safeGet(fields, 8))
            .lowRiskAmountId(safeGet(fields, 9))
            .lowRiskAmount(parseBigDecimal(safeGet(fields, 10)))
            .amountCurrency(safeGet(fields, 11))
            .amountId(safeGet(fields, 12))
            .amount(parseBigDecimal(safeGet(fields, 13)))
            .lastInboundSerialNo(safeGet(fields, 14))
            .globalSerialNo(safeGet(fields, 15))
            .entityRelationId(safeGet(fields, 16))
            .entityApplyId(safeGet(fields, 17))
            .entityId(safeGet(fields, 18))
            .updateTime(parseDate(safeGet(fields, 19)))
            .createTime(parseDate(safeGet(fields, 20)))
            .inboundSerialDatetime(parseDate(safeGet(fields, 21)))
            .inboundSerialNo(safeGet(fields, 22))
            .bizDatetime(parseDate(safeGet(fields, 23)))
            .channelNo(safeGet(fields, 24))
            .serialNo(safeGet(fields, 25))
            .build();
    }

    /**
     * 安全获取数组元素
     */
    private static String safeGet(String[] fields, int index) {
        if (fields.length > index) {
            return trimToNull(fields[index]);
        }
        return null;
    }

    /**
     * 字符串去空格并转换为null
     */
    private static String trimToNull(String str) {
        if (StringUtil.isBlank(str)) {
            return null;
        }
        return str.trim();
    }

    /**
     * 解析日期字符串
     */
    private static Date parseDate(String dateStr) {
        if (StringUtil.isBlank(dateStr)) {
            return null;
        }
        try {
            // 尝试多种日期格式
            if (dateStr.length() == 8) {
                return DateUtil.parseDate(dateStr, "yyyyMMdd");
            } else if (dateStr.length() == 14) {
                return DateUtil.parseDate(dateStr, "yyyyMMddHHmmss");
            } else if (dateStr.contains("-")) {
                return DateUtil.parseDate(dateStr, "yyyy-MM-dd HH:mm:ss");
            } else if (dateStr.contains("/")) {
                return DateUtil.parseDate(dateStr, "yyyy/MM/dd");
            }
        } catch (Exception e) {
            // 日期解析失败，返回null
        }
        return null;
    }

    /**
     * 解析BigDecimal
     */
    private static java.math.BigDecimal parseBigDecimal(String numStr) {
        if (StringUtil.isBlank(numStr)) {
            return null;
        }
        try {
            return new java.math.BigDecimal(numStr.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 源DO转目标DO（核心转换方法）
     * 从LcEntityOperateSerialDo转换为LbCEntityOperateSerialDo
     *
     * @param model 源DO对象
     * @return 目标DO对象
     */
    public static LbCEntityOperateSerialDo do2Copy(LcEntityOperateSerialDo model) {
        return LbCEntityOperateSerialCnvs.INSTANCE.do2Copy(model);
    }

    /**
     * 源DO列表转目标DO列表（核心转换方法）
     *
     * @param doList 源DO列表
     * @return 目标DO列表
     */
    public static List<LbCEntityOperateSerialDo> doList2CopyList(List<LcEntityOperateSerialDo> doList) {
        if (doList == null) {
            return Collections.emptyList();
        }
        return doList.stream()
            .map(LbCEntityOperateSerialConverter::do2Copy)
            .collect(Collectors.toList());
    }
}