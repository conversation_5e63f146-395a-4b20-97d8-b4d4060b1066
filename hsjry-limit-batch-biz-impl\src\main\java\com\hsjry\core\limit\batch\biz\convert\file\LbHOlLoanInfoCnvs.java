/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.convert.file;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import com.hsjry.core.limit.batch.biz.entity.LbHOlLoanInfoData;
import com.hsjry.core.limit.batch.dal.dao.model.LbHOlLoanInfoDo;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 网贷历史借据信息文件数据转换器
 * 使用MapStruct进行高性能对象映射
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/21 16:30
 */
@Mapper(componentModel = "spring")
public interface LbHOlLoanInfoCnvs {

    LbHOlLoanInfoCnvs INSTANCE = Mappers.getMapper(LbHOlLoanInfoCnvs.class);

    /**
     * 单个Data转DO
     * 使用MapStruct自动映射，处理特殊字段映射
     * 注意：loanPayTime字段如果在DO中不存在，会被忽略
     *
     * @param data 源数据对象
     * @return 目标DO对象
     */
    @Mapping(target = "createTime", source = "createTime", qualifiedByName = "stringToDate")
    @Mapping(target = "updateTime", source = "updateTime", qualifiedByName = "stringToDate")
    @Mapping(target = "loanStartTime", source = "loanStartTime", qualifiedByName = "stringToDate")
    @Mapping(target = "loanEndTime", source = "loanEndTime", qualifiedByName = "stringToDate")
    @Mapping(target = "settleDate", source = "settleDate", qualifiedByName = "stringToDate")
    LbHOlLoanInfoDo data2Do(LbHOlLoanInfoData data);

    /**
     * 单个DO转Data（反向转换）
     * 反向转换，用于数据回显或其他场景
     *
     * @param dataObject DO对象
     * @return Data对象
     */
    @Mapping(target = "createTime", source = "createTime", qualifiedByName = "dateToString")
    @Mapping(target = "updateTime", source = "updateTime", qualifiedByName = "dateToString")
    @Mapping(target = "loanStartTime", source = "loanStartTime", qualifiedByName = "dateToString")
    @Mapping(target = "loanEndTime", source = "loanEndTime", qualifiedByName = "dateToString")
    @Mapping(target = "settleDate", source = "settleDate", qualifiedByName = "dateToString")
    LbHOlLoanInfoData do2Data(LbHOlLoanInfoDo dataObject);

    /**
     * 批量Data转DO列表
     * 利用MapStruct的批量转换能力
     *
     * @param dataList 源数据列表
     * @return 目标DO列表
     */
    List<LbHOlLoanInfoDo> dataListToDoList(List<LbHOlLoanInfoData> dataList);

    /**
     * 批量DO转Data列表
     * 反向批量转换
     *
     * @param doList DO列表
     * @return Data列表
     */
    List<LbHOlLoanInfoData> doListToDataList(List<LbHOlLoanInfoDo> doList);

    /**
     * 字符串转日期
     * 支持多种日期格式，容错处理
     *
     * @param dateStr 日期字符串
     * @return Date对象
     */
    @Named("stringToDate")
    static Date stringToDate(String dateStr) {
        if (StringUtil.isBlank(dateStr)) {
            return null;
        }
        
        // 支持的日期格式
        String[] patterns = {
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd",
            "yyyyMMdd HHmmss",
            "yyyyMMdd",
            "yyyy/MM/dd HH:mm:ss",
            "yyyy/MM/dd"
        };
        
        for (String pattern : patterns) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                return sdf.parse(dateStr.trim());
            } catch (ParseException e) {
                // 继续尝试下一个格式
            }
        }
        
        return null;
    }

    /**
     * 日期转字符串
     * 使用标准格式输出
     *
     * @param date Date对象
     * @return 日期字符串
     */
    @Named("dateToString")
    static String dateToString(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }
} 