package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTIbnkCustInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTIbnkCustInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-同业客户信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbTIbnkCustInfoDao extends IBaseDao<LbTIbnkCustInfoDo> {
    /**
     * 分页查询额度中心-中间表-同业客户信息
     *
     * @param lbTIbnkCustInfoQuery 条件
     * @return PageInfo<LbTIbnkCustInfoDo>
     */
    PageInfo<LbTIbnkCustInfoDo> selectPage(LbTIbnkCustInfoQuery lbTIbnkCustInfoQuery, PageParam pageParam);

    /**
     * 根据key查询额度中心-中间表-同业客户信息
     *
     * @param custNo
     * @return
     */
    LbTIbnkCustInfoDo selectByKey(String custNo);

    /**
     * 根据key删除额度中心-中间表-同业客户信息
     *
     * @param custNo
     * @return
     */
    int deleteByKey(String custNo);

    /**
     * 查询额度中心-中间表-同业客户信息信息
     *
     * @param lbTIbnkCustInfoQuery 条件
     * @return List<LbTIbnkCustInfoDo>
     */
    List<LbTIbnkCustInfoDo> selectByExample(LbTIbnkCustInfoQuery lbTIbnkCustInfoQuery);

    /**
     * 新增额度中心-中间表-同业客户信息信息
     *
     * @param lbTIbnkCustInfo 条件
     * @return int>
     */
    int insertBySelective(LbTIbnkCustInfoDo lbTIbnkCustInfo);

    /**
     * 修改额度中心-中间表-同业客户信息信息
     *
     * @param lbTIbnkCustInfo
     * @return
     */
    int updateBySelective(LbTIbnkCustInfoDo lbTIbnkCustInfo);

    /**
     * 修改额度中心-中间表-同业客户信息信息
     *
     * @param lbTIbnkCustInfo
     * @param lbTIbnkCustInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTIbnkCustInfoDo lbTIbnkCustInfo, LbTIbnkCustInfoQuery lbTIbnkCustInfoQuery);

    /**
     * 清空同业客户信息表数据
     * 
     * @return 影响行数
     */
    int truncateTable();

    /**
     * 从源表LC_CUST_LIMIT_OBJECT_INFO导入同业客户信息数据
     * 
     * @return 影响行数
     */
    int insertFromSource();

    /**
     * 根据客户编号列表从源表LC_CUST_LIMIT_OBJECT_INFO导入同业客户信息数据
     *
     * @param userIdList 客户编号列表
     * @return 影响行数
     */
    int insertFromSource(List<String> userIdList);
}
