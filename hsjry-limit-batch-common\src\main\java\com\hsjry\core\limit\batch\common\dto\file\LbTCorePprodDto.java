package com.hsjry.core.limit.batch.common.dto.file;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * [核心系统-落地表-产品定义表]Dto
 *
 * <AUTHOR>
 * @version V3.0.5
 * @since 2025/7/315:39
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LbTCorePprodDto implements Serializable {
    private static final long serialVersionUID = -8985151400912122687L;

    /**
     * 法人代码
     */
    private String faredm;
    /**
     * 产品编号
     */
    private String chapbh;
    /**
     * 产品描述
     */
    private String chapmx;
    /**
     * 模块
     */
    private String module;
    /**
     * 维护日期
     */
    private String weihrq;
    /**
     * 维护时间
     */
    private BigDecimal weihsj;
    /**
     * 维护柜员
     */
    private String weihgy;
    /**
     * 维护机构
     */
    private String weihjg;
    /**
     * 序列号
     */
    private String rowidd;
    /**
     * 时间戳
     */
    private BigDecimal shjnch;
    /**
     * 记录状态
     */
    private String jiluzt;
}
