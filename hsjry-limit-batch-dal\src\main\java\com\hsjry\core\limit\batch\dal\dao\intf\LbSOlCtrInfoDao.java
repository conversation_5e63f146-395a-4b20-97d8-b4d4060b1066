package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbSOlCtrInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSOlCtrInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 网贷系统-历史表-合同信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbSOlCtrInfoDao extends IBaseDao<LbSOlCtrInfoDo> {
    /**
     * 分页查询网贷系统-历史表-合同信息
     *
     * @param lbSOlCtrInfoQuery 条件
     * @return PageInfo<LbSOlCtrInfoDo>
     */
    PageInfo<LbSOlCtrInfoDo> selectPage(LbSOlCtrInfoQuery lbSOlCtrInfoQuery, PageParam pageParam);

    /**
     * 根据key查询网贷系统-历史表-合同信息
     *
     * @param contractId
     * @return
     */
    LbSOlCtrInfoDo selectByKey(String contractId);

    /**
     * 根据key删除网贷系统-历史表-合同信息
     *
     * @param contractId
     * @return
     */
    int deleteByKey(String contractId);

    /**
     * 查询网贷系统-历史表-合同信息信息
     *
     * @param lbSOlCtrInfoQuery 条件
     * @return List<LbSOlCtrInfoDo>
     */
    List<LbSOlCtrInfoDo> selectByExample(LbSOlCtrInfoQuery lbSOlCtrInfoQuery);

    /**
     * 新增网贷系统-历史表-合同信息信息
     *
     * @param lbSOlCtrInfo 条件
     * @return int>
     */
    int insertBySelective(LbSOlCtrInfoDo lbSOlCtrInfo);

    /**
     * 修改网贷系统-历史表-合同信息信息
     *
     * @param lbSOlCtrInfo
     * @return
     */
    int updateBySelective(LbSOlCtrInfoDo lbSOlCtrInfo);

    /**
     * 修改网贷系统-历史表-合同信息信息
     *
     * @param lbSOlCtrInfo
     * @param lbSOlCtrInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbSOlCtrInfoDo lbSOlCtrInfo, LbSOlCtrInfoQuery lbSOlCtrInfoQuery);

    /**
     * 批量插入网贷系统合同信息-落地信息
     *
     * @param lbSOlCtrInfoList 批量数据
     * @return int
     */
    @Override
    int insertList(List<LbSOlCtrInfoDo> lbSOlCtrInfoList);

    /**
     * 清空网贷系统合同信息-落地所有数据
     *
     * @return int
     */
    int deleteAll();

    /**
     * 获取第一个对象，用于分片查询
     * 根据复合主键排序，获取指定偏移量的第一条记录
     *
     * @param query 查询条件
     * @return 第一条记录，如果没有则返回null
     */
    LbSOlCtrInfoDo selectFirstOne(LbSOlCtrInfoQuery query);

    /**
     * 查询当前分片主键范围内的数据总数
     * 根据复合主键范围统计当前分片的数据量
     *
     * @param query 查询条件，需包含主键范围
     * @return 当前分片的数据量
     */
    Integer selectCountByCurrentGroup(LbSOlCtrInfoQuery query);

    /**
     * 查询分片数据列表
     * 支持offset/limit分页查询
     *
     * @param query 查询条件，包含offset和limit
     * @return 分片数据列表
     */
    List<LbSOlCtrInfoDo> selectShardList(LbSOlCtrInfoQuery query);
}
