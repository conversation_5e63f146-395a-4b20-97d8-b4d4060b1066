package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpCprsvCrdtDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcCorpCprsvCrdtQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中对公客户中综合授信额度数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-08-21 11:31:28
 */
public interface LbTReclcCorpCprsvCrdtDao extends IBaseDao<LbTReclcCorpCprsvCrdtDo> {
    /**
     * 分页查询额度中心-中间表-额度重算中对公客户中综合授信额度
     *
     * @param lbTReclcCorpCprsvCrdtQuery 条件
     * @return PageInfo<LbTReclcCorpCprsvCrdtDo>
     */
    PageInfo<LbTReclcCorpCprsvCrdtDo> selectPage(LbTReclcCorpCprsvCrdtQuery lbTReclcCorpCprsvCrdtQuery,
        PageParam pageParam);

    /**
     * 根据key查询额度中心-中间表-额度重算中对公客户中综合授信额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    LbTReclcCorpCprsvCrdtDo selectByKey(String custNo, String custLimitId);

    /**
     * 根据key删除额度中心-中间表-额度重算中对公客户中综合授信额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custNo, String custLimitId);

    /**
     * 查询额度中心-中间表-额度重算中对公客户中综合授信额度信息
     *
     * @param lbTReclcCorpCprsvCrdtQuery 条件
     * @return List<LbTReclcCorpCprsvCrdtDo>
     */
    List<LbTReclcCorpCprsvCrdtDo> selectByExample(LbTReclcCorpCprsvCrdtQuery lbTReclcCorpCprsvCrdtQuery);

    /**
     * 新增额度中心-中间表-额度重算中对公客户中综合授信额度信息
     *
     * @param lbTReclcCorpCprsvCrdt 条件
     * @return int>
     */
    int insertBySelective(LbTReclcCorpCprsvCrdtDo lbTReclcCorpCprsvCrdt);

    /**
     * 修改额度中心-中间表-额度重算中对公客户中综合授信额度信息
     *
     * @param lbTReclcCorpCprsvCrdt
     * @return
     */
    int updateBySelective(LbTReclcCorpCprsvCrdtDo lbTReclcCorpCprsvCrdt);

    /**
     * 修改额度中心-中间表-额度重算中对公客户中综合授信额度信息
     *
     * @param lbTReclcCorpCprsvCrdt
     * @param lbTReclcCorpCprsvCrdtQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTReclcCorpCprsvCrdtDo lbTReclcCorpCprsvCrdt,
        LbTReclcCorpCprsvCrdtQuery lbTReclcCorpCprsvCrdtQuery);

    // ==================== 综合授信额度重算相关方法 ====================

    /**
     * 2.1.清空综合授信额度中间表
     */
    int truncateComprehensiveCreditLimit();

    /**
     * 2.2.插入综合授信额度客户编号和额度编号
     */
    int insertComprehensiveCreditLimit();

    /**
     * 2.3.更新综合授信额度中间表金额信息
     */
    int mergeComprehensiveCreditLimitAmount();

    /**
     * 2.4.更新额度实例金额信息
     */
    int mergeComprehensiveCreditLimitInstance();
}
