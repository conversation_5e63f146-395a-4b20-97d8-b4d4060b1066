package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIndvConsmLoanDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcIndvConsmLoanQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中个人额度中消费贷额度数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-08-21 13:10:51
 */
public interface LbTReclcIndvConsmLoanDao extends IBaseDao<LbTReclcIndvConsmLoanDo> {
    /**
     * 分页查询额度中心-中间表-额度重算中个人额度中消费贷额度
     *
     * @param lbTReclcIndvConsmLoanQuery 条件
     * @return PageInfo<LbTReclcIndvConsmLoanDo>
     */
    PageInfo<LbTReclcIndvConsmLoanDo> selectPage(LbTReclcIndvConsmLoanQuery lbTReclcIndvConsmLoanQuery,
        PageParam pageParam);

    /**
     * 根据key查询额度中心-中间表-额度重算中个人额度中消费贷额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    LbTReclcIndvConsmLoanDo selectByKey(String custNo, String custLimitId);

    /**
     * 根据key删除额度中心-中间表-额度重算中个人额度中消费贷额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custNo, String custLimitId);

    /**
     * 查询额度中心-中间表-额度重算中个人额度中消费贷额度信息
     *
     * @param lbTReclcIndvConsmLoanQuery 条件
     * @return List<LbTReclcIndvConsmLoanDo>
     */
    List<LbTReclcIndvConsmLoanDo> selectByExample(LbTReclcIndvConsmLoanQuery lbTReclcIndvConsmLoanQuery);

    /**
     * 新增额度中心-中间表-额度重算中个人额度中消费贷额度信息
     *
     * @param lbTReclcIndvConsmLoan 条件
     * @return int>
     */
    int insertBySelective(LbTReclcIndvConsmLoanDo lbTReclcIndvConsmLoan);

    /**
     * 修改额度中心-中间表-额度重算中个人额度中消费贷额度信息
     *
     * @param lbTReclcIndvConsmLoan
     * @return
     */
    int updateBySelective(LbTReclcIndvConsmLoanDo lbTReclcIndvConsmLoan);

    /**
     * 修改额度中心-中间表-额度重算中个人额度中消费贷额度信息
     *
     * @param lbTReclcIndvConsmLoan
     * @param lbTReclcIndvConsmLoanQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTReclcIndvConsmLoanDo lbTReclcIndvConsmLoan,
        LbTReclcIndvConsmLoanQuery lbTReclcIndvConsmLoanQuery);

    // ==================== 消费贷额度重算相关方法 ====================

    /**
     * 2.1.清空消费贷额度中间表
     */
    int truncateConsmLoanLimit();

    /**
     * 2.2.插入消费贷额度客户编号和额度编号
     */
    int insertConsmLoanLimit();

    /**
     * 2.3.更新消费贷额度中间表金额信息
     */
    int mergeConsmLoanLimitAmount();

    /**
     * 2.4.更新额度实例金额信息
     */
    int mergeConsmLoanLimitInstance();
}
