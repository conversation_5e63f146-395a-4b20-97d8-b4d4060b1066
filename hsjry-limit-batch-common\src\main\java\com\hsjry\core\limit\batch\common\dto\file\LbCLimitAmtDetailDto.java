package com.hsjry.core.limit.batch.common.dto.file;

import java.io.Serializable;

import lombok.Builder;
import lombok.Data;

/**
 * 金额明细dto
 *
 * <AUTHOR>
 * @date 2025-07-09 12:31:05
 */
@Data
@Builder
public class LbCLimitAmtDetailDto implements Serializable {
    private static final long serialVersionUID = 1942924465449140224L;
    /** 租户号 */
    private String tenantId;
    /** 创建时间 */
    private java.util.Date createTime;
    /** 更新时间 */
    private java.util.Date updateTime;
    /** 金额编号 */
    private String amountId;
    /** 序号 */
    private Integer seq;
    /** 金额 */
    private java.math.BigDecimal amount;
    /** 币种 */
    private String currency;
    /** 客户编号 */
    private String custNo;
}
