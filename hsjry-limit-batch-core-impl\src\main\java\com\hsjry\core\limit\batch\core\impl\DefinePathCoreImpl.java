/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.core.impl;

import java.util.Date;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.dto.EnumJobError;
import com.hsjry.base.common.job.dto.EnumPathType;
import com.hsjry.base.common.job.util.JobUtil;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.core.IDefinePathCore;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.exception.HsjryCheckException;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/4/17 9:42
 */
@Slf4j
@Service
public class DefinePathCoreImpl implements IDefinePathCore {
    /** 远程父级路径 */
    @Value("${remote.parent.path:/hsjry/limit}")
    private String remoteParentPath;
    /** 本地父级路径 */
    @Value("${local.parent.path:/hsjry}")
    private String localParentPath;
    @Value("${limit.local.temporary.files.reconcile:/reconcile/{tenantId}/{yyyyMMdd}}")
    private String localShardFileWriteDir;
    @Value("${limit.remote.temporary.files.reconcile:/reconcile/{tenantId}/{yyyyMMdd}}")
    private String remoteAppMergeFileTmpDir;

    @Override
    public String getLocalPath(EnumJobTrade jobTrade, String tenantId, Date date) {
        String path;
        switch (jobTrade) {
            case INBOUND_FILE:
                path = localShardFileWriteDir;
                break;
            default:
                path = null;
        }
        //检查路径,如果未配置抛异常
        if (StringUtil.isBlank(path)) {
            log.error("{},未配置文件路径", jobTrade.getDescription());
            throw new HsjryBizException(EnumBatchJobError.FILE_PATH_NOT_EXIST.getCode(),
                EnumBatchJobError.FILE_PATH_NOT_EXIST.getDescription(), this.getClass());
        }

        return JobUtil.pathFormat(path, localParentPath, tenantId, date, EnumPathType.LOCAL);
    }

    @Override
    public String getRemotePath(EnumJobTrade jobTrade, String tenantId, Date date) {
        String path;
        switch (jobTrade) {
            case INBOUND_FILE:
                path = remoteAppMergeFileTmpDir;
                break;
            default:
                path = null;
        }
        //检查路径,如果未配置抛异常
        if (StringUtil.isBlank(path)) {
            log.error("{},未配置文件路径", jobTrade.getDescription());
            throw new HsjryBizException(EnumBatchJobError.FILE_PATH_NOT_EXIST.getCode(),
                EnumBatchJobError.FILE_PATH_NOT_EXIST.getDescription(), this.getClass());
        }
        return JobUtil.pathFormat(path, remoteParentPath, tenantId, date, EnumPathType.REMOTE);
    }
}
