// package com.hsjry.core.limit.batch.biz.job.sharding.biz.limit;
//
// import java.math.BigDecimal;
// import java.util.ArrayList;
// import java.util.List;
// import java.util.Map;
// import java.util.concurrent.TimeUnit;
// import java.util.stream.Collectors;
//
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.beans.factory.annotation.Value;
// import org.springframework.data.redis.core.RedisTemplate;
// import org.springframework.stereotype.Service;
// import org.springframework.util.CollectionUtils;
//
// import com.google.common.collect.Lists;
// import com.hsjry.base.common.job.dto.IEnumTrade;
// import com.hsjry.base.common.job.dto.JobInitDto;
// import com.hsjry.base.common.job.dto.JobShared;
// import com.hsjry.base.common.model.enums.limit.EnumCustLimitStatus;
// import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
// import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
// import com.hsjry.core.limit.batch.common.constants.LimitBatchConstants;
// import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
// import com.hsjry.core.limit.center.dal.dao.intf.CustLimitInfoBatchDao;
// import com.hsjry.core.limit.batch.dal.dao.model.LcSliceBatchSerialDo;
// import com.hsjry.core.limit.center.dal.dao.query.CustLimitInfoQuery;
// import com.hsjry.core.limit.center.common.constants.LimitCenterConstants;
// import com.hsjry.core.limit.center.core.ICustLimitQueryCore;
// import com.hsjry.core.limit.center.core.bo.CustLimitBo;
// import com.hsjry.core.limit.center.core.bo.OperateAmountBo;
// import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoDo;
// import com.hsjry.lang.common.utils.CollectionUtil;
// import com.hsjry.lang.common.utils.GsonUtil;
// import com.hsjry.lang.common.utils.StringUtil;
//
// import lombok.extern.slf4j.Slf4j;
//
// /**
//  * 统计机构额度
//  *
//  * <AUTHOR>
//  * @version V4.0
//  * @since 4.0 2024/1/12 14:21
//  */
// @Service
// @Slf4j
// public class StatisticsOrganLimitPrepareImpl extends AbstractShardingPrepareBiz<CustLimitInfoQuery>
//     implements JobCoreBusiness<LcCustLimitInfoDo> {
//     @Autowired
//     private CustLimitInfoBatchDao custLimitInfoBatchDao;
//     @Autowired
//     private ICustLimitQueryCore iCustLimitQueryCore;
//     @Autowired
//     private RedisTemplate<String, Long> redisTemplate;
//
//     /** 总额度节点编号 */
//     @Value("${statistics.total.limit.node.id:DGLN0001}")
//     private String totalLimitNodeId;
//     /** 授信额度节点编号 */
//     @Value("${statistics.credit.limit.node.id:DGLN0002}")
//     private String creditLimitNodeId;
//     /** 非授信节点编号 */
//     @Value("${statistics.non.credit.limit.node.id:DGLN0003}")
//     private String nonCreditLimitNodeId;
//     /** 合作方节点编号 */
//     @Value("${statistics.other.limit.node.id:DGLN0004}")
//     private String otherLimitNodeId;
//
//     @Override
//     public Integer selectCountByCurrentGroupFromDb(CustLimitInfoQuery query) {
//         return custLimitInfoBatchDao.selectNodeCountByCurrentGroup(query);
//     }
//
//     @Override
//     public IEnumTrade getJobTrade() {
//         return EnumJobTrade.STAT_ORGAN_LIMIT_PRE;
//     }
//
//     @Override
//     public ShardingResult<LcCustLimitInfoDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
//         JobInitDto jobInitDto, JobShared jobShared) {
//         ShardingResult<LcCustLimitInfoDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
//         if (StringUtil.isBlank(jobShared.getExtParam())) {
//             return shardingResult;
//         }
//         //原始查询条件
//         Integer batchFixNum = jobInitDto.getFixNum();
//         CustLimitInfoQuery limitInfoQuery = GsonUtil.json2Obj(jobShared.getExtParam(), CustLimitInfoQuery.class);
//         CustLimitInfoQuery query = CustLimitInfoQuery.builder()
//             .offset(batchFixNum * (lcSliceBatchSerialDo.getBatchNum() - 1))
//             .limit(jobShared.getLimit())
//             .limitStatusList(limitInfoQuery.getLimitStatusList())
//             .templateNodeIdList(limitInfoQuery.getTemplateNodeIdList())
//             .build();
//         log.info("=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");
//         List<LcCustLimitInfoDo> list = custLimitInfoBatchDao.selectNodeShardList(query);
//         shardingResult.setShardingResultList(list);
//         return shardingResult;
//     }
//
//     @Override
//     public void execJobCoreBusiness(ShardingResult<LcCustLimitInfoDo> shardingResult) {
//         List<LcCustLimitInfoDo> custLimitInfoDoList = shardingResult.getShardingResultList();
//         if (CollectionUtils.isEmpty(custLimitInfoDoList)) {
//             log.info("=========分片执行结束:" + shardingResult.getJobShared()
//                 .getBatchNum() + "数量为空=========");
//             return;
//         }
//         List<String> custLimitIdList = custLimitInfoDoList.stream()
//             .map(LcCustLimitInfoDo::getCustLimitId)
//             .collect(Collectors.toList());
//         List<CustLimitBo> custLimitBoList = iCustLimitQueryCore.queryCustLimitBoList(custLimitIdList);
//         if (CollectionUtil.isEmpty(custLimitBoList)) {
//             return;
//         }
//         Map<String, CustLimitBo> custLimitBoMap = custLimitBoList.stream()
//             .collect(Collectors.toMap(CustLimitBo::getCustLimitId, o -> o));
//         for (LcCustLimitInfoDo lcCustLimitInfoDo : custLimitInfoDoList) {
//             handle(custLimitBoMap.get(lcCustLimitInfoDo.getCustLimitId()));
//         }
//         //更新分片流水成功
//         normalUpdateSliceSerial(custLimitInfoDoList.size(), shardingResult.getLcSliceBatchSerialDo());
//     }
//
//     /**
//      * 处理
//      *
//      * @param custLimitBo
//      */
//     private void handle(CustLimitBo custLimitBo) {
//         if (StringUtil.isBlank(custLimitBo.getOwnOrganId())) {
//             return;
//         }
//         BigDecimal totalAmount = OperateAmountBo.builder()
//             .amount(custLimitBo.getCustLimitAmountBo()
//                 .getTotalAmount())
//             .currency(custLimitBo.getCustLimitAmountBo()
//                 .getCurrency())
//             .build()
//             .getOperateAmount(LimitCenterConstants.DEFAULT_CURRENCY);
//         BigDecimal availableAmount = OperateAmountBo.builder()
//             .amount(custLimitBo.getCustLimitAmountBo()
//                 .availableAmount())
//             .currency(custLimitBo.getCustLimitAmountBo()
//                 .getCurrency())
//             .build()
//             .getOperateAmount(LimitCenterConstants.DEFAULT_CURRENCY);
//         // 总额度
//         String totalAmountKey = getRedisKey(custLimitBo, LimitBatchConstants.STATISTICS_TOTAL_AMOUNT);
//         Long totalAmountLong = bigDecimal2Long(totalAmount);
//         redisTemplate.opsForValue()
//             .increment(totalAmountKey, totalAmountLong);
//         redisTemplate.expire(totalAmountKey, 12, TimeUnit.HOURS);
//         redisTemplate.opsForHash()
//             .put(LimitBatchConstants.STATISTICS_TOTAL_AMOUNT, totalAmountKey, totalAmountKey);
//         redisTemplate.expire(LimitBatchConstants.STATISTICS_TOTAL_AMOUNT, 12, TimeUnit.HOURS);
//         // 可用总额度
//         String availableAmountKey = getRedisKey(custLimitBo, LimitBatchConstants.STATISTICS_AVAILABLE_AMOUNT);
//         Long availableAmountLong = bigDecimal2Long(availableAmount);
//         redisTemplate.opsForValue()
//             .increment(availableAmountKey, availableAmountLong);
//         redisTemplate.expire(availableAmountKey, 12, TimeUnit.HOURS);
//         redisTemplate.opsForHash()
//             .put(LimitBatchConstants.STATISTICS_AVAILABLE_AMOUNT, availableAmountKey, availableAmountKey);
//         redisTemplate.expire(LimitBatchConstants.STATISTICS_AVAILABLE_AMOUNT, 12, TimeUnit.HOURS);
//         if (custLimitBo.getNodeId()
//             .equals(creditLimitNodeId)) {
//             BigDecimal lowRiskAmount = custLimitBo.getCustLimitAmountBo()
//                 .getLowRiskAmount()
//                 .getOperateAmount(LimitCenterConstants.DEFAULT_CURRENCY);
//             BigDecimal availableLowRisk = OperateAmountBo.builder()
//                 .amount(custLimitBo.getCustLimitAmountBo()
//                     .availableLowRisk())
//                 .currency(custLimitBo.getCustLimitAmountBo()
//                     .getLowRiskCurrency())
//                 .build()
//                 .getOperateAmount(LimitCenterConstants.DEFAULT_CURRENCY);
//             // 低风险
//             String lowRiskKey = getRedisKey(custLimitBo, LimitBatchConstants.STATISTICS_LOW_RISK_AMOUNT);
//             Long lowRiskLong = bigDecimal2Long(lowRiskAmount);
//             redisTemplate.opsForValue()
//                 .increment(lowRiskKey, lowRiskLong);
//             redisTemplate.expire(lowRiskKey, 12, TimeUnit.HOURS);
//             redisTemplate.opsForHash()
//                 .put(LimitBatchConstants.STATISTICS_LOW_RISK_AMOUNT, lowRiskKey, lowRiskKey);
//             redisTemplate.expire(LimitBatchConstants.STATISTICS_LOW_RISK_AMOUNT, 12, TimeUnit.HOURS);
//             // 可用低风险
//             String availableLowRiskKey = getRedisKey(custLimitBo, LimitBatchConstants.STATISTICS_AVAILABLE_LOW_RISK);
//             Long availableLowRiskLong = bigDecimal2Long(availableLowRisk);
//             redisTemplate.opsForValue()
//                 .increment(availableLowRiskKey, availableLowRiskLong);
//             redisTemplate.expire(availableLowRiskKey, 12, TimeUnit.HOURS);
//             redisTemplate.opsForHash()
//                 .put(LimitBatchConstants.STATISTICS_AVAILABLE_LOW_RISK, availableLowRiskKey, availableLowRiskKey);
//             redisTemplate.expire(LimitBatchConstants.STATISTICS_AVAILABLE_LOW_RISK, 12, TimeUnit.HOURS);
//         }
//     }
//
//     /**
//      * 组装redisKey
//      *
//      * @param custLimitBo
//      * @return
//      */
//     private String getRedisKey(CustLimitBo custLimitBo, String amountKey) {
//         return custLimitBo.getOwnOrganId() + "_" + custLimitBo.getNodeId() + "_" + amountKey;
//     }
//
//     /**
//      * bigdecimal2Long *10000
//      *
//      * @param number
//      * @return
//      */
//     public static Long bigDecimal2Long(BigDecimal number) {
//         if (null == number) {
//             return 0L;
//         }
//         return number.multiply(LimitBatchConstants.TEN_THOUSAND)
//             .longValue();
//     }
//
//     @Override
//     public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
//         log.info("====================== 接入业务{}分片逻辑 start ================================================",
//             getJobTrade().getDescription());
//         List<JobShared> jobSharedList = new ArrayList<>();
//         //sql 批处理数量 暂定为分片数量，不放大
//         Integer batchFixNum = jobInitDto.getFixNum();
//         //当前分组的最大值，为下次 批处理的最小值
//         LcCustLimitInfoDo maxLimitInfoDo = new LcCustLimitInfoDo();
//         //构造查询条件 查询当前 分批处理的 排序 最大 对象
//         CustLimitInfoQuery query = CustLimitInfoQuery.builder()
//             .limitStatusList(
//                 Lists.newArrayList(EnumCustLimitStatus.VALID.getCode(), EnumCustLimitStatus.FROZEN.getCode(),
//                     EnumCustLimitStatus.BREAK.getCode(), EnumCustLimitStatus.EXPIRE.getCode()))
//             .templateNodeIdList(
//                 Lists.newArrayList(totalLimitNodeId, creditLimitNodeId, nonCreditLimitNodeId, otherLimitNodeId))
//             .offset(batchFixNum - 1)
//             .limit(1)
//             .build();
//         //分片流水
//         int batchNum = 0;
//         while (maxLimitInfoDo != null) {
//             query.setCustLimitId(maxLimitInfoDo.getCustLimitId());
//             maxLimitInfoDo = custLimitInfoBatchDao.selectNodeFirstOne(query);
//             //统计分片 数量
//             batchNum = countBatchNum(batchFixNum, query, maxLimitInfoDo, batchNum, jobInitDto, jobSharedList,
//                 query.getCustLimitId(), false);
//         }
//         log.info("====================== 接入业务{}分片逻辑 end ================================================",
//             getJobTrade().getDescription());
//         return jobSharedList;
//     }
//
// }
