package com.hsjry.core.limit.batch.biz.convert.copy;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.hsjry.core.limit.batch.common.dto.file.LbCLimitOperateSerialDto;
import com.hsjry.core.limit.batch.dal.dao.model.LbCLimitOperateSerialDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitOperateSerialDo;
import com.hsjry.lang.common.utils.DateUtil;
import com.hsjry.lang.common.utils.StringUtil;

/**
 * 额度操作流水转换类
 *
 * <AUTHOR>
 * @date 2025-07-09 12:31:05
 */
public class LbCLimitOperateSerialConverter {
    /**
     * DTO转DO
     *
     * @param dto DTO对象
     * @return DO对象
     */
    public LbCLimitOperateSerialDo dtoToDo(LbCLimitOperateSerialDto dto) {
        if (dto == null) {
            return null;
        }
        return LbCLimitOperateSerialCnvs.INSTANCE.dtoToDo(dto);
    }

    /**
     * DO转DTO
     *
     * @param limitObjectInfoDo DO对象
     * @return DTO对象
     */
    public LbCLimitOperateSerialDto doToDto(LbCLimitOperateSerialDo limitObjectInfoDo) {
        if (limitObjectInfoDo == null) {
            return null;
        }
        return LbCLimitOperateSerialCnvs.INSTANCE.do2Dto(limitObjectInfoDo);
    }

    /**
     * DTO列表转DO列表
     *
     * @param dtoList DTO列表
     * @return DO列表
     */
    public List<LbCLimitOperateSerialDo> dtoListToDoList(List<LbCLimitOperateSerialDto> dtoList) {
        if (dtoList == null) {
            return null;
        }

        List<LbCLimitOperateSerialDo> doList = new ArrayList<>(dtoList.size());
        for (LbCLimitOperateSerialDto dto : dtoList) {
            doList.add(dtoToDo(dto));
        }
        return doList;
    }

    /**
     * DO列表转DTO列表
     *
     * @param doList DO列表
     * @return DTO列表
     */
    public List<LbCLimitOperateSerialDto> doListToDtoList(List<LbCLimitOperateSerialDo> doList) {
        if (doList == null) {
            return null;
        }

        List<LbCLimitOperateSerialDto> dtoList = new ArrayList<>(doList.size());
        for (LbCLimitOperateSerialDo limitInfoDo : doList) {
            dtoList.add(doToDto(limitInfoDo));
        }
        return dtoList;
    }

    /**
     * 从字符串数组创建DTO (根据CSV文件头字段顺序)
     *
     * @param fields 字段数组
     * @return DTO对象
     */
    public LbCLimitOperateSerialDto fieldsToDto(String[] fields) {
        if (fields == null || fields.length == 0) {
            return null;
        }
        return LbCLimitOperateSerialDto.builder()
            .tenantId(safeGet(fields, 0))
            .createTime(parseDate(fields[1]))
            .updateTime(parseDate(fields[2]))
            .operateAmountId(safeGet(fields, 3))
            .custNo(safeGet(fields, 4))
            .operatePath(safeGet(fields, 5))
            .contractRecalFlag(safeGet(fields, 6))
            .exchangeRateVersion(parseBigDecimal(safeGet(fields, 7)))
            .entityId(safeGet(fields, 8))
            .remark(safeGet(fields, 9))
            .failReason(safeGet(fields, 10))
            .operateDirection(safeGet(fields, 11))
            .custLimitId(safeGet(fields, 12))
            .lastInboundSerialNo(safeGet(fields, 13))
            .relationId(safeGet(fields, 14))
            .operateLowRiskCurrency(safeGet(fields, 15))
            .operateLowRiskAmtId(safeGet(fields, 16))
            .operateLowRiskAmount(parseBigDecimal(safeGet(fields, 17)))
            .operateAmountCurrency(safeGet(fields, 18))
            .globalSerialNo(safeGet(fields, 19))
            .operateAmount(parseBigDecimal(safeGet(fields, 20)))
            .operateType(safeGet(fields, 21))
            .ownOrganId(safeGet(fields, 22))
            .operatorId(safeGet(fields, 23))
            .status(safeGet(fields, 24))
            .closSerialNo(safeGet(fields, 25))
            .inboundSerialDatetime(parseDate(fields[26]))
            .inboundSerialNo(safeGet(fields, 27))
            .bizDatetime(parseDate(fields[28]))
            .channelNo(safeGet(fields, 29))
            .serialNo(safeGet(fields, 30))
            .build();
    }

    /**
     * 安全获取数组元素
     */
    private String safeGet(String[] fields, int index) {
        if (fields.length > index) {
            return trimToNull(fields[index]);
        }
        return null;
    }

    /**
     * 字符串去空格并转换为null
     */
    private String trimToNull(String str) {
        return StringUtil.trimToNull(str);
    }

    /**
     * 解析日期字符串
     */
    private Date parseDate(String dateStr) {
        if (StringUtil.isBlank(dateStr)) {
            return null;
        }
        try {
            // 尝试多种日期格式
            if (dateStr.length() == 8) {
                return DateUtil.parseDate(dateStr, "yyyyMMdd");
            } else if (dateStr.length() == 14) {
                return DateUtil.parseDate(dateStr, "yyyyMMddHHmmss");
            } else if (dateStr.contains("-")) {
                return DateUtil.parseDate(dateStr, "yyyy-MM-dd");
            } else if (dateStr.contains("/")) {
                return DateUtil.parseDate(dateStr, "yyyy/MM/dd");
            }
        } catch (Exception e) {
            // 日期解析失败，返回null
        }
        return null;
    }

    /**
     * 解析BigDecimal
     */
    private BigDecimal parseBigDecimal(String numStr) {
        if (StringUtil.isBlank(numStr)) {
            return null;
        }
        try {
            return new BigDecimal(numStr.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * LcCustLimitOperateSerialDo转LbCLimitOperateSerialDo
     *
     * @param lcCustLimitOperateSerialDo 源DO对象
     * @return 目标DO对象
     */
    public LbCLimitOperateSerialDo objectInfoDoToInfoDo(LcCustLimitOperateSerialDo lcCustLimitOperateSerialDo) {
        if (lcCustLimitOperateSerialDo == null) {
            return null;
        }
        return LbCLimitOperateSerialCnvs.INSTANCE.do2Copy(lcCustLimitOperateSerialDo);
    }

    /**
     * LcCustLimitOperateSerialDo转LbCLimitOperateSerialDo
     *
     * @param model 源DO对象
     * @return 目标DO对象
     */
    public static LbCLimitOperateSerialDo do2Copy(LcCustLimitOperateSerialDo model) {
        return LbCLimitOperateSerialCnvs.INSTANCE.do2Copy(model);
    }

    /**
     * LcCustLimitOperateSerialDo列表转LbCLimitOperateSerialDo列表
     *
     * @param doList 源DO列表
     * @return 目标DO列表
     */
    public static List<LbCLimitOperateSerialDo> doList2CopyList(List<LcCustLimitOperateSerialDo> doList) {
        if (null == doList) {
            return Collections.emptyList();
        }
        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (doList.size() / 0.75f) + 1, 16);
        return doList.parallelStream().map(LbCLimitOperateSerialConverter::do2Copy)//
            .collect(Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
    }

    /**
     * Integer转BigDecimal工具方法
     */
    private BigDecimal integerToBigDecimal(Integer value) {
        return value == null ? null : new BigDecimal(value);
    }
}