package com.hsjry.core.limit.batch.common.utils;

import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * -----
 *
 * <AUTHOR>
 * @version V3.0.5.1
 * @since V3.0.5.1
 */
public final class XmlUtils {
    private XmlUtils() {}

    public static Map<String, String> getMqMap(String xml) throws Exception {
        if (StringUtils.isBlank(xml)) {
            return new HashMap<>(0);
        }

        Map<String, String> map = new HashMap<>(100);

        Document document = DocumentHelper.parseText(xml);
        Element rootElement = document.getRootElement();
        Element serviceHeader = rootElement.element("Service_Header");
        Iterator<Element> headerIterator = serviceHeader.elementIterator();
        while (headerIterator.hasNext()) {
            Element element = headerIterator.next();
            map.put(element.getName(), element.getText());
        }

        Element serviceBody = rootElement.element("Service_Body");
        //ext_attributes 报文
        Element attributes = serviceBody.element("ext_attributes");
        Iterator<Element> attributesIterator = attributes.elementIterator();
        while (attributesIterator.hasNext()) {
            Element element = attributesIterator.next();
            map.put(element.getName(), element.getText());
        }
        //request 报文
        Element request = serviceBody.element("request");
        Iterator<Element> requestIterator = request.elementIterator();
        while (requestIterator.hasNext()) {
            Element element = requestIterator.next();
            map.put(element.getName(), element.getText());
        }
        return map;
    }

}
