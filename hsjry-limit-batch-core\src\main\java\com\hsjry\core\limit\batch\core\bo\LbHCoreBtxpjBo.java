package com.hsjry.core.limit.batch.core.bo;

import java.io.Serializable;

import com.hsjry.lang.mybatis.pagehelper.PageParam;

import lombok.Builder;
import lombok.Data;

/**
 * 核心系统-历史表-贴现票据信息
 *
 * <AUTHOR>
 * @date 2025-08-20 02:51:44
 */
@Data
@Builder
public class LbHCoreBtxpjBo implements Serializable {
    private static final long serialVersionUID = 1957998959763062787L;
    /** 承兑行行号 */
    private String chdhhh;
    /** 数据日期 */
    private String dataDate;
    /** 记录状态 */
    private String jiluzt;
    /** 时间戳 */
    private Long shjnch;
    /** 维护时间 */
    private Integer weihsj;
    /** 维护机构 */
    private String weihjg;
    /** 维护柜员 */
    private String weihgy;
    /** 维护日期 */
    private String weihrq;
    /** 交易柜员 */
    private String jioygy;
    /** 交易日期 */
    private String jioyrq;
    /** 柜员流水号 */
    private String guiyls;
    /** 承兑行种类 */
    private String chdhzl;
    /** 承兑行行名 */
    private String fukhmc;
    /** 法人代码 */
    private String faredm;
    /** 收款人全称 */
    private String skrnmc;
    /** 出票人全称 */
    private String chprqc;
    /** 调整天数 */
    private Integer tiozts;
    /** 票据到期日 */
    private String pjdqrq;
    /** 票据签发日 */
    private String pjqfrq;
    /** 票面金额 */
    private java.math.BigDecimal piomje;
    /** 票据号码 */
    private String piaojh;
    /** 是否电子票据 */
    private String sfdzpj;
    /** 票据种类 */
    private String piojzl;
    /** 贴现协议编号 */
    private String txxybh;
    /** 票据包号 */
    private String piojzh;
    /** 贴现借据号 */
    private String txnjjh;
}
