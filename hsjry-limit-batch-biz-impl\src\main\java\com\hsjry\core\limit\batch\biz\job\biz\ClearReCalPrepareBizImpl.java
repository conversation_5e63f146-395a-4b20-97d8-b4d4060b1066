/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.biz;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.core.limit.batch.biz.ReCalPlanBiz;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/22 16:12
 */
@Service
public class ClearReCalPrepareBizImpl implements BaseOrdinaryBiz {
    @Autowired
    private ReCalPlanBiz reCalPlanBiz;

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.CLEAR_RE_CAL_PREPARE;
    }

    @Override
    public void execBaseJob(JobInitDto jobInitDto) {
        reCalPlanBiz.clear();
    }
}
