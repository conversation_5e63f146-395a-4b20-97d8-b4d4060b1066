package com.hsjry.core.limit.batch.biz.convert.copy;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.hsjry.core.limit.batch.common.dto.file.LbCEntityOperateSerialDto;
import com.hsjry.core.limit.batch.dal.dao.model.LbCEntityOperateSerialDo;
import com.hsjry.core.limit.center.dal.dao.model.LcEntityOperateSerialDo;

/**
 * 实体操作流水转换器
 *
 * <AUTHOR>
 * @date 2025-07-09 12:31:05
 */
@Mapper(componentModel = "spring")
public interface LbCEntityOperateSerialCnvs {

    LbCEntityOperateSerialCnvs INSTANCE = Mappers.getMapper(LbCEntityOperateSerialCnvs.class);

    /**
     * 源DO转换为目标DO（主要转换方法）
     * 从LcEntityOperateSerialDo转换为LbCEntityOperateSerialDo
     */
    LbCEntityOperateSerialDo do2Copy(LcEntityOperateSerialDo model);

    /**
     * DTO转DO
     */
    LbCEntityOperateSerialDo dtoToDo(LbCEntityOperateSerialDto dto);

    /**
     * DO转DTO
     */
    LbCEntityOperateSerialDto do2Dto(LbCEntityOperateSerialDo model);
} 