package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.core.limit.batch.dal.dao.intf.LbTReclcIndvConsmLoanDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTReclcIndvConsmLoanMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIndvConsmLoanDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIndvConsmLoanExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIndvConsmLoanKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcIndvConsmLoanQuery;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中个人额度中消费贷额度数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-08-21 13:10:51
 */
@Repository
public class LbTReclcIndvConsmLoanDaoImpl
    extends AbstractBaseDaoImpl<LbTReclcIndvConsmLoanDo, LbTReclcIndvConsmLoanMapper>
    implements LbTReclcIndvConsmLoanDao {
    /**
     * 分页查询
     *
     * @param lbTReclcIndvConsmLoan 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTReclcIndvConsmLoanDo> selectPage(LbTReclcIndvConsmLoanQuery lbTReclcIndvConsmLoan,
        PageParam pageParam) {
        LbTReclcIndvConsmLoanExample example = buildExample(lbTReclcIndvConsmLoan);
        return PageHelper.<LbTReclcIndvConsmLoanDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询额度中心-中间表-额度重算中个人额度中消费贷额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public LbTReclcIndvConsmLoanDo selectByKey(String custNo, String custLimitId) {
        LbTReclcIndvConsmLoanKeyDo lbTReclcIndvConsmLoanKeyDo = new LbTReclcIndvConsmLoanKeyDo();
        lbTReclcIndvConsmLoanKeyDo.setCustNo(custNo);
        lbTReclcIndvConsmLoanKeyDo.setCustLimitId(custLimitId);
        return getMapper().selectByPrimaryKey(lbTReclcIndvConsmLoanKeyDo);
    }

    /**
     * 根据key删除额度中心-中间表-额度重算中个人额度中消费贷额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public int deleteByKey(String custNo, String custLimitId) {
        LbTReclcIndvConsmLoanKeyDo lbTReclcIndvConsmLoanKeyDo = new LbTReclcIndvConsmLoanKeyDo();
        lbTReclcIndvConsmLoanKeyDo.setCustNo(custNo);
        lbTReclcIndvConsmLoanKeyDo.setCustLimitId(custLimitId);
        return getMapper().deleteByPrimaryKey(lbTReclcIndvConsmLoanKeyDo);
    }

    /**
     * 查询额度中心-中间表-额度重算中个人额度中消费贷额度信息
     *
     * @param lbTReclcIndvConsmLoan 条件
     * @return List<LbTReclcIndvConsmLoanDo>
     */
    @Override
    public List<LbTReclcIndvConsmLoanDo> selectByExample(LbTReclcIndvConsmLoanQuery lbTReclcIndvConsmLoan) {
        return getMapper().selectByExample(buildExample(lbTReclcIndvConsmLoan));
    }

    /**
     * 新增额度中心-中间表-额度重算中个人额度中消费贷额度信息
     *
     * @param lbTReclcIndvConsmLoan 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTReclcIndvConsmLoanDo lbTReclcIndvConsmLoan) {
        if (lbTReclcIndvConsmLoan == null) {
            return -1;
        }

        return getMapper().insertSelective(lbTReclcIndvConsmLoan);
    }

    /**
     * 修改额度中心-中间表-额度重算中个人额度中消费贷额度信息
     *
     * @param lbTReclcIndvConsmLoan
     * @return
     */
    @Override
    public int updateBySelective(LbTReclcIndvConsmLoanDo lbTReclcIndvConsmLoan) {
        if (lbTReclcIndvConsmLoan == null) {
            return -1;
        }

        return getMapper().updateByPrimaryKeySelective(lbTReclcIndvConsmLoan);
    }

    @Override
    public int updateBySelectiveByExample(LbTReclcIndvConsmLoanDo lbTReclcIndvConsmLoan,
        LbTReclcIndvConsmLoanQuery lbTReclcIndvConsmLoanQuery) {
        return getMapper().updateByExampleSelective(lbTReclcIndvConsmLoan, buildExample(lbTReclcIndvConsmLoanQuery));
    }

    /**
     * 构建额度中心-中间表-额度重算中个人额度中消费贷额度Example信息
     *
     * @param lbTReclcIndvConsmLoan
     * @return
     */
    public LbTReclcIndvConsmLoanExample buildExample(LbTReclcIndvConsmLoanQuery lbTReclcIndvConsmLoan) {
        LbTReclcIndvConsmLoanExample example = new LbTReclcIndvConsmLoanExample();
        LbTReclcIndvConsmLoanExample.Criteria criteria = example.createCriteria();
        if (lbTReclcIndvConsmLoan != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbTReclcIndvConsmLoan.getCustNo())) {
                criteria.andCustNoEqualTo(lbTReclcIndvConsmLoan.getCustNo());
            }
            if (StringUtil.isNotEmpty(lbTReclcIndvConsmLoan.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lbTReclcIndvConsmLoan.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lbTReclcIndvConsmLoan.getTemplateNodeId())) {
                criteria.andTemplateNodeIdEqualTo(lbTReclcIndvConsmLoan.getTemplateNodeId());
            }
            if (StringUtil.isNotEmpty(lbTReclcIndvConsmLoan.getLimitStatus())) {
                criteria.andLimitStatusEqualTo(lbTReclcIndvConsmLoan.getLimitStatus());
            }
            if (null != lbTReclcIndvConsmLoan.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbTReclcIndvConsmLoan.getTotalAmount());
            }
            if (null != lbTReclcIndvConsmLoan.getPreOccupyAmount()) {
                criteria.andPreOccupyAmountEqualTo(lbTReclcIndvConsmLoan.getPreOccupyAmount());
            }
            if (null != lbTReclcIndvConsmLoan.getRealOccupyAmount()) {
                criteria.andRealOccupyAmountEqualTo(lbTReclcIndvConsmLoan.getRealOccupyAmount());
            }
            if (null != lbTReclcIndvConsmLoan.getLowRiskAmount()) {
                criteria.andLowRiskAmountEqualTo(lbTReclcIndvConsmLoan.getLowRiskAmount());
            }
            if (null != lbTReclcIndvConsmLoan.getPreOccupyLowRiskAmt()) {
                criteria.andPreOccupyLowRiskAmtEqualTo(lbTReclcIndvConsmLoan.getPreOccupyLowRiskAmt());
            }
            if (null != lbTReclcIndvConsmLoan.getRealOccupyLowRiskAmt()) {
                criteria.andRealOccupyLowRiskAmtEqualTo(lbTReclcIndvConsmLoan.getRealOccupyLowRiskAmt());
            }
        }
        buildExampleExt(lbTReclcIndvConsmLoan, criteria);
        return example;
    }

    /**
     * 构建额度中心-中间表-额度重算中个人额度中消费贷额度ExampleExt方法
     *
     * @param lbTReclcIndvConsmLoan
     * @return
     */
    public void buildExampleExt(LbTReclcIndvConsmLoanQuery lbTReclcIndvConsmLoan,
        LbTReclcIndvConsmLoanExample.Criteria criteria) {

        //自定义实现
    }

    // ==================== 消费贷额度重算相关方法实现 ====================

    /**
     * 2.1.清空消费贷额度中间表
     */
    @Override
    public int truncateConsmLoanLimit() {
        return getMapper().truncateConsmLoanLimit();
    }

    /**
     * 2.2.插入消费贷额度客户编号和额度编号
     */
    @Override
    public int insertConsmLoanLimit() {
        return getMapper().insertConsmLoanLimit();
    }

    /**
     * 2.3.更新消费贷额度中间表金额信息
     */
    @Override
    public int mergeConsmLoanLimitAmount() {
        return getMapper().mergeConsmLoanLimitAmount();
    }

    /**
     * 2.4.更新额度实例金额信息
     */
    @Override
    public int mergeConsmLoanLimitInstance() {
        return getMapper().mergeConsmLoanLimitInstance();
    }

}
