package com.hsjry.core.limit.batch.common.dto.file;

import java.io.Serializable;

import lombok.Builder;
import lombok.Data;

/**
 * 额度实例金额信息dto
 *
 * <AUTHOR>
 * @date 2025-07-09 12:31:05
 */
@Data
@Builder
public class LbCLimitAmtInfoDto implements Serializable {
    private static final long serialVersionUID = 1942924465449140226L;
    /** 预占低风险 */
    private java.math.BigDecimal preOccupyLowRiskAmt;
    /** 客户编号 */
    private String custNo;
    /** 总低风险金额编号 */
    private String lowRiskAmountId;
    /** 串用金额上限 */
    private java.math.BigDecimal shareAmountLimit;
    /** 被串用金额上限 */
    private java.math.BigDecimal sharedAmountLimit;
    /** 低风险币种 */
    private String lowRiskCurrency;
    /** 额度币种 */
    private String currency;
    /** 被串用实占低风险 */
    private java.math.BigDecimal sharedRealOccupyLowRisk;
    /** 被串用预占低风险 */
    private java.math.BigDecimal sharedPreOccupyLowRiskAmt;
    /** 下级压缩实占低风险 */
    private java.math.BigDecimal subRealOccupyLowRisk;
    /** 下级压缩预占低风险 */
    private java.math.BigDecimal subComprPreOccupyLowRisk;
    /** 压缩实占低风险 */
    private java.math.BigDecimal comprRealOccupyLowRisk;
    /** 压缩预占低风险 */
    private java.math.BigDecimal comprPreOccupyLowRisk;
    /** 已使用低风险 */
    private java.math.BigDecimal usedLowRiskAmt;
    /** 实占低风险 */
    private java.math.BigDecimal realOccupyLowRiskAmt;
    /** 暂封低风险 */
    private java.math.BigDecimal tmpForbidLowRiskAmt;
    /** 租户号 */
    private String tenantId;
    /** 总低风险额度 */
    private java.math.BigDecimal lowRiskAmount;
    /** 被串用实占额度 */
    private java.math.BigDecimal sharedRealOccupyAmount;
    /** 被串用预占额度 */
    private java.math.BigDecimal sharedPreOccupyAmount;
    /** 下层压缩实占额度 */
    private java.math.BigDecimal subComprRealOccupyAmount;
    /** 下层压缩预占额度 */
    private java.math.BigDecimal subComprPreOccupyAmount;
    /** 压缩实占额度 */
    private java.math.BigDecimal comprRealOccupyAmount;
    /** 压缩预占额度 */
    private java.math.BigDecimal comprPreOccupyAmount;
    /** 已使用额度 */
    private java.math.BigDecimal usedAmount;
    /** 实占额度【原使用中】 */
    private java.math.BigDecimal realOccupyAmount;
    /** 暂封额度 */
    private java.math.BigDecimal tmpForbidAmount;
    /** 预占额度【原冻结中】 */
    private java.math.BigDecimal preOccupyAmount;
    /** 总额度 */
    private java.math.BigDecimal totalAmount;
    /** 额度编号 */
    private String custLimitId;
    /** 更新时间 */
    private java.util.Date updateTime;
    /** 创建时间 */
    private java.util.Date createTime;
}
