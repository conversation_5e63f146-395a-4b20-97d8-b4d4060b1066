package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.core.limit.batch.dal.dao.intf.LbTReclcCorpNoCrdtDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTReclcCorpNoCrdtMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpNoCrdtDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpNoCrdtExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpNoCrdtKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcCorpNoCrdtQuery;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中对公客户中非授信额度数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-08-21 11:31:28
 */
@Repository
public class LbTReclcCorpNoCrdtDaoImpl extends AbstractBaseDaoImpl<LbTReclcCorpNoCrdtDo, LbTReclcCorpNoCrdtMapper>
    implements LbTReclcCorpNoCrdtDao {
    /**
     * 分页查询
     *
     * @param lbTReclcCorpNoCrdt 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTReclcCorpNoCrdtDo> selectPage(LbTReclcCorpNoCrdtQuery lbTReclcCorpNoCrdt, PageParam pageParam) {
        LbTReclcCorpNoCrdtExample example = buildExample(lbTReclcCorpNoCrdt);
        return PageHelper.<LbTReclcCorpNoCrdtDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询额度中心-中间表-额度重算中对公客户中非授信额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public LbTReclcCorpNoCrdtDo selectByKey(String custNo, String custLimitId) {
        LbTReclcCorpNoCrdtKeyDo lbTReclcCorpNoCrdtKeyDo = new LbTReclcCorpNoCrdtKeyDo();
        lbTReclcCorpNoCrdtKeyDo.setCustNo(custNo);
        lbTReclcCorpNoCrdtKeyDo.setCustLimitId(custLimitId);
        return getMapper().selectByPrimaryKey(lbTReclcCorpNoCrdtKeyDo);
    }

    /**
     * 根据key删除额度中心-中间表-额度重算中对公客户中非授信额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public int deleteByKey(String custNo, String custLimitId) {
        LbTReclcCorpNoCrdtKeyDo lbTReclcCorpNoCrdtKeyDo = new LbTReclcCorpNoCrdtKeyDo();
        lbTReclcCorpNoCrdtKeyDo.setCustNo(custNo);
        lbTReclcCorpNoCrdtKeyDo.setCustLimitId(custLimitId);
        return getMapper().deleteByPrimaryKey(lbTReclcCorpNoCrdtKeyDo);
    }

    /**
     * 查询额度中心-中间表-额度重算中对公客户中非授信额度信息
     *
     * @param lbTReclcCorpNoCrdt 条件
     * @return List<LbTReclcCorpNoCrdtDo>
     */
    @Override
    public List<LbTReclcCorpNoCrdtDo> selectByExample(LbTReclcCorpNoCrdtQuery lbTReclcCorpNoCrdt) {
        return getMapper().selectByExample(buildExample(lbTReclcCorpNoCrdt));
    }

    /**
     * 新增额度中心-中间表-额度重算中对公客户中非授信额度信息
     *
     * @param lbTReclcCorpNoCrdt 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTReclcCorpNoCrdtDo lbTReclcCorpNoCrdt) {
        if (lbTReclcCorpNoCrdt == null) {
            return -1;
        }
        return getMapper().insertSelective(lbTReclcCorpNoCrdt);
    }

    /**
     * 修改额度中心-中间表-额度重算中对公客户中非授信额度信息
     *
     * @param lbTReclcCorpNoCrdt
     * @return
     */
    @Override
    public int updateBySelective(LbTReclcCorpNoCrdtDo lbTReclcCorpNoCrdt) {
        if (lbTReclcCorpNoCrdt == null) {
            return -1;
        }
        return getMapper().updateByPrimaryKeySelective(lbTReclcCorpNoCrdt);
    }

    @Override
    public int updateBySelectiveByExample(LbTReclcCorpNoCrdtDo lbTReclcCorpNoCrdt,
        LbTReclcCorpNoCrdtQuery lbTReclcCorpNoCrdtQuery) {
        return getMapper().updateByExampleSelective(lbTReclcCorpNoCrdt, buildExample(lbTReclcCorpNoCrdtQuery));
    }

    /**
     * 构建额度中心-中间表-额度重算中对公客户中非授信额度Example信息
     *
     * @param lbTReclcCorpNoCrdt
     * @return
     */
    public LbTReclcCorpNoCrdtExample buildExample(LbTReclcCorpNoCrdtQuery lbTReclcCorpNoCrdt) {
        LbTReclcCorpNoCrdtExample example = new LbTReclcCorpNoCrdtExample();
        LbTReclcCorpNoCrdtExample.Criteria criteria = example.createCriteria();
        if (lbTReclcCorpNoCrdt != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbTReclcCorpNoCrdt.getCustNo())) {
                criteria.andCustNoEqualTo(lbTReclcCorpNoCrdt.getCustNo());
            }
            if (StringUtil.isNotEmpty(lbTReclcCorpNoCrdt.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lbTReclcCorpNoCrdt.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lbTReclcCorpNoCrdt.getTemplateNodeId())) {
                criteria.andTemplateNodeIdEqualTo(lbTReclcCorpNoCrdt.getTemplateNodeId());
            }
            if (StringUtil.isNotEmpty(lbTReclcCorpNoCrdt.getLimitStatus())) {
                criteria.andLimitStatusEqualTo(lbTReclcCorpNoCrdt.getLimitStatus());
            }
            if (null != lbTReclcCorpNoCrdt.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbTReclcCorpNoCrdt.getTotalAmount());
            }
            if (null != lbTReclcCorpNoCrdt.getPreOccupyAmount()) {
                criteria.andPreOccupyAmountEqualTo(lbTReclcCorpNoCrdt.getPreOccupyAmount());
            }
            if (null != lbTReclcCorpNoCrdt.getRealOccupyAmount()) {
                criteria.andRealOccupyAmountEqualTo(lbTReclcCorpNoCrdt.getRealOccupyAmount());
            }
            if (null != lbTReclcCorpNoCrdt.getLowRiskAmount()) {
                criteria.andLowRiskAmountEqualTo(lbTReclcCorpNoCrdt.getLowRiskAmount());
            }
            if (null != lbTReclcCorpNoCrdt.getPreOccupyLowRiskAmt()) {
                criteria.andPreOccupyLowRiskAmtEqualTo(lbTReclcCorpNoCrdt.getPreOccupyLowRiskAmt());
            }
            if (null != lbTReclcCorpNoCrdt.getRealOccupyLowRiskAmt()) {
                criteria.andRealOccupyLowRiskAmtEqualTo(lbTReclcCorpNoCrdt.getRealOccupyLowRiskAmt());
            }
        }
        buildExampleExt(lbTReclcCorpNoCrdt, criteria);
        return example;
    }

    /**
     * 构建额度中心-中间表-额度重算中对公客户中非授信额度ExampleExt方法
     *
     * @param lbTReclcCorpNoCrdt
     * @return
     */
    public void buildExampleExt(LbTReclcCorpNoCrdtQuery lbTReclcCorpNoCrdt,
        LbTReclcCorpNoCrdtExample.Criteria criteria) {

        //自定义实现
    }

    // ==================== 非授信额度重算相关方法实现 ====================

    /**
     * 6.1.清空非授信额度中间表
     */
    @Override
    public int truncateNoCreditLimit() {
        return getMapper().truncateNoCreditLimit();
    }

    /**
     * 6.2.插入非授信额度客户编号和额度编号
     */
    @Override
    public int insertNoCreditLimit() {
        return getMapper().insertNoCreditLimit();
    }

    /**
     * 6.3.更新非授信额度中间表金额信息
     */
    @Override
    public int mergeNoCreditLimitAmount() {
        return getMapper().mergeNoCreditLimitAmount();
    }

    /**
     * 6.4.更新额度实例金额信息
     */
    @Override
    public int mergeNoCreditLimitInstance() {
        return getMapper().mergeNoCreditLimitInstance();
    }

}
