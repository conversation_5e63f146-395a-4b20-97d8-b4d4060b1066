package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度中心-中间表-集团客户信息Do
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
@Table(name = "lb_t_grp_cust_info")
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LbTGrpCustInfoDo extends LbTGrpCustInfoKeyDo implements Serializable {
    private static final long serialVersionUID = 1942415996337979416L;
    /** 客户类型 */
    @Column(name = "cust_typ")
    private String custTyp;
    /** 客户名称 */
    @Column(name = "cust_nm")
    private String custNm;
    /** 证件类型 */
    @Column(name = "cert_typ")
    private String certTyp;
    /** 证件号码 */
    @Column(name = "cert_no")
    private String certNo;
    /** 操作人编号 */
    @Column(name = "operator_id")
    private String operatorId;
    /** 所属组织编号 */
    @Column(name = "own_organ_id")
    private String ownOrganId;
    /** 创建时间 */
    @Column(name = "create_time")
    private java.util.Date createTime;
    /** 更新时间 */
    @Column(name = "update_time")
    private java.util.Date updateTime;
}
