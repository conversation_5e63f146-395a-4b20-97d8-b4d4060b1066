package com.hsjry.core.limit.batch.common.enums;

import com.hsjry.lang.common.stereotype.enums.IEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 借据操作类型
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0 2023/11/13 17:06
 */
@Getter
@AllArgsConstructor
public enum EnumInvoiceOperateType implements IEnum {
    /** 还款 */
    REPAY("001", "还款"),
    /** 金额冲正 */
    AMOUNT_REVERSE ("002", "金额冲正"),
    /** 垫款 */
    ADVANCED_MONEY ("003", "垫款"),
    ;

    /** 状态码 */
    private String code;

    /** 状态描述 */
    private String description;

    /**
     * 根据编码查找枚举
     *
     * @param code 编码
     * @return {@link EnumInvoiceOperateType } 实例
     **/
    public static EnumInvoiceOperateType find(String code) {
        for (EnumInvoiceOperateType instance : EnumInvoiceOperateType.values()) {
            if (instance.getCode()
                .equals(code)) {
                return instance;
            }
        }
        return null;
    }
}