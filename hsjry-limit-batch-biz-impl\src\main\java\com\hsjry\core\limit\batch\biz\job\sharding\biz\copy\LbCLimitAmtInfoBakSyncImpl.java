package com.hsjry.core.limit.batch.biz.job.sharding.biz.copy;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.limit.EnumLimitHandlerStatus;
import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.biz.convert.copy.LbCLimitAmtInfoConverter;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.limit.AbstractShardingPrepareBiz;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.dal.dao.intf.LbCLimitAmtInfoDao;
import com.hsjry.core.limit.batch.dal.dao.model.LbCLimitAmtInfoDo;
import com.hsjry.core.limit.center.dal.dao.intf.CustLimitAmtInfoBatchDao;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitAmtInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.CustLimitAmtInfoQuery;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
/**
 * 额度实例金额信息数据同步到[核心系统-落地表-额度实例金额信息表]
 * 从源表lc_cust_limit_amt_info同步数据到目标表lb_c_limit_amt_info
 *
 * <AUTHOR>
 * @date 2025/7/9
 */
@Slf4j
@Service("lbCLimitAmtInfoBakSyncImpl")
@RequiredArgsConstructor
public class LbCLimitAmtInfoBakSyncImpl extends AbstractShardingPrepareBiz<CustLimitAmtInfoQuery>
    implements JobCoreBusiness<LcCustLimitAmtInfoDo> {

    private final LbCLimitAmtInfoDao lbCLimitAmtInfoDao;
    private final CustLimitAmtInfoBatchDao custLimitAmtInfoBatchDao;

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.C_LIMIT_AMT_INFO_BAK_SYNC;
    }

    @Override
    public Integer selectCountByCurrentGroupFromDb(CustLimitAmtInfoQuery query) {
        Integer count = custLimitAmtInfoBatchDao.selectCountByCurrentGroup(query);
        return count != null ? count : 0;
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);
        log.info(prefixLog + "====================== 分片逻辑 start ================================================");

        List<JobShared> jobSharedList = new ArrayList<>();
        // SQL批处理数量,暂定为分片数量
        Integer batchFixNum = jobInitDto.getFixNum();
        Integer fixNum = jobInitDto.getFixNum();

        // 获取总数据量
        CustLimitAmtInfoQuery countQuery = CustLimitAmtInfoQuery.builder()
            .tenantId(AppParamUtil.getTenantId())
            .build();
        Integer totalCount = custLimitAmtInfoBatchDao.selectCountByCurrentGroup(countQuery);

        if (totalCount == null || totalCount == 0) {
            log.info(prefixLog + "没有数据需要处理，总数据量: {}", totalCount);
            return jobSharedList;
        }

        log.info(prefixLog + "总数据量: {}, 分片大小: {}", totalCount, fixNum);

        // 计算分片数量
        Integer shardCount = (totalCount + fixNum - 1) / fixNum; // 向上取整

        // 生成分片
        for (int i = 0; i < shardCount; i++) {
            int batchNum = i + 1;
            int offset = i * fixNum;
            int limit = Math.min(fixNum, totalCount - offset);

            // 创建分片查询条件
            CustLimitAmtInfoQuery shardQuery = CustLimitAmtInfoQuery.builder()
                .tenantId(AppParamUtil.getTenantId())
                .offset(offset)
                .limit(limit)
                .build();

            // 创建JobShared对象
            JobShared jobShared = new JobShared();
            jobShared.setBatchSerialNo(batchSerialNo);
            jobShared.setBusinessDate(businessDate);
            jobShared.setBatchNum(batchNum);
            jobShared.setOffset(offset);
            jobShared.setLimit(limit);
            jobShared.setFixNum(fixNum);
            jobShared.setInPara(jobInitDto.getInPara());
            jobShared.setExtParam(GsonUtil.obj2Json(shardQuery));

            jobSharedList.add(jobShared);

            log.info(prefixLog + "生成分片[{}]: offset={}, limit={}", batchNum, offset, limit);
        }

        log.info(prefixLog + "====================== 分片逻辑 end ================================================");
        log.info(prefixLog + "额度实例信息同步分片任务生成完成,共{}个分片", jobSharedList.size());
        return jobSharedList;
    }

    @Override
    public ShardingResult<LcCustLimitAmtInfoDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info(prefixLog + "开始查询分片数据,分片号:[{}]", batchNum);

        ShardingResult<LcCustLimitAmtInfoDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            log.warn(prefixLog + "分片[{}]的extParam为空，跳过查询", batchNum);
            return shardingResult;
        }

        try {
            // 从extParam中解析查询条件
            CustLimitAmtInfoQuery query = GsonUtil.json2Obj(jobShared.getExtParam(), CustLimitAmtInfoQuery.class);
            if (query == null) {
                log.error(prefixLog + "分片[{}]的extParam解析失败: {}", batchNum, jobShared.getExtParam());
                return shardingResult;
            }

            log.info(prefixLog + "=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");

            // 查询分片数据
            List<LcCustLimitAmtInfoDo> dataList = custLimitAmtInfoBatchDao.selectShardList(query);
            shardingResult.setShardingResultList(dataList);

            log.info(prefixLog + "分片数据查询完成,分片号:[{}],数据量:[{}]", batchNum,
                CollectionUtil.isEmpty(dataList) ? 0 : dataList.size());

        } catch (Exception e) {
            log.error(prefixLog + "分片[{}]查询数据时发生异常", batchNum, e);
            throw new HsjryBizException(EnumBatchJobError.SYSTEM_ERR.getCode(),
                "分片数据查询异常: " + e.getMessage());
        }

        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LcCustLimitAmtInfoDo> shardingResult) {
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        Integer dataSize = shardingResult.getShardingResultList().size();

        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        List<LcCustLimitAmtInfoDo> dataList = shardingResult.getShardingResultList();

        if (CollectionUtil.isEmpty(dataList)) {
            log.info(prefixLog + "=========分片执行结束:[{}]数量为空===========", batchNum);
            return;
        }

        log.info(prefixLog + "=========分片执行开始:[{}]数量为[{}]===========", batchNum, dataSize);

        try {
            LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();
            // 只在第一个分片中清空目标表
            if (sliceBatchSerialDo.getBatchNum() == 1) {
                log.info(prefixLog + "第一个分片,开始清空目标表lb_c_limit_amt_info");
                lbCLimitAmtInfoDao.deleteAll();
                log.info(prefixLog + "目标表lb_c_limit_amt_info清空完成");
            }
            // 更新分片流水前，初始化执行状态，确保不为空
            if (sliceBatchSerialDo.getSharedStatus() == null) {
                sliceBatchSerialDo.setSharedStatus(EnumLimitHandlerStatus.IN_HANDLE.getCode());
            }
            // 数据转换和插入
            List<LbCLimitAmtInfoDo> targetDataList = convertSourceToTarget(dataList);
            if (CollectionUtil.isNotEmpty(targetDataList)) {
                // 对插入数据列表的custLimitId进行重复性检查
                Set<String> custLimitIdSet = new HashSet<>();
                List<String> duplicateCustLimitIds = new ArrayList<>();
                for (LbCLimitAmtInfoDo item : targetDataList) {
                    String custLimitId = item.getCustLimitId();
                    if (custLimitId != null) {
                        if (!custLimitIdSet.add(custLimitId)) {
                            duplicateCustLimitIds.add(custLimitId);
                        }
                    }
                }
                // 检查数据库中是否已存在插入列表中的custLimitId
                List<String> insertCustLimitIds = targetDataList.stream()
                        .map(LbCLimitAmtInfoDo::getCustLimitId)
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(insertCustLimitIds)) {
                    List<String> existCustLimitIds = lbCLimitAmtInfoDao.selectExistCustLimitIds(insertCustLimitIds);
                    if (CollectionUtil.isNotEmpty(existCustLimitIds)) {
                        log.error(prefixLog + "检测到数据库已存在的custLimitId，重复ID列表: {}", GsonUtil.obj2Json(existCustLimitIds));
                        throw new HsjryBizException("BATCH_DB_DUPLICATE_CUST_LIMIT_ID", "数据库已存在插入数据中的custLimitId，重复ID: " + GsonUtil.obj2Json(existCustLimitIds));
                    }
                }
                if (!duplicateCustLimitIds.isEmpty()) {
                    log.error(prefixLog + "检测到重复的custLimitId，重复ID列表: {}", GsonUtil.obj2Json(duplicateCustLimitIds));
                    throw new HsjryBizException("BATCH_DUPLICATE_CUST_LIMIT_ID", "插入数据中存在重复的custLimitId，重复ID: " + GsonUtil.obj2Json(duplicateCustLimitIds));
                }
                lbCLimitAmtInfoDao.insertList(targetDataList);
                log.info(prefixLog + "成功插入[{}]条数据到目标表", targetDataList.size());
            }

            // 更新分片流水成功
            normalUpdateSliceSerial(dataSize, sliceBatchSerialDo);

        } catch (Exception e) {
            log.error(prefixLog + "分片执行异常:[{}]", batchNum, e);
            throw new HsjryBizException(EnumBatchJobError.SYSTEM_ERR.getCode(),
                EnumBatchJobError.SYSTEM_ERR.getDescription());
        }

        log.info(prefixLog + "=========分片执行结束:[{}]数量为[{}]===========", batchNum, dataSize);
    }

    /**
     * 数据转换:从LcCustLimitAmtInfoDo转换为LbCLimitAmtInfoDo
     */
    private List<LbCLimitAmtInfoDo> convertSourceToTarget(List<LcCustLimitAmtInfoDo> sourceList) {
        if (CollectionUtil.isEmpty(sourceList)) {
            return Collections.emptyList();
        }
        return LbCLimitAmtInfoConverter.doList2CopyList(sourceList);
    }
}
