/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.biz;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.util.JobUtil;
import com.hsjry.core.limit.batch.biz.AmtLimitBiz;
import com.hsjry.core.limit.batch.biz.AmtLimitRuleBiz;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;

import lombok.extern.slf4j.Slf4j;

/**
 * 限额停用前提醒
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/6 14:55
 */
@Service
@Slf4j
public class DisableAmtLimitRuleRemindBizImpl implements BaseOrdinaryBiz {

    @Autowired
    private AmtLimitRuleBiz amtLimitRuleBiz;

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.AMT_LIMIT_DISABLE_REMIND;
    }

    @Override
    public void execBaseJob(JobInitDto jobInitDto) {
        log.info("========执行限额停用前提醒批量开始========" + jobInitDto.getBusinessDate());
        amtLimitRuleBiz.disableAmtLimitRemind(JobUtil.getNum2Date(jobInitDto.getBusinessDate()));
        log.info("========执行限额停用前提醒批量结束========" + jobInitDto.getBusinessDate());
    }
}
