# 批处理作业故障排除指南

## LB_T_ELCBL_IBNK_PROD_LMT_INFO_PROCESS 无限循环问题

### 问题描述
电票系统同业客户产品层额度信息处理作业在分片生成阶段出现无限循环，导致超过最大循环次数限制（10000次）而失败。

### 根本原因
1. **数据质量问题**：`lb_s_elcbl_ibnk_lmt_synz` 表中存在重复的 `ibnk_user_id` 值
2. **分片算法缺陷**：使用 `ibnk_user_id > ?` 条件查询时，重复值会导致一直返回相同记录
3. **缺少去重机制**：没有跟踪已处理的记录，无法检测重复处理

### 修复方案

#### 立即修复（已实施）
1. **代码修复**：
   - 添加已处理记录跟踪机制（使用 HashSet）
   - 改进错误日志，提供更详细的诊断信息
   - 增强循环退出条件检查

2. **SQL 优化**：
   - 使用复合排序 `ORDER BY ibnk_user_id ASC, id ASC` 确保唯一性

#### 数据质量检查
执行 `scripts/data_quality_check.sql` 脚本检查：
- 重复的 ibnk_user_id
- NULL 值
- 数据格式问题
- 特殊字符

#### 数据清理（如需要）
```sql
-- 1. 备份原表
CREATE TABLE lb_s_elcbl_ibnk_lmt_synz_backup AS 
SELECT * FROM lb_s_elcbl_ibnk_lmt_synz;

-- 2. 删除重复记录，保留最新的
DELETE FROM lb_s_elcbl_ibnk_lmt_synz 
WHERE id IN (
    SELECT id FROM (
        SELECT id, 
               ROW_NUMBER() OVER (PARTITION BY ibnk_user_id ORDER BY create_time DESC, id DESC) as rn
        FROM lb_s_elcbl_ibnk_lmt_synz
    ) ranked
    WHERE rn > 1
);

-- 3. 添加唯一性约束（可选）
ALTER TABLE lb_s_elcbl_ibnk_lmt_synz 
ADD CONSTRAINT uk_lb_s_elcbl_ibnk_lmt_synz_user_id UNIQUE (ibnk_user_id);
```

### 监控和预防

#### 1. 作业监控指标
- 分片生成时间
- 循环次数
- 处理的唯一记录数
- 数据质量指标

#### 2. 告警规则
- 循环次数超过 1000 次时发出警告
- 分片生成时间超过 5 分钟时告警
- 检测到重复数据时立即告警

#### 3. 定期检查
- 每日执行数据质量检查脚本
- 监控表的数据增长和重复率
- 定期清理历史数据

### 类似问题排查

其他分片处理类可能存在类似问题：
1. `LbTCptlProdLmtInfoProcessImpl`
2. `LbTOlCorpProdLmtInfoProcessImpl`  
3. `LbTOlIndvProdLmtInfoProcessImpl`

建议对这些类也进行类似的改进。

### 性能优化建议

1. **批处理大小调优**：
   - 当前默认 fixNum 可能过小，建议调整为 1000-5000
   - 根据数据量和系统性能动态调整

2. **索引优化**：
   ```sql
   -- 确保有合适的索引
   CREATE INDEX idx_lb_s_elcbl_ibnk_lmt_synz_user_id 
   ON lb_s_elcbl_ibnk_lmt_synz (ibnk_user_id, id);
   ```

3. **分片策略优化**：
   - 考虑使用基于时间或数值范围的分片策略
   - 避免依赖可能重复的业务字段

### 应急处理流程

1. **发现问题**：监控到循环次数异常
2. **立即响应**：停止作业，检查数据质量
3. **问题诊断**：执行数据质量检查脚本
4. **数据修复**：清理重复数据
5. **重新执行**：使用修复后的代码重新运行作业
6. **后续监控**：加强监控，防止问题复发

### 联系信息
- 技术负责人：[待填写]
- 紧急联系方式：[待填写]
- 问题跟踪：[待填写]
