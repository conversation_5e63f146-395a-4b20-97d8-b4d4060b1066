package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbCLimitOperateSerialDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbCLimitOperateSerialQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度操作流水数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-03 10:03:12
 */
public interface LbCLimitOperateSerialDao extends IBaseDao<LbCLimitOperateSerialDo> {
    /**
     * 分页查询额度操作流水
     *
     * @param lbCLimitOperateSerialQuery 条件
     * @return PageInfo<LbCLimitOperateSerialDo>
     */
    PageInfo<LbCLimitOperateSerialDo> selectPage(LbCLimitOperateSerialQuery lbCLimitOperateSerialQuery,
        PageParam pageParam);

    /**
     * 根据key查询额度操作流水
     *
     * @param closSerialNo
     * @return
     */
    LbCLimitOperateSerialDo selectByKey(String closSerialNo);

    /**
     * 根据key删除额度操作流水
     *
     * @param closSerialNo
     * @return
     */
    int deleteByKey(String closSerialNo);

    /**
     * 查询额度操作流水信息
     *
     * @param lbCLimitOperateSerialQuery 条件
     * @return List<LbCLimitOperateSerialDo>
     */
    List<LbCLimitOperateSerialDo> selectByExample(LbCLimitOperateSerialQuery lbCLimitOperateSerialQuery);

    /**
     * 新增额度操作流水信息
     *
     * @param lbCLimitOperateSerial 条件
     * @return int>
     */
    int insertBySelective(LbCLimitOperateSerialDo lbCLimitOperateSerial);

    /**
     * 修改额度操作流水信息
     *
     * @param lbCLimitOperateSerial
     * @return
     */
    int updateBySelective(LbCLimitOperateSerialDo lbCLimitOperateSerial);

    /**
     * 修改额度操作流水信息
     *
     * @param lbCLimitOperateSerial
     * @param lbCLimitOperateSerialQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbCLimitOperateSerialDo lbCLimitOperateSerial,
        LbCLimitOperateSerialQuery lbCLimitOperateSerialQuery);

    /**
     * 批量插入额度实例金额信息
     *
     * @param lbCLimitObjectInfoList 批量数据
     * @return int
     */
    @Override
    int insertList(List<LbCLimitOperateSerialDo> lbCLimitObjectInfoList);

    /**
     * 清空额度实例流水信息所有数据
     *
     * @return int
     */
    int deleteAll();

    /**
     * 查询分片数据
     *
     * @param query 查询条件
     * @return List<LbCLimitOperateSerialDo>
     */
    List<LbCLimitOperateSerialDo> selectShardList(LbCLimitOperateSerialQuery query);

    /**
     * 获取第一个对象，limit m，1
     *
     * @param query 查询条件
     * @return LbCLimitOperateSerialDo
     */
    LbCLimitOperateSerialDo selectFirstOne(LbCLimitOperateSerialQuery query);

    /**
     * 获取当前组的数据量
     *
     * @param query 查询条件
     * @return Integer
     */
    Integer selectCountByCurrentGroup(LbCLimitOperateSerialQuery query);
}
