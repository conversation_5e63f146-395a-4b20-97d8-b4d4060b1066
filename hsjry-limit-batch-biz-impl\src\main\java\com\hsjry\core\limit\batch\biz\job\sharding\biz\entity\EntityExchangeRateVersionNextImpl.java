/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.sharding.biz.entity;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.limit.EnumReCalFlag;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.limit.AbstractShardingPrepareBiz;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.center.dal.dao.intf.EntityInfoBatchDao;
import com.hsjry.core.limit.center.dal.dao.model.LcEntityInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.EntityInfoBatchQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.extern.slf4j.Slf4j;
/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/28 10:31
 */
@Service
@Slf4j
public class EntityExchangeRateVersionNextImpl extends AbstractShardingPrepareBiz<EntityInfoBatchQuery>
    implements JobCoreBusiness<LcEntityInfoDo> {

    @Autowired
    private EntityInfoBatchDao entityInfoBatchDao;

    @Override
    public Integer selectCountByCurrentGroupFromDb(EntityInfoBatchQuery query) {
        return entityInfoBatchDao.selectCountByCurrentGroup(query);
    }

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.ENTITY_EXCHANGE_RATE_VERSION_NEXT;
    }

    @Override
    public ShardingResult<LcEntityInfoDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        ShardingResult<LcEntityInfoDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }
        //原始查询条件
        EntityInfoBatchQuery entityInfoBatchQuery = GsonUtil.json2Obj(jobShared.getExtParam(),
            EntityInfoBatchQuery.class);
        EntityInfoBatchQuery query = EntityInfoBatchQuery.builder()
            .offset(jobShared.getOffset())
            .limit(jobShared.getLimit())
            .amtLimitExRateRecalFlag(EnumReCalFlag.DONE.getCode())
            .custLimitExRateRecalFlag(EnumReCalFlag.DONE.getCode())
            .entityId(entityInfoBatchQuery.getEntityId())
            .build();
        log.info("=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");
        List<LcEntityInfoDo> list = entityInfoBatchDao.selectShardList(query);
        shardingResult.setShardingResultList(list);
        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LcEntityInfoDo> shardingResult) {
        log.info("=========分片执行开始:[{}]===========", shardingResult.getJobShared()
            .getBatchNum());
        List<LcEntityInfoDo> lcEntityInfoDoList = shardingResult.getShardingResultList();
        if (CollectionUtils.isEmpty(lcEntityInfoDoList)) {
            log.info("=========分片执行结束:" + shardingResult.getJobShared()
                .getBatchNum() + "数量为空===========");
            return;
        }
        List<String> onlyCustList = Lists.newArrayList();
        List<String> onlyAmtList = Lists.newArrayList();
        List<String> allList = Lists.newArrayList();
        for (LcEntityInfoDo lcEntityInfoDo : lcEntityInfoDoList) {
            if (EnumReCalFlag.DONE.getCode()
                .equals(lcEntityInfoDo.getCustLimitExRateRecalFlag()) && EnumReCalFlag.NEVER.getCode()
                .equals(lcEntityInfoDo.getAmtLimitExRateRecalFlag())) {
                onlyCustList.add(lcEntityInfoDo.getEntityId());
                continue;
            }
            if (EnumReCalFlag.DONE.getCode()
                .equals(lcEntityInfoDo.getAmtLimitExRateRecalFlag()) && EnumReCalFlag.NEVER.getCode()
                .equals(lcEntityInfoDo.getCustLimitExRateRecalFlag())) {
                onlyAmtList.add(lcEntityInfoDo.getEntityId());
                continue;
            }
            if (EnumReCalFlag.DONE.getCode()
                .equals(lcEntityInfoDo.getCustLimitExRateRecalFlag()) && EnumReCalFlag.DONE.getCode()
                .equals(lcEntityInfoDo.getAmtLimitExRateRecalFlag())) {
                allList.add(lcEntityInfoDo.getEntityId());
            }

        }

        if (CollectionUtil.isNotEmpty(allList)) {
            int size = entityInfoBatchDao.updateExchangeRateVersion(allList, BusinessDateUtil.getDate(),
                EnumReCalFlag.NOT.getCode(), EnumReCalFlag.NOT.getCode(), EnumReCalFlag.DONE.getCode(),
                EnumReCalFlag.DONE.getCode());
            if (size != allList.size()) {
                log.warn("更新数量和目标数量不符,目标数量[{}],更新数量[{}]", allList.size(), size);
            }
        }
        if (CollectionUtil.isNotEmpty(onlyAmtList)) {
            int size = entityInfoBatchDao.updateExchangeRateVersion(onlyAmtList, BusinessDateUtil.getDate(),
                EnumReCalFlag.NEVER.getCode(), EnumReCalFlag.NOT.getCode(), EnumReCalFlag.NEVER.getCode(),
                EnumReCalFlag.DONE.getCode());
            if (size != onlyAmtList.size()) {
                log.warn("更新数量和目标数量不符,目标数量[{}],更新数量[{}]", onlyAmtList.size(), size);
            }
        }
        if (CollectionUtil.isNotEmpty(onlyCustList)) {
            int size = entityInfoBatchDao.updateExchangeRateVersion(onlyCustList, BusinessDateUtil.getDate(),
                EnumReCalFlag.NOT.getCode(), EnumReCalFlag.NEVER.getCode(), EnumReCalFlag.DONE.getCode(),
                EnumReCalFlag.NEVER.getCode());
            if (size != onlyCustList.size()) {
                log.warn("更新数量和目标数量不符,目标数量[{}],更新数量[{}]", onlyCustList.size(), size);
            }
        }

        //更新分片流水成功
        normalUpdateSliceSerial(lcEntityInfoDoList.size(), shardingResult.

            getLcSliceBatchSerialDo());
        log.info("=========分片执行结束:[{}]数量为[{}]===========", shardingResult.getJobShared()
            .

                getBatchNum(), lcEntityInfoDoList.

            size());
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        log.info("====================== 接入业务{}分片逻辑 start ================================================",
            getJobTrade().getDescription());
        List<JobShared> jobSharedList = new ArrayList<>();
        //sql 批处理数量 暂定为分片数量，不放大
        Integer batchFixNum = jobInitDto.getFixNum();
        //当前分组的最大值，为下次 批处理的最小值
        LcEntityInfoDo maxEntityInfoDo = new LcEntityInfoDo();
        //构造查询条件 查询当前 分批处理的 排序 最大 对象
        EntityInfoBatchQuery query = EntityInfoBatchQuery.builder()
            .amtLimitExRateRecalFlag(EnumReCalFlag.DONE.getCode())
            .custLimitExRateRecalFlag(EnumReCalFlag.DONE.getCode())
            .offset(batchFixNum - 1)
            .limit(1)
            .build();
        //分片流水
        int batchNum = 0;
        while (maxEntityInfoDo != null) {
            query.setEntityId(maxEntityInfoDo.getEntityId());
            maxEntityInfoDo = entityInfoBatchDao.selectFirstOne(query);
            //统计分片 数量
            batchNum = countBatchNum(batchFixNum, query, maxEntityInfoDo, batchNum, jobInitDto, jobSharedList,
                query.getEntityId(), false);
        }
        log.info("======================== 接入业务{}分片逻辑 end ================================================",
            getJobTrade().getDescription());
        return jobSharedList;
    }

}
