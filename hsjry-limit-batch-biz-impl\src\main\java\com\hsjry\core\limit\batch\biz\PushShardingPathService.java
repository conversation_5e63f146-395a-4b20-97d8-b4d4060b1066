package com.hsjry.core.limit.batch.biz;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.fs.service.FileProcessService;
import com.hsjry.base.common.fs.service.FileProcessServiceFactory;
import com.hsjry.base.common.fs.service.processor.ShardFileService;
import com.hsjry.base.common.fs.util.processor.FileUtils;
import com.hsjry.base.common.job.dto.EnumJobError;
import com.hsjry.core.limit.batch.biz.utils.FileShardingUtils;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.exception.JobBizException;
import com.hsjry.lang.common.utils.ArrayUtil;
import com.hsjry.lang.common.utils.CollectionUtil;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class PushShardingPathService {
    @Autowired
    private ShardFileService shardFileService;
    //合同维度文件名
    @Value("${contract.limit.file.name:LIMITBUSINESSRLIST_[DATE].txt}")
    private String contractLimitFileName;
    @Value("${user.limit.file.localFilePath:/app/loan/limit-file}")
    private String localFilePathDefine;
    //任务里充当临时目录来用
    @Value("${contract.limit.file.remoteFilePath:/nfsdata/file/midd/ULCS/ALS/data/[DATE]/810/}")
    private String remoteFilePathDefine;
    //客户维度文件名
    @Value("${user.limit.file.name:LIMITCUSTOMERLIST.txt}")
    private String userLimitFileName;

    private final String LOCAL_MERGE_DIR = "localMerge";
    private final String ONLINE_MERGE_DIR = "onlineMerge";

    /**
     * 获取分片数据路径
     * @param jobTrade
     * @param date
     * @return
     */
    public  String getShardingFilePath(EnumJobTrade jobTrade, Integer date){
        String path = null;
        switch (jobTrade) {
            case PUSH_CONTRACT_LIMIT_FILE:
            case PUSH_CUSTOMER_LIMIT_FILE:
                path = localFilePathDefine + File.separator + String.valueOf(date);
                break;
            default:
                path = null;
                break;
        }
        return path;
    }

    /**
     * 分片数据文件名
     * @param jobTrade
     * @param date
     * @param suffix 后缀
     *               本地分片文件 传 batchNum
     *               本地分片合并后文件 ip
     *               最终文件 null
     * @return
     */
    public String getShardingFileName(EnumJobTrade jobTrade, Integer date, String suffix){
        String name = null;
        switch (jobTrade) {
            case PUSH_CONTRACT_LIMIT_FILE:
                name = contractLimitFileName.replace(FileShardingUtils.FILE_DATE_CODE_MARK,String.valueOf(date));
                break;
            case PUSH_CUSTOMER_LIMIT_FILE:
                name = userLimitFileName.replace(FileShardingUtils.FILE_DATE_CODE_MARK,String.valueOf(date));
                break;
            default:
                name = null;
                break;
        }
        if(StringUtils.isNotBlank(suffix)){
            name += "_" +suffix;
        }
        return name;
    }
    /**
     * 获取本节点合并后文件生成路径
     * @param jobTrade
     * @param date
     * @return
     */
    public String getLocalMergePath(EnumJobTrade jobTrade, Integer date){
        return getShardingFilePath(jobTrade,date) + File.separator + LOCAL_MERGE_DIR;
    }
    /**
     * 远程临时文件目录
     *  每个节点合并完自己节点的分片文件后都会上传到这
     */
    public String getRemoteTmpFilePath(EnumJobTrade jobTrade,Integer date){
        String path = null;
        switch (jobTrade) {
            case PUSH_CONTRACT_LIMIT_FILE:
            case PUSH_CUSTOMER_LIMIT_FILE:
                path = remoteFilePathDefine.replace(FileShardingUtils.FILE_DATE_CODE_MARK,String.valueOf(date));
                break;
            default:
                path = null;
                break;
        }
        return path;
    }



    /**
     * 获取远程文件下载后存放路径
     * @param jobTrade
     * @param date
     * @return
     */
    public String getOnlineTmpFilePath(EnumJobTrade jobTrade,Integer date){
        return getShardingFilePath(jobTrade,date) + File.separator + ONLINE_MERGE_DIR;
    }

    /**
     * 最终上传的远程sftp路径
     * @param jobTrade
     * @param date
     * @return
     */
    public String getRemoteFilePath(EnumJobTrade jobTrade,Integer date){
        return  remoteFilePathDefine.replace(FileShardingUtils.FILE_DATE_CODE_MARK,String.valueOf(date));
    }

    public void writeFile(List<String> rowDataList,String filePath){
        FileWriter fileWriter = null;
        BufferedWriter bufferedReader = null;
        try {
        fileWriter = new FileWriter(new File(filePath));
        bufferedReader = new BufferedWriter(fileWriter);

            if (CollectionUtil.isNotEmpty(rowDataList)) {
                for (String rowData : rowDataList) {
                    // 写入文件
                    bufferedReader.write(rowData);
                    bufferedReader.newLine();
                }
            }
            // 刷新缓冲区
            bufferedReader.flush();
        }catch (Exception e){
            log.error("本地文件写入异常，e={}",e);
            throw new HsjryBizException(EnumBatchJobError.WRITE_FILE_ERROR.getCode(),
                    EnumBatchJobError.WRITE_FILE_ERROR.getDescription(), this.getClass());
        }finally {
            try {
                fileWriter.close();
                bufferedReader.close();
            } catch (IOException e) {
                log.error("文件流关闭失败e={}",e);
            }
        }
    }

    public String mergeLocalFile(List<LcSliceBatchSerialDo> sliceBatchSerialDoList, EnumJobTrade enumJobTrade, Integer businessDate,String ip) {
        //获取本节点合并后文件生成路径
        String mergeFilePath = getLocalMergePath(enumJobTrade,businessDate);
        try {
            FileUtils.createDirectories(mergeFilePath);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
//        FileUtils.deleteIfExists(mergeFilePath);
        //合并后文件名
        String mergeFileName = getShardingFileName(enumJobTrade,businessDate,ip);
        String absPath = mergeFilePath+File.separator+mergeFileName;
        //分片文件当时的写入路径
        String shardingFilePath = getShardingFilePath(enumJobTrade,businessDate);
        FileUtils.deleteIfExists(absPath);
        List<File> list = new ArrayList<>(sliceBatchSerialDoList.size());
        log.info("本地分片文件合并开始->{}",absPath);
        for(LcSliceBatchSerialDo lcSliceBatchSerialDo :sliceBatchSerialDoList){
            //每个分片的文件名
            String shardingFileName = getShardingFileName(enumJobTrade, businessDate, String.valueOf(lcSliceBatchSerialDo.getBatchNum()));
            list.add(new File(shardingFilePath + File.separator + shardingFileName));
        }
        try {
            shardFileService.mergeFilesNoHeader(list,mergeFilePath,mergeFileName);
        } catch (IOException e) {
            log.error("本地分片文件{} 合并失败,e={}", absPath,e);
            throw new JobBizException(EnumJobError.MERGE_SLICE_FILE_ERROR.getCode(),
                    EnumJobError.MERGE_SLICE_FILE_ERROR.getDescription(), getClass());
        }
        log.info("本地分片文件合并完成->{}",absPath);
        return absPath;
    }

    /**
     * 下载(前面节点，本地分片文件合并后，以ip地址为后缀，一个节点一个)
     * @param jobTrade
     * @param businessDate
     * @param execIpSet
     * @param serviceFactory
     * @return
     */
    public String downloadEveryNodeAndMergeFiles(EnumJobTrade jobTrade, Integer businessDate, Set<String> execIpSet, FileProcessServiceFactory serviceFactory) {
        //远程sftp上的临时文件目录
        String remoteTmpFilePath = getRemoteTmpFilePath(jobTrade,businessDate);
        List<File> result = new ArrayList<>();
        Iterator<String> iterator = execIpSet.iterator();
        FileProcessService instance = serviceFactory.getInstance();
        //远程文件下载后本地存放路径
        String localOnlineMergePath = getOnlineTmpFilePath(jobTrade, businessDate);
        try {
            FileUtils.createDirectories(localOnlineMergePath);
        } catch (IOException e) {
            log.info("创建远程文件下载 本地路径失败 path={}",localOnlineMergePath);
            throw new RuntimeException(e);
        }
//        FileUtils.deleteIfExists(localOnlineMergePath);
        while(iterator.hasNext()){
            String ip = iterator.next();
            //sftp上的临时文件文件名
            String remoteMergeName = getShardingFileName(jobTrade,businessDate,ip);
            String absRemotePath = remoteTmpFilePath + File.separator + remoteMergeName;
            byte[] bytes = instance.downLoadFileBytes(absRemotePath);
            if (!ArrayUtil.isEmpty(bytes)) {
                try {
                    FileUtils.writeBytes(bytes, localOnlineMergePath+File.separator+remoteMergeName);
                } catch (IOException e) {
                    log.info("下载远程文件失败 path={}",localOnlineMergePath+File.separator+remoteMergeName);
                    throw new RuntimeException(e);
                }
            }
            result.add(new File(localOnlineMergePath+File.separator+remoteMergeName));
        }
        //最终文件名
        String localOnlineMergeName = getShardingFileName(jobTrade, businessDate,"");
        String absLocalOnlineMergePath = localOnlineMergePath + File.separator + localOnlineMergeName;
        try {
            shardFileService.mergeFilesNoHeader(result,localOnlineMergePath,localOnlineMergeName);
        } catch (IOException e) {
            log.error("本地远程分片文件{} 合并失败,e={}", absLocalOnlineMergePath , e);
            throw new JobBizException(EnumJobError.MERGE_SLICE_FILE_ERROR.getCode(),
                    EnumJobError.MERGE_SLICE_FILE_ERROR.getDescription(), getClass());
        }
        return absLocalOnlineMergePath;

    }

}
