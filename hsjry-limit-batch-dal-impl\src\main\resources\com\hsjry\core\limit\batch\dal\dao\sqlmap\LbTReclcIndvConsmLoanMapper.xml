<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.batch.dal.dao.mapper.LbTReclcIndvConsmLoanMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIndvConsmLoanDo">
        <result property="custNo" column="cust_no" jdbcType="VARCHAR"/> <!-- 客户编号 -->
        <result property="custLimitId" column="cust_limit_id" jdbcType="VARCHAR"/> <!-- 额度编号 -->
        <result property="templateNodeId" column="template_node_id" jdbcType="VARCHAR"/> <!-- 模板节点编号 -->
        <result property="limitStatus" column="limit_status" jdbcType="VARCHAR"/> <!-- 额度状态 -->
        <result property="totalAmount" column="total_amount" jdbcType="DECIMAL"/> <!-- 总额度 -->
        <result property="preOccupyAmount" column="pre_occupy_amount" jdbcType="DECIMAL"/> <!-- 预占额度 -->
        <result property="realOccupyAmount" column="real_occupy_amount" jdbcType="DECIMAL"/> <!-- 实占额度 -->
        <result property="lowRiskAmount" column="low_risk_amount" jdbcType="DECIMAL"/> <!-- 总低风险额度 -->
        <result property="preOccupyLowRiskAmt" column="pre_occupy_low_risk_amt" jdbcType="DECIMAL"/> <!-- 预占低风险 -->
        <result property="realOccupyLowRiskAmt" column="real_occupy_low_risk_amt" jdbcType="DECIMAL"/> <!-- 实占低风险 -->
    </resultMap>
    <sql id="Base_Column_List">
        cust_no
        , cust_limit_id
                , template_node_id
                , limit_status
                , total_amount
                , pre_occupy_amount
                , real_occupy_amount
                , low_risk_amount
                , pre_occupy_low_risk_amt
                , real_occupy_low_risk_amt
    </sql>

    <!-- ==================== 消费贷额度重算相关SQL ==================== -->
    <!-- 2.1.清空消费贷额度中间表 -->
    <update id="truncateConsmLoanLimit">
        TRUNCATE TABLE LB_T_RECLC_INDV_CONSM_LOAN
    </update>

    <!-- 2.2.插入消费贷额度客户编号和额度编号 -->
    <insert id="insertConsmLoanLimit">
        INSERT INTO LB_T_RECLC_INDV_CONSM_LOAN(CUST_NO, CUST_LIMIT_ID, TEMPLATE_NODE_ID, LIMIT_STATUS)
        SELECT DISTINCT LCLI.LIMIT_OBJECT_ID, LCLI.CUST_LIMIT_ID, lcli.TEMPLATE_NODE_ID, LCLI.LIMIT_STATUS
        FROM LC_CUST_LIMIT_INFO LCLI
        WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSGRKHEDTX')
          AND LCLI.TEMPLATE_NODE_ID IN ('GRXFDED')
          AND LCLI.LIMIT_STATUS IN ('020')
    </insert>

    <!-- 2.3.更新消费贷额度中间表金额信息 -->
    <update id="mergeConsmLoanLimitAmount">
        MERGE INTO LB_T_RECLC_INDV_CONSM_LOAN TGT
        USING (SELECT LCLI.LIMIT_OBJECT_ID,
                      SUM(LCLAI.TOTAL_AMOUNT)             AS TOTAL_AMOUNT,
                      SUM(LCLAI.PRE_OCCUPY_AMOUNT)        AS PRE_OCCUPY_AMOUNT,
                      SUM(LCLAI.REAL_OCCUPY_AMOUNT)       AS REAL_OCCUPY_AMOUNT,
                      SUM(LCLAI.LOW_RISK_AMOUNT)          AS LOW_RISK_AMOUNT,
                      SUM(LCLAI.PRE_OCCUPY_LOW_RISK_AMT)  AS PRE_OCCUPY_LOW_RISK_AMT,
                      SUM(LCLAI.REAL_OCCUPY_LOW_RISK_AMT) AS REAL_OCCUPY_LOW_RISK_AMT
               FROM LC_CUST_LIMIT_INFO LCLI
                        INNER JOIN LC_CUST_LIMIT_AMT_INFO LCLAI
                                   ON LCLI.CUST_LIMIT_ID = LCLAI.CUST_LIMIT_ID AND
                                      LCLI.LIMIT_OBJECT_ID = LCLAI.CUST_NO
               WHERE LCLI.LIMIT_TEMPLATE_ID IN ('HNNSGRKHEDTX')
                 AND LCLI.TEMPLATE_NODE_ID IN ('GRXFDCPED', 'GRXFAJCPED', 'GRXYKED')
                 AND LCLI.LIMIT_STATUS IN ('020')
                 AND LCLI.CUST_LIMIT_ID IN (SELECT CURRENT_NODE_LIMIT_ID
                                            FROM LC_CUST_LIMIT_RELATION LCLR
                                            WHERE LCLR.PARENT_NODE_LIMIT_ID IN (SELECT DISTINCT CUST_LIMIT_ID
                                                                                FROM LB_T_RECLC_INDV_CONSM_LOAN))
               GROUP BY LCLI.LIMIT_OBJECT_ID) SRC
        ON (TGT.CUST_NO = SRC.LIMIT_OBJECT_ID)
        WHEN MATCHED THEN
        UPDATE
            SET TGT.TOTAL_AMOUNT = SRC.TOTAL_AMOUNT,
            TGT.PRE_OCCUPY_AMOUNT = SRC.PRE_OCCUPY_AMOUNT,
            TGT.REAL_OCCUPY_AMOUNT = SRC.REAL_OCCUPY_AMOUNT,
            TGT.LOW_RISK_AMOUNT = SRC.LOW_RISK_AMOUNT,
            TGT.PRE_OCCUPY_LOW_RISK_AMT = SRC.PRE_OCCUPY_LOW_RISK_AMT,
            TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT
    </update>

    <!-- 2.4.更新额度实例金额信息 -->
    <update id="mergeConsmLoanLimitInstance">
        MERGE INTO LC_CUST_LIMIT_AMT_INFO TGT
        USING (SELECT CUST_NO,
                      CUST_LIMIT_ID,
                      LIMIT_STATUS,
                      TOTAL_AMOUNT,
                      PRE_OCCUPY_AMOUNT,
                      REAL_OCCUPY_AMOUNT,
                      LOW_RISK_AMOUNT,
                      PRE_OCCUPY_LOW_RISK_AMT,
                      REAL_OCCUPY_LOW_RISK_AMT
               FROM LB_T_RECLC_INDV_CONSM_LOAN) SRC
        ON (TGT.CUST_NO = SRC.CUST_NO
            AND TGT.CUST_LIMIT_ID = SRC.CUST_LIMIT_ID)
        WHEN MATCHED THEN
        UPDATE
            SET TGT.TOTAL_AMOUNT = SRC.TOTAL_AMOUNT,
            TGT.PRE_OCCUPY_AMOUNT = SRC.PRE_OCCUPY_AMOUNT,
            TGT.REAL_OCCUPY_AMOUNT = SRC.REAL_OCCUPY_AMOUNT,
            TGT.LOW_RISK_AMOUNT = SRC.LOW_RISK_AMOUNT,
            TGT.PRE_OCCUPY_LOW_RISK_AMT = SRC.PRE_OCCUPY_LOW_RISK_AMT,
            TGT.REAL_OCCUPY_LOW_RISK_AMT = SRC.REAL_OCCUPY_LOW_RISK_AMT,
            TGT.UPDATE_TIME = sysdate
    </update>
</mapper>