package com.hsjry.core.limit.batch.common.enums;

import com.hsjry.lang.common.stereotype.enums.IEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/7 19:23
 */
@Getter
@AllArgsConstructor
public enum EnumBatchJobError implements IEnum {

    /** 分片流水记录状态异常 */
    SHARDING_SERIAL_STATUS_ERROR("LCB9001", "分片流水记录状态异常"),
    /** 分片信息错误 */
    SHARDING_INFO_ERROR("LCB9002", "分片信息错误"),
    /** 分片信息主键缺失 */
    SHARDING_PRIMARY_ERROR("LCB9003", "分片信息主键缺失"),
    /** 任务调度交易码不合法 */
    JOB_TRADE_CODE_ERROR("LCB9004", "任务调度交易码不合法"),
    /** 任务调度下游业务执行失败 */
    JOB_EXEC_BIZ_ERROR("LCB9005", "任务调度下游业务执行失败"),
    /** 对象为空 */
    OBJECT_NULL("LCB9006", "对象为空"),
    /** 字段为空 */
    PROPERTY_NULL("LCB9007", "字段为空"),
    /** 文件路径不存在 */
    FILE_PATH_NOT_EXIST("LCB9008", "文件路径不存在"),
    /** 触发批量任务编号为空 */
    TRIGGER_JOB_ID_IS_NULL("LCB9009", "触发批量任务编号为空"),
    /** 写入文件异常 */
    WRITE_FILE_ERROR("LCB9010","写入文件异常"),
    /** 创建文件异常 */
    CREATE_FILE_ERROR("LCB9011","创建文件异常"),

    SYSTEM_ERR("LCB9999", "系统错误"),
    ;

    /** 状态码 */
    private String code;

    /** 状态描述 */
    private String description;

    /**
     * 根据编码查找枚举
     *
     * @param code 编码
     * @return {@link EnumBatchJobError } 实例
     **/
    public static EnumBatchJobError find(String code) {
        for (EnumBatchJobError instance : EnumBatchJobError.values()) {
            if (instance.getCode()
                .equals(code)) {
                return instance;
            }
        }
        return null;
    }
}
