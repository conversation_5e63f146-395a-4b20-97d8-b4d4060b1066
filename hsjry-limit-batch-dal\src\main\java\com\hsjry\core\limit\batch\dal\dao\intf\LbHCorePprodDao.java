package com.hsjry.core.limit.batch.dal.dao.intf;

import com.hsjry.core.limit.batch.dal.dao.model.LbHCorePprodDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbHCorePprodQuery;

import java.util.List;

import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 核心系统产品定义表-落地表（存储产品基础定义信息）数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbHCorePprodDao extends IBaseDao<LbHCorePprodDo> {
    /**
     * 分页查询核心系统产品定义表-落地表（存储产品基础定义信息）
     *
     * @param lbHCorePprodQuery 条件
     * @return PageInfo<LbHCorePprodDo>
     */
    PageInfo<LbHCorePprodDo> selectPage(LbHCorePprodQuery lbHCorePprodQuery, PageParam pageParam);

    /**
     * 根据key查询核心系统产品定义表-落地表（存储产品基础定义信息）
     *
     * @param chapbh
     * @param dataDate
     * @return
     */
    LbHCorePprodDo selectByKey(String chapbh, String dataDate);

    /**
     * 根据key删除核心系统产品定义表-落地表（存储产品基础定义信息）
     *
     * @param chapbh
     * @param dataDate
     * @return
     */
    int deleteByKey(String chapbh, String dataDate);

    /**
     * 查询核心系统产品定义表-落地表（存储产品基础定义信息）信息
     *
     * @param lbHCorePprodQuery 条件
     * @return List<LbHCorePprodDo>
     */
    List<LbHCorePprodDo> selectByExample(LbHCorePprodQuery lbHCorePprodQuery);

    /**
     * 新增核心系统产品定义表-落地表（存储产品基础定义信息）信息
     *
     * @param lbHCorePprod 条件
     * @return int>
     */
    int insertBySelective(LbHCorePprodDo lbHCorePprod);

    /**
     * 修改核心系统产品定义表-落地表（存储产品基础定义信息）信息
     *
     * @param lbHCorePprod
     * @return
     */
    int updateBySelective(LbHCorePprodDo lbHCorePprod);

    /**
     * 修改核心系统产品定义表-落地表（存储产品基础定义信息）信息
     *
     * @param lbHCorePprod
     * @param lbHCorePprodQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbHCorePprodDo lbHCorePprod, LbHCorePprodQuery lbHCorePprodQuery);

    int deleteAll();

    int deleteByDataDate(String dataDate);

    /**
     * 批量插入核心系统产品定义信息
     *
     * @param lbHCorePprodList 核心系统产品定义信息列表
     * @return int
     */
    int insertList(List<LbHCorePprodDo> lbHCorePprodList);
}
