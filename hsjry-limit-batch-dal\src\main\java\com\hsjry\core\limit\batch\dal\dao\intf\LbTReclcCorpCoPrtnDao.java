package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpCoPrtnDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcCorpCoPrtnQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中对公客户中合作方额度数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-08-21 11:31:28
 */
public interface LbTReclcCorpCoPrtnDao extends IBaseDao<LbTReclcCorpCoPrtnDo> {
    /**
     * 分页查询额度中心-中间表-额度重算中对公客户中合作方额度
     *
     * @param lbTReclcCorpCoPrtnQuery 条件
     * @return PageInfo<LbTReclcCorpCoPrtnDo>
     */
    PageInfo<LbTReclcCorpCoPrtnDo> selectPage(LbTReclcCorpCoPrtnQuery lbTReclcCorpCoPrtnQuery, PageParam pageParam);

    /**
     * 根据key查询额度中心-中间表-额度重算中对公客户中合作方额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    LbTReclcCorpCoPrtnDo selectByKey(String custNo, String custLimitId);

    /**
     * 根据key删除额度中心-中间表-额度重算中对公客户中合作方额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custNo, String custLimitId);

    /**
     * 查询额度中心-中间表-额度重算中对公客户中合作方额度信息
     *
     * @param lbTReclcCorpCoPrtnQuery 条件
     * @return List<LbTReclcCorpCoPrtnDo>
     */
    List<LbTReclcCorpCoPrtnDo> selectByExample(LbTReclcCorpCoPrtnQuery lbTReclcCorpCoPrtnQuery);

    /**
     * 新增额度中心-中间表-额度重算中对公客户中合作方额度信息
     *
     * @param lbTReclcCorpCoPrtn 条件
     * @return int>
     */
    int insertBySelective(LbTReclcCorpCoPrtnDo lbTReclcCorpCoPrtn);

    /**
     * 修改额度中心-中间表-额度重算中对公客户中合作方额度信息
     *
     * @param lbTReclcCorpCoPrtn
     * @return
     */
    int updateBySelective(LbTReclcCorpCoPrtnDo lbTReclcCorpCoPrtn);

    /**
     * 修改额度中心-中间表-额度重算中对公客户中合作方额度信息
     *
     * @param lbTReclcCorpCoPrtn
     * @param lbTReclcCorpCoPrtnQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTReclcCorpCoPrtnDo lbTReclcCorpCoPrtn,
        LbTReclcCorpCoPrtnQuery lbTReclcCorpCoPrtnQuery);

    // ==================== 合作方额度重算相关方法 ====================

    /**
     * 5.1.清空合作方额度中间表
     */
    int truncateCoPartnerLimit();

    /**
     * 5.2.插入合作方额度客户编号和额度编号
     */
    int insertCoPartnerLimit();

    /**
     * 5.3.更新合作方额度中间表金额信息
     */
    int mergeCoPartnerLimitAmount();

    /**
     * 5.4.更新额度实例金额信息
     */
    int mergeCoPartnerLimitInstance();
}
