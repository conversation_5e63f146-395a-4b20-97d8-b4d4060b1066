package com.hsjry.core.limit.batch.biz.job.sharding.biz.mid;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.customer.EnumUserType;
import com.hsjry.base.common.model.enums.limit.EnumCustLimitStatus;
import com.hsjry.base.common.model.enums.limit.EnumLmtTplNode;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.limit.AbstractShardingPrepareBiz;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.dal.dao.intf.LbSOlCtrInfoDao;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTOlIndvCtrInfoDao;
import com.hsjry.core.limit.batch.dal.dao.model.LbSOlCtrInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSOlCtrInfoQuery;
import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitInfoDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitObjectInfoDao;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitObjectInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitInfoQuery;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitObjectInfoQuery;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 网贷个人额度处理
 *
 * <AUTHOR>
 * @date 2025-07-10 12:31:05
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LbTOlIndvCtrInfoProcessImpl extends AbstractShardingPrepareBiz<LbSOlCtrInfoQuery>
    implements JobCoreBusiness<LbSOlCtrInfoDo> {
    private final LbSOlCtrInfoDao lbSOlCtrInfoDao;
    private final LbTOlIndvCtrInfoDao lbTOlIndvCtrInfoDao;
    private final LcCustLimitInfoDao custLimitInfoDao;
    private final LcCustLimitObjectInfoDao custLimitObjectInfoDao;

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.LB_T_OL_INDV_CTR_INFO_PROCESS;
    }

    /**
     * 查询当前分片主键范围内的数据总数
     * 根据contractId、tenantId复合主键范围统计当前分片的数据量
     *
     * @param query 查询条件，需包含主键范围
     * @return 当前分片的数据量
     */
    @Override
    public Integer selectCountByCurrentGroupFromDb(LbSOlCtrInfoQuery query) {
        Integer count = lbSOlCtrInfoDao.selectCountByCurrentGroup(query);
        return count != null ? count : 0;
    }

    /**
     * 生成分片任务列表
     * 采用offset/limit分页方式，基于总数据量计算分片数量，避免复杂的主键循环
     *
     * @param jobInitDto 任务初始化参数
     * @return 分片任务列表
     */
    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);
        log.info(prefixLog + "====================== 分片逻辑 start ================================================");

        List<JobShared> jobSharedList = new ArrayList<>();
        // SQL批处理数量,暂定为分片数量
        Integer fixNum = jobInitDto.getFixNum();

        // 获取总数据量
        LbSOlCtrInfoQuery countQuery = LbSOlCtrInfoQuery.builder().build();
        Integer totalCount = lbSOlCtrInfoDao.selectCountByCurrentGroup(countQuery);

        if (totalCount == null || totalCount == 0) {
            log.info(prefixLog + "没有数据需要处理，总数据量: {}", totalCount);
            return jobSharedList;
        }

        log.info(prefixLog + "总数据量: {}, 分片大小: {}", totalCount, fixNum);

        // 计算分片数量
        Integer shardCount = (totalCount + fixNum - 1) / fixNum; // 向上取整

        // 生成分片
        for (int i = 0; i < shardCount; i++) {
            int batchNum = i + 1;
            int offset = i * fixNum;
            int limit = Math.min(fixNum, totalCount - offset);

            // 创建分片查询条件
            LbSOlCtrInfoQuery shardQuery = LbSOlCtrInfoQuery.builder()
                .offset(offset)
                .limit(limit)
                .build();

            // 创建JobShared对象
            JobShared jobShared = new JobShared();
            jobShared.setBatchSerialNo(batchSerialNo);
            jobShared.setBusinessDate(businessDate);
            jobShared.setBatchNum(batchNum);
            jobShared.setOffset(offset);
            jobShared.setLimit(limit);
            jobShared.setFixNum(fixNum);
            jobShared.setInPara(jobInitDto.getInPara());
            jobShared.setExtParam(GsonUtil.obj2Json(shardQuery));

            jobSharedList.add(jobShared);

            log.info(prefixLog + "生成分片[{}]: offset={}, limit={}", batchNum, offset, limit);
        }

        log.info(prefixLog + "====================== 分片逻辑 end ================================================");
        log.info(prefixLog + "网贷个人额度处理分片任务生成完成,共{}个分片", jobSharedList.size());
        return jobSharedList;
    }

    @Override
    public ShardingResult<LbSOlCtrInfoDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info(prefixLog + "开始查询分片数据,分片号:[{}]", batchNum);

        ShardingResult<LbSOlCtrInfoDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            log.warn(prefixLog + "分片[{}]的extParam为空，跳过查询", batchNum);
            return shardingResult;
        }

        try {
            // 从extParam中解析查询条件
            LbSOlCtrInfoQuery query = GsonUtil.json2Obj(jobShared.getExtParam(), LbSOlCtrInfoQuery.class);
            if (query == null) {
                log.error(prefixLog + "分片[{}]的extParam解析失败: {}", batchNum, jobShared.getExtParam());
                return shardingResult;
            }

            log.info(prefixLog + "=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");

            // 性能监控
            long startTime = System.currentTimeMillis();

            // 查询分片数据
            List<LbSOlCtrInfoDo> dataList = lbSOlCtrInfoDao.selectShardList(query);
            shardingResult.setShardingResultList(dataList);

            long endTime = System.currentTimeMillis();
            log.info(prefixLog + "分片数据查询完成,分片号:[{}],数据量:[{}],耗时:[{}]ms",
                batchNum, CollectionUtils.isEmpty(dataList) ? 0 : dataList.size(), (endTime - startTime));

            // 性能告警
            if (dataList != null && dataList.size() > jobInitDto.getFixNum() * 2) {
                log.warn(prefixLog + "分片数据量过大,分片号:[{}],数据量:[{}],可能影响性能",
                    batchNum, dataList.size());
            }

        } catch (Exception e) {
            log.error(prefixLog + "分片[{}]查询数据时发生异常", batchNum, e);
            throw new RuntimeException("分片数据查询异常: " + e.getMessage(), e);
        }

        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LbSOlCtrInfoDo> shardingResult) {
        // 获取分片数据列表
        List<LbSOlCtrInfoDo> shardingDataList = shardingResult.getShardingResultList();
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        Integer dataSize = shardingResult.getShardingResultList().size();

        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);
        log.info(prefixLog + "=========分片执行开始:[{}]数量为[{}]===========", batchNum, dataSize);

        // 判空处理，若分片数据为空直接返回
        if (CollectionUtils.isEmpty(shardingDataList)) {
            log.info(prefixLog + "=========分片执行结束:分片号[{}]数量为空===========", batchNum);
            return;
        }
        try {
            log.info(prefixLog + "开始处理[{}]条[网贷系统-落地表-合同信息]数据", shardingDataList.size());
            LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();
            //查询[网贷系统-落地表-合同信息]中所有的[USER_ID]
            List<String> userIdList = shardingDataList.stream().filter(Objects::nonNull)//
                .map(LbSOlCtrInfoDo::getUserId)//
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
            //查询[额度实例所属对象信息]
            List<LcCustLimitObjectInfoDo> objectInfoDoList = custLimitObjectInfoDao.selectByExample(
                LcCustLimitObjectInfoQuery.builder()//
                    .userType(EnumUserType.INDIVIDUAL_CUSTOMER.getCode())//
                    .userIdList(userIdList)//
                    .build());
            List<String> custLimitIdList = objectInfoDoList.stream().filter(Objects::nonNull)//
                .map(LcCustLimitObjectInfoDo::getCustLimitId)//
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
            //查询[额度实例信息]中TEMPLATE_NODE_ID=['GRJYDCPED','GRXFDCPED']列表
            List<String> nodeIdList = Lists.newArrayList(EnumLmtTplNode.GRJYDCPED.getTemplateNodeId(),
                EnumLmtTplNode.GRXFDCPED.getTemplateNodeId());
            List<LcCustLimitInfoDo> limitInfoDoList = custLimitInfoDao.selectByExample(LcCustLimitInfoQuery.builder()
                .custLimitIdList(custLimitIdList)
                .limitObjectIdList(userIdList)
                .templateNodeIdList(nodeIdList)
                .statusList(EnumCustLimitStatus.getOperableCodeList())
                .build());
            List<String> limitIdList = limitInfoDoList.stream().filter(Objects::nonNull)//
                .map(LcCustLimitInfoDo::getCustLimitId)//
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());

            //插入数据到[网贷系统-中间表-个人合同信息]中
            int insertCount = lbTOlIndvCtrInfoDao.insertOlIndvCtrInfo(nodeIdList, limitIdList);
            log.info(prefixLog + "插入网贷系统-中间表-个人合同信息完成,插入记录数:[{}]", insertCount);

            //更新[额度实例金额信息]信息
            int updateCustLimitAmtInfoCount = lbTOlIndvCtrInfoDao.updateCustLimitAmtInfo(limitIdList);
            log.info(prefixLog + "更新额度实例金额信息完成,更新记录数:[{}]", updateCustLimitAmtInfoCount);

            // 更新分片流水成功
            normalUpdateSliceSerial(shardingDataList.size(), sliceBatchSerialDo);
        } catch (Exception e) {
            // 异常处理，记录详细日志并抛出运行时异常
            log.error(prefixLog + "分片执行异常:[{}]", batchNum, e);
            throw new RuntimeException(prefixLog + "处理异常", e);
        }
        log.info(prefixLog + "=========分片执行结束:[{}]===========", batchNum);
    }
} 