package com.hsjry.core.limit.batch.biz.job.job.file;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * 国结系统历史表信用证信息文件同步处理任务
 * 负责H_ITNST_LC_INFO_FILE_SYNC文件的批量同步处理
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/11 11:41
 */
@Slf4j
@Service("lbHItnstLcInfoFileSyncJob")
public class LbHItnstLcInfoFileSyncJob extends AbstractBaseBatchJob {

    public LbHItnstLcInfoFileSyncJob() {
        log.info("LbHItnstLcInfoFileSyncJob Bean初始化完成");
    }

    @Autowired
    @Qualifier("lbHItnstLcInfoFileSyncBizImpl")
    private BaseOrdinaryBiz lbHItnstLcInfoFileSyncBizImpl;

    @Override
    public BaseOrdinaryBiz getBaseOrdinaryBiz() {
        return lbHItnstLcInfoFileSyncBizImpl;
    }
}
