package com.hsjry.core.limit.batch.biz.job.job.copy;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * 备份表-实体信息数据备份同步处理任务
 *
 * <AUTHOR>
 * @date 2025-07-09 12:31:05
 */
@Slf4j
@Service("lbCEntityInfoBakSyncJob")
public class LbCEntityInfoBakSyncJob extends AbstractBaseBatchJob {
    
    public LbCEntityInfoBakSyncJob() {
        log.info("LbCEntityInfoBakSyncJob Bean初始化完成");
    }

    @Autowired
    @Qualifier("lbCEntityInfoBakSyncBizImpl")
    private BaseOrdinaryBiz baseOrdinaryBiz;

    @Override
    public BaseOrdinaryBiz getBaseOrdinaryBiz() {
        return baseOrdinaryBiz;
    }
} 