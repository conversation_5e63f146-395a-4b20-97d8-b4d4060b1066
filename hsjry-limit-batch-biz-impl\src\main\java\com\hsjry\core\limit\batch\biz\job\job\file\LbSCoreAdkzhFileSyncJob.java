package com.hsjry.core.limit.batch.biz.job.job.file;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * 核心系统-落地表-贷款账户主表文件的同步处理任务
 * 负责S_CORE_ADKZH文件的批量同步处理
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/21 11:00
 */
@Slf4j
@Service("lbSCoreAdkzhFileSyncJob")
public class LbSCoreAdkzhFileSyncJob extends AbstractBaseBatchJob {
    
    public LbSCoreAdkzhFileSyncJob() {
        log.info("LbSCoreAdkzhFileSyncJob Bean初始化完成");
    }

    @Autowired
    @Qualifier("lbSCoreAdkzhFileSyncBizImpl")
    private BaseOrdinaryBiz lbSCoreAdkzhFileSyncBizImpl;

    @Override
    public BaseOrdinaryBiz getBaseOrdinaryBiz() {
        return lbSCoreAdkzhFileSyncBizImpl;
    }
} 