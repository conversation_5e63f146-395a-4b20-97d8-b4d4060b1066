/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.convert.file;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.hsjry.core.limit.batch.biz.entity.LbSCoreBtxpjData;
import com.hsjry.core.limit.batch.dal.dao.model.LbSCoreBtxpjDo;

import lombok.extern.slf4j.Slf4j;

/**
 * 核心贴现票据信息文件数据转换器
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/11 11:41
 */
@Slf4j
public class LbSCoreBtxpjConverter {

    /** 默认初始容量 */
    private static final int DEFAULT_INITIAL_CAPACITY = 16;
    /** 负载因子 */
    private static final float LOAD_FACTOR = 0.75f;

    /**
     * Data 转换为 Do
     *
     * @param data 文件数据
     */
    public static LbSCoreBtxpjDo data2Do(LbSCoreBtxpjData data) {
        return LbSCoreBtxpjCnvs.INSTANCE.data2Do(data);
    }

    /**
     * Data列表转DO列表
     *
     * @param dataList DTO列表
     * @return DO列表
     */
    public List<LbSCoreBtxpjDo> dataListToDoList(List<LbSCoreBtxpjData> dataList) {
        if (Objects.isNull(dataList)) {
            return Collections.emptyList();
        }
        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (dataList.size() / LOAD_FACTOR) + 1, DEFAULT_INITIAL_CAPACITY);
        return dataList.parallelStream().map(LbSCoreBtxpjConverter::data2Do)//
            .collect(Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
    }
}