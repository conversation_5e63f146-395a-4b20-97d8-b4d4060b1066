package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTOlCorpLoanInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTOlCorpLoanInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 网贷系统-中间表-对公借据信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-31 12:50:59
 */
public interface LbTOlCorpLoanInfoDao extends IBaseDao<LbTOlCorpLoanInfoDo> {
    /**
     * 分页查询网贷系统-中间表-对公借据信息
     *
     * @param lbTOlCorpLoanInfoQuery 条件
     * @return PageInfo<LbTOlCorpLoanInfoDo>
     */
    PageInfo<LbTOlCorpLoanInfoDo> selectPage(LbTOlCorpLoanInfoQuery lbTOlCorpLoanInfoQuery, PageParam pageParam);

    /**
     * 根据key查询网贷系统-中间表-对公借据信息
     *
     * @param custNo
     * @param custLimitId
     * @param entityId
     * @return
     */
    LbTOlCorpLoanInfoDo selectByKey(String custNo, String custLimitId, String entityId);

    /**
     * 根据key删除网贷系统-中间表-对公借据信息
     *
     * @param custNo
     * @param custLimitId
     * @param entityId
     * @return
     */
    int deleteByKey(String custNo, String custLimitId, String entityId);

    /**
     * 查询网贷系统-中间表-对公借据信息信息
     *
     * @param lbTOlCorpLoanInfoQuery 条件
     * @return List<LbTOlCorpLoanInfoDo>
     */
    List<LbTOlCorpLoanInfoDo> selectByExample(LbTOlCorpLoanInfoQuery lbTOlCorpLoanInfoQuery);

    /**
     * 新增网贷系统-中间表-对公借据信息信息
     *
     * @param lbTOlCorpLoanInfo 条件
     * @return int>
     */
    int insertBySelective(LbTOlCorpLoanInfoDo lbTOlCorpLoanInfo);

    /**
     * 修改网贷系统-中间表-对公借据信息信息
     *
     * @param lbTOlCorpLoanInfo
     * @return
     */
    int updateBySelective(LbTOlCorpLoanInfoDo lbTOlCorpLoanInfo);

    /**
     * 修改网贷系统-中间表-对公借据信息信息
     *
     * @param lbTOlCorpLoanInfo
     * @param lbTOlCorpLoanInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTOlCorpLoanInfoDo lbTOlCorpLoanInfo,
        LbTOlCorpLoanInfoQuery lbTOlCorpLoanInfoQuery);

    /**
     * 将网贷对公客户额度同步数据插入到借据信息表
     * 执行复杂的多表关联查询，将结果插入到lb_t_ol_corp_loan_info表
     *
     * @param templateNodeIdList 模板节点ID列表
     * @param custLimitIdList 客户额度ID列表
     * @return int 插入的记录数
     */
    int insertOlCorpLoanInfo(List<String> templateNodeIdList, List<String> custLimitIdList);

    /**
     * 更新实体信息表
     * 根据网贷系统中间表数据更新LC_ENTITY_INFO表
     *
     * @param custLimitIdList 客户额度ID列表
     * @return int 更新的记录数
     */
    int updateEntityInfo(List<String> custLimitIdList);

    /**
     * 更新实体操作流水表
     * 根据网贷系统中间表数据更新LC_ENTITY_OPERATE_SERIAL表
     *
     * @return int 更新的记录数
     */
    int updateEntityOperateSerial();

    /**
     * 查询当前分片主键范围内的数据总数
     * 根据entityId主键范围统计当前分片的数据量
     *
     * @param query 查询条件，需包含entityId范围
     * @return 当前分片的数据量
     */
    Integer selectCountByCurrentGroup(LbTOlCorpLoanInfoQuery query);

    /**
     * 查询分片数据列表
     * 支持offset/limit分页查询
     *
     * @param query 查询条件，包含offset和limit
     * @return 分片数据列表
     */
    List<LbTOlCorpLoanInfoDo> selectShardList(LbTOlCorpLoanInfoQuery query);
}
