/*
 * yunrong.cn Inc. Copyright (c) 2014-2019 All Rights Reserved
 */

package com.hsjry.core.limit.batch.common.utils;

import java.util.Collection;

import org.springframework.util.CollectionUtils;

import com.google.common.base.Objects;
import com.hsjry.base.common.model.enums.common.EnumErrorType;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.exception.HsjryCheckException;
import com.hsjry.lang.common.exception.HsjryDBException;
import com.hsjry.lang.common.exception.HsjryRpcException;
import com.hsjry.lang.common.log.TenantLog;
import com.hsjry.lang.common.utils.StringUtil;

/**
 * 异常生成工具类
 *
 * <AUTHOR>
 * @version V3.0
 * @since 3.0.1 2020/10/17 16:04
 */
public class ExceptionUtil {
    /**
     * 生成校验异常
     *
     * @param error 错误枚举
     * @param detail 错误详细信息
     * @param tClass
     * @return
     */
    public static HsjryCheckException getCheckException(EnumBatchJobError error, String detail, Class tClass) {
        TenantLog.getLogger(tClass)
            .warn("{}", detail);
        return new HsjryCheckException(error.getCode(), error.getDescription(), tClass);
    }

    /**
     * 生成校验异常
     *
     * @param error 错误枚举
     * @param detail 错误详细信息
     * @param tClass
     * @return
     */
    public static HsjryCheckException getCheckException(EnumBatchJobError error, String detail, Class tClass,
        Exception e) {
        TenantLog.getLogger(tClass)
            .warn(e, "{}", detail);
        return new HsjryCheckException(error.getCode(), error.getDescription(), tClass);
    }

    /**
     * 生成业务异常，默认流程错误
     *
     * @param error 错误枚举
     * @param detail 错误详细信息
     * @param tClass
     * @return
     */
    public static HsjryBizException getBizException(EnumBatchJobError error, String detail, Class tClass) {
        TenantLog.getLogger(tClass)
            .error("{}", detail);
        return new HsjryBizException(error.getCode(), error.getDescription(), tClass);
    }

    /**
     * 生成业务异常，默认流程错误
     *
     * @param error 错误枚举
     * @param detail 错误详细信息
     * @param tClass
     * @return
     */
    public static HsjryBizException getBizException(EnumBatchJobError error, String detail, Class tClass, Throwable e) {
        TenantLog.getLogger(tClass)
            .error(e, "{}", detail);
        return new HsjryBizException(error.getCode(), error.getDescription(), tClass, e);
    }

    /**
     * 生成业务异常，指定错误类型
     *
     * @param error 错误枚举
     * @param detail 错误详细信息
     * @param tClass
     * @param errorType 异常类型
     * @return
     */
    public static HsjryBizException getBizException(EnumBatchJobError error, String detail, Class tClass,
        EnumErrorType errorType) {
        TenantLog.getLogger(tClass)
            .error("{}", detail);
        return new HsjryBizException(error.getCode(), error.getDescription(), tClass, errorType.getCode(),
            errorType.getDescription());
    }

    /**
     * 生成数据库异常
     *
     * @param error 错误枚举
     * @param detail 错误详细信息
     * @param tClass
     * @return
     */
    public static HsjryDBException getDBException(EnumBatchJobError error, String detail, Class tClass) {
        TenantLog.getLogger(tClass)
            .error("{}", detail);
        return new HsjryDBException(error.getCode(), error.getDescription(), tClass);
    }

    /**
     * 生成数据库异常
     *
     * @param error 错误枚举
     * @param detail 错误详细信息
     * @param tClass
     * @return
     */
    public static HsjryDBException getDBException(EnumBatchJobError error, String detail, Class tClass, Throwable e) {
        TenantLog.getLogger(tClass)
            .error(e, "{}", detail);
        return new HsjryDBException(error.getCode(), error.getDescription(), tClass);
    }

    /**
     * 如果对象为空，自动抛出异常
     *
     * @param obj
     * @param objDescribe
     * @param tClass
     */
    public static void nullCheckException(Object obj, String objDescribe, Class tClass) {
        if (Objects.equal(obj, null)) {
            TenantLog.getLogger(tClass)
                .error("{} 为空", objDescribe);
            String msg = objDescribe + " " + EnumBatchJobError.OBJECT_NULL.getDescription();
            throw new HsjryCheckException(EnumBatchJobError.OBJECT_NULL.getCode(), msg, tClass);
        }
    }

    /**
     * 如果对象为空，自动抛出异常
     *
     * @param obj
     * @param objDescribe
     * @param tClass
     */
    public static void nullBizException(Object obj, String objDescribe, Class tClass) {
        if (Objects.equal(obj, null)) {
            TenantLog.getLogger(tClass)
                .error("{} 为空", objDescribe);
            String msg = objDescribe + " " + EnumBatchJobError.OBJECT_NULL.getDescription();
            throw new HsjryBizException(EnumBatchJobError.OBJECT_NULL.getCode(), msg, tClass);
        }
    }

    /**
     * 如果字段为空，自动抛出BIZ异常
     *
     * @param str 待校验的字段
     * @param strDescribe 字段描述
     * @param tClass 方法执行所在类
     */
    public static void stringBlankBizException(String str, String strDescribe, Class tClass) {
        if (StringUtil.isBlank(str)) {
            TenantLog.getLogger(tClass)
                .error("{} 为空", strDescribe);
            String msg = strDescribe + " " + EnumBatchJobError.PROPERTY_NULL.getDescription();
            throw new HsjryBizException(EnumBatchJobError.PROPERTY_NULL.getCode(), msg, tClass);
        }
    }

    /**
     * 列表对象为空，自动抛出异常
     *
     * @param collection
     * @param objDescribe
     * @param tClass
     */
    public static void listEmptyException(Collection collection, String objDescribe, Class tClass) {
        if (CollectionUtils.isEmpty(collection)) {
            TenantLog.getLogger(tClass)
                .error("{} 列表为空", objDescribe);
            String msg = objDescribe + " " + EnumBatchJobError.OBJECT_NULL.getDescription();
            throw new HsjryBizException(EnumBatchJobError.OBJECT_NULL.getCode(), msg, tClass);
        }
    }

    /**
     * 生成远程调用异常
     *
     * @return
     */
    public static HsjryRpcException getRPCException(EnumBatchJobError error, String detail, Class tClass, Throwable e) {
        TenantLog.getLogger(tClass)
            .error(e, "{}", detail);
        return new HsjryRpcException(error.getCode(), error.getDescription(), tClass);
    }

    /**
     * 获取biz异常
     *
     * @param error 错误枚举
     * @param tClass 错误类
     * @return biz异常
     */
    public static HsjryBizException getBizException(EnumBatchJobError error, Class tClass) {
        throw new HsjryBizException(error.getCode(), error.getDescription(), tClass);
    }
}
