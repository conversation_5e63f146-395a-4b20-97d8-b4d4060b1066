package com.hsjry.core.limit.batch.dal.dao.mapper;

import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpNoCrdtDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 额度中心-中间表-额度重算中对公客户中非授信额度mapper
 *
 * <AUTHOR>
 * @date 2025-08-21 11:31:28
 */
public interface LbTReclcCorpNoCrdtMapper extends CommonMapper<LbTReclcCorpNoCrdtDo> {

    // ==================== 非授信额度重算相关方法 ====================

    /**
     * 6.1.清空非授信额度中间表
     */
    int truncateNoCreditLimit();

    /**
     * 6.2.插入非授信额度客户编号和额度编号
     */
    int insertNoCreditLimit();

    /**
     * 6.3.更新非授信额度中间表金额信息
     */
    int mergeNoCreditLimitAmount();

    /**
     * 6.4.更新额度实例金额信息
     */
    int mergeNoCreditLimitInstance();
}