package com.hsjry.core.limit.batch.biz.job.job.file;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * 信用卡账户文件的同步处理任务
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/11/7 11:41
 */
@Slf4j
@Service("lbSCcsAcctFileSyncJob")
public class LbSCcsAcctFileSyncJob extends AbstractBaseBatchJob {
    
    public LbSCcsAcctFileSyncJob() {
        log.info("LbSCcsAcctFileSyncJob Bean 正在创建...");
    }
    
    @Autowired
    @Qualifier("lbSCcsAcctFileSyncBizImpl")
    private BaseOrdinaryBiz baseOrdinaryBiz;

    @Override
    public BaseOrdinaryBiz getBaseOrdinaryBiz() {
        return baseOrdinaryBiz;
    }

    /**
     * 设置基础业务逻辑对象
     * 
     * @param baseOrdinaryBiz 基础业务逻辑对象
     */
    public void setBaseOrdinaryBiz(BaseOrdinaryBiz baseOrdinaryBiz) {
        this.baseOrdinaryBiz = baseOrdinaryBiz;
    }
} 