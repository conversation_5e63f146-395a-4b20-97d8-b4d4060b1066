package com.hsjry.core.limit.batch.dal.dao.mapper;

import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpTotlDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 额度中心-中间表-额度重算中对公客户中总额度mapper
 *
 * <AUTHOR>
 * @date 2025-08-21 11:31:28
 */
public interface LbTReclcCorpTotlMapper extends CommonMapper<LbTReclcCorpTotlDo> {

    // ==================== 客户总额度重算相关方法 ====================

    /**
     * 7.1.清空客户总额度中间表
     */
    int truncateTotalLimit();

    /**
     * 7.2.插入客户总额度客户编号和额度编号
     */
    int insertTotalLimit();

    /**
     * 7.3.更新客户总额度中间表金额信息
     */
    int mergeTotalLimitAmount();

    /**
     * 7.4.更新额度实例金额信息
     */
    int mergeTotalLimitInstance();
}