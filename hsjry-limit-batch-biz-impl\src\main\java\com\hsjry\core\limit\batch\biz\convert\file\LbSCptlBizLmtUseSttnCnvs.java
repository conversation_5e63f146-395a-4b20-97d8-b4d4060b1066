/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.convert.file;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.hsjry.core.limit.batch.biz.entity.LbSCptlBizLmtUseSttnData;
import com.hsjry.core.limit.batch.dal.dao.model.LbSCptlBizLmtUseSttnDo;

/**
 * 资金系统落地表日终业务额度使用情况文件数据转换器
 * 使用MapStruct进行高性能对象映射
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/15 10:00
 */
@Mapper(componentModel = "spring")
public interface LbSCptlBizLmtUseSttnCnvs {

    LbSCptlBizLmtUseSttnCnvs INSTANCE = Mappers.getMapper(LbSCptlBizLmtUseSttnCnvs.class);

    /**
     * 单个Data转DO
     * 基础转换方法，使用MapStruct自动映射
     *
     * @param data 源数据对象
     * @return 目标DO对象
     */
    LbSCptlBizLmtUseSttnDo data2Do(LbSCptlBizLmtUseSttnData data);

    /**
     * 单个DO转Data（新增方法）
     * 反向转换，用于数据回显或其他场景
     *
     * @param doObj 源DO对象
     * @return 目标Data对象
     */
    LbSCptlBizLmtUseSttnData do2Data(LbSCptlBizLmtUseSttnDo doObj);

    /**
     * Data列表转DO列表
     * 批量转换方法，用于提高批处理性能
     *
     * @param dataList 源数据列表
     * @return 目标DO列表
     */
    List<LbSCptlBizLmtUseSttnDo> dataList2DoList(List<LbSCptlBizLmtUseSttnData> dataList);

    /**
     * DO列表转Data列表
     * 批量反向转换方法
     *
     * @param doList 源DO列表
     * @return 目标Data列表
     */
    List<LbSCptlBizLmtUseSttnData> doList2DataList(List<LbSCptlBizLmtUseSttnDo> doList);
} 