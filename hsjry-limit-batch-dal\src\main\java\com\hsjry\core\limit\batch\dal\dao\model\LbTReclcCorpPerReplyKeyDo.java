package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度中心-中间表-额度重算中对公客户中单笔单批额度主键
 *
 * <AUTHOR>
 * @date 2025-08-21 11:31:28
 */
@Table(name = "lb_t_reclc_corp_per_reply")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LbTReclcCorpPerReplyKeyDo implements Serializable {

    private static final long serialVersionUID = 1958492141684719619L;
    /** 客户编号 */
    @Id
    @Column(name = "cust_no")
    private String custNo;
    /** 额度编号 */
    @Id
    @Column(name = "cust_limit_id")
    private String custLimitId;
}