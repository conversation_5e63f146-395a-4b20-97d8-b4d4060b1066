package com.hsjry.core.limit.batch.common.dto.file;

import java.io.Serializable;

import lombok.Builder;
import lombok.Data;

/**
 * 额度实例所属对象信息dto
 *
 * <AUTHOR>
 * @date 2025-07-09 12:31:05
 */
@Data
@Builder
public class LbCLimitObjectInfoDto implements Serializable {
    private static final long serialVersionUID = 1942924465449140229L;
    /** 租户号 */
    private String tenantId;
    /** 创建时间 */
    private java.util.Date createTime;
    /** 更新时间 */
    private java.util.Date updateTime;
    /** 额度编号 */
    private String custLimitId;
    /** 客户编号 */
    private String userId;
    /** 核心客户编号 */
    private String outUserId;
    /** 客户类型 */
    private String userType;
    /** 客户名称 */
    private String userName;
    /** 客户证件类型 */
    private String userCertificateKind;
    /** 客户证件编号 */
    private String userCertificateNo;
    /** 同业金融产品编号 */
    private String ibFinancialProdId;
    /** 核心同业金融产品编号 */
    private String ibFinancialProdCoreId;
    /** 同业金融产品类型 */
    private String ibFinancialProdType;
    /** 同业金融产品名称 */
    private String ibFinancialProdName;
    /** 同业金融类型 */
    private String ibFinancialType;
    /** 同业金融编号 */
    private String ibFinancialId;
}
