package com.hsjry.core.limit.batch.biz.convert.copy;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.hsjry.core.limit.batch.common.dto.file.LbCLimitRelationDto;
import com.hsjry.core.limit.batch.dal.dao.model.LbCLimitRelationDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitRelationDo;
import com.hsjry.lang.common.utils.DateUtil;
import com.hsjry.lang.common.utils.StringUtil;

/**
 * 额度实例所属对象信息转换类
 *
 * <AUTHOR>
 * @date 2025-07-09 12:31:05
 */
public class LbCLimitRelationConverter {

    /**
     * DTO转DO
     *
     * @param dto DTO对象
     * @return DO对象
     */
    public static LbCLimitRelationDo dtoToDo(LbCLimitRelationDto dto) {
        if (dto == null) {
            return null;
        }
        return LbCLimitRelationCnvs.INSTANCE.dtoToDo(dto);
    }

    /**
     * DO转DTO
     *
     * @param limitRelationDo DO对象
     * @return DTO对象
     */
    public static LbCLimitRelationDto doToDto(LbCLimitRelationDo limitRelationDo) {
        if (limitRelationDo == null) {
            return null;
        }
        return LbCLimitRelationCnvs.INSTANCE.do2Dto(limitRelationDo);
    }

    /**
     * DTO列表转DO列表
     *
     * @param dtoList DTO列表
     * @return DO列表
     */
    public static List<LbCLimitRelationDo> dtoListToDoList(List<LbCLimitRelationDto> dtoList) {
        if (dtoList == null) {
            return null;
        }

        List<LbCLimitRelationDo> doList = new ArrayList<>(dtoList.size());
        for (LbCLimitRelationDto dto : dtoList) {
            doList.add(dtoToDo(dto));
        }
        return doList;
    }

    /**
     * DO列表转DTO列表
     *
     * @param doList DO列表
     * @return DTO列表
     */
    public static List<LbCLimitRelationDto> doListToDtoList(List<LbCLimitRelationDo> doList) {
        if (doList == null) {
            return null;
        }

        List<LbCLimitRelationDto> dtoList = new ArrayList<>(doList.size());
        for (LbCLimitRelationDo limitInfoDo : doList) {
            dtoList.add(doToDto(limitInfoDo));
        }
        return dtoList;
    }

    /**
     * 从字符串数组创建DTO (根据CSV文件头字段顺序)
     *
     * @param fields 字段数组
     * @return DTO对象
     */
    public LbCLimitRelationDto fieldsToDto(String[] fields) {
        if (fields == null || fields.length == 0) {
            return null;
        }
        return LbCLimitRelationDto.builder()
            .tenantId(safeGet(fields, 0))
            .createTime(parseDate(safeGet(fields, 1)))
            .updateTime(parseDate(safeGet(fields, 2)))
            .limitRelationId(safeGet(fields, 3))
            .currentNodeLimitId(safeGet(fields, 4))
            .parentNodeLimitId(safeGet(fields, 5))
            .limitRelationType(safeGet(fields, 6))
            .custNo(safeGet(fields, 7))
            .build();
    }

    /**
     * 安全获取数组元素
     */
    private String safeGet(String[] fields, int index) {
        if (fields.length > index) {
            return trimToNull(fields[index]);
        }
        return null;
    }

    /**
     * 字符串去空格并转换为null
     */
    private String trimToNull(String str) {
        return StringUtil.trimToNull(str);
    }

    /**
     * 解析日期字符串
     */
    private Date parseDate(String dateStr) {
        if (StringUtil.isBlank(dateStr)) {
            return null;
        }
        try {
            // 尝试多种日期格式
            if (dateStr.length() == 8) {
                return DateUtil.parseDate(dateStr, "yyyyMMdd");
            } else if (dateStr.length() == 14) {
                return DateUtil.parseDate(dateStr, "yyyyMMddHHmmss");
            } else if (dateStr.contains("-")) {
                return DateUtil.parseDate(dateStr, "yyyy-MM-dd");
            } else if (dateStr.contains("/")) {
                return DateUtil.parseDate(dateStr, "yyyy/MM/dd");
            }
        } catch (Exception e) {
            // 日期解析失败，返回null
        }
        return null;
    }

    /**
     * 解析BigDecimal
     */
    private BigDecimal parseBigDecimal(String numStr) {
        if (StringUtil.isBlank(numStr)) {
            return null;
        }
        try {
            return new BigDecimal(numStr.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * LcCustLimitObjectInfoDo转LbCLimitObjectInfoDo
     *
     * @param lcCustLimitRelationDo 源DO对象
     * @return 目标DO对象
     */
    public static LbCLimitRelationDo do2Copy(LcCustLimitRelationDo lcCustLimitRelationDo) {
        if (lcCustLimitRelationDo == null) {
            return null;
        }
        return LbCLimitRelationCnvs.INSTANCE.do2Copy(lcCustLimitRelationDo);
    }

    /**
     * LcCustLimitObjectInfoDo列表转LbCLimitObjectInfoDo列表
     *
     * @param lcCustLimitRelationDoList 源DO列表
     * @return 目标DO列表
     */
    public static List<LbCLimitRelationDo> doList2CopyList(List<LcCustLimitRelationDo> lcCustLimitRelationDoList) {
        if (lcCustLimitRelationDoList == null) {
            return null;
        }

        List<LbCLimitRelationDo> lbCLimitRelationDoList = new ArrayList<>(lcCustLimitRelationDoList.size());
        for (LcCustLimitRelationDo lcCustLimitRelationDo : lcCustLimitRelationDoList) {
            lbCLimitRelationDoList.add(do2Copy(lcCustLimitRelationDo));
        }
        return lbCLimitRelationDoList;
    }

    /**
     * Integer转BigDecimal工具方法
     */
    private BigDecimal integerToBigDecimal(Integer value) {
        return value == null ? null : new BigDecimal(value);
    }
}