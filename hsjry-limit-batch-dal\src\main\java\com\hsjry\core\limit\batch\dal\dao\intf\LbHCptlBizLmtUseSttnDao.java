package com.hsjry.core.limit.batch.dal.dao.intf;

import com.hsjry.core.limit.batch.dal.dao.model.LbHCptlBizLmtUseSttnDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbHCptlBizLmtUseSttnQuery;

import java.util.List;

import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 资金日终业务额度使用情况同步表（记录客户额度使用情况）数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbHCptlBizLmtUseSttnDao extends IBaseDao<LbHCptlBizLmtUseSttnDo> {
    /**
     * 分页查询资金日终业务额度使用情况同步表（记录客户额度使用情况）
     *
     * @param lbHCptlBizLmtUseSttnQuery 条件
     * @return PageInfo<LbHCptlBizLmtUseSttnDo>
     */
    PageInfo<LbHCptlBizLmtUseSttnDo> selectPage(LbHCptlBizLmtUseSttnQuery lbHCptlBizLmtUseSttnQuery,
        PageParam pageParam);

    /**
     * 根据key查询资金日终业务额度使用情况同步表（记录客户额度使用情况）
     *
     * @param userId
     * @param dataDate
     * @return
     */
    LbHCptlBizLmtUseSttnDo selectByKey(String userId, String dataDate);

    /**
     * 根据key删除资金日终业务额度使用情况同步表（记录客户额度使用情况）
     *
     * @param userId
     * @param dataDate
     * @return
     */
    int deleteByKey(String userId, String dataDate);

    /**
     * 查询资金日终业务额度使用情况同步表（记录客户额度使用情况）信息
     *
     * @param lbHCptlBizLmtUseSttnQuery 条件
     * @return List<LbHCptlBizLmtUseSttnDo>
     */
    List<LbHCptlBizLmtUseSttnDo> selectByExample(LbHCptlBizLmtUseSttnQuery lbHCptlBizLmtUseSttnQuery);

    /**
     * 新增资金日终业务额度使用情况同步表（记录客户额度使用情况）信息
     *
     * @param lbHCptlBizLmtUseSttn 条件
     * @return int>
     */
    int insertBySelective(LbHCptlBizLmtUseSttnDo lbHCptlBizLmtUseSttn);

    /**
     * 修改资金日终业务额度使用情况同步表（记录客户额度使用情况）信息
     *
     * @param lbHCptlBizLmtUseSttn
     * @return
     */
    int updateBySelective(LbHCptlBizLmtUseSttnDo lbHCptlBizLmtUseSttn);

    /**
     * 修改资金日终业务额度使用情况同步表（记录客户额度使用情况）信息
     *
     * @param lbHCptlBizLmtUseSttn
     * @param lbHCptlBizLmtUseSttnQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbHCptlBizLmtUseSttnDo lbHCptlBizLmtUseSttn,
        LbHCptlBizLmtUseSttnQuery lbHCptlBizLmtUseSttnQuery);

    /**
     * 删除所有记录
     * 用于文件同步前清空目标表
     *
     * @return 删除的记录数
     */
    int deleteAll();

    /**
     * 批量插入资金日终业务额度使用情况同步表记录
     * 用于文件同步批量数据插入
     *
     * @param list 要插入的记录列表
     * @return 插入的记录数
     */
    int batchInsert(List<LbHCptlBizLmtUseSttnDo> list);

    /**
     * 根据数据日期删除记录
     * 用于文件同步前清理指定日期的数据
     *
     * @param dataDate 数据日期
     * @return 删除的记录数
     */
    int deleteByDataDate(String dataDate);
}
