package com.hsjry.core.limit.batch.dal.dao.mapper;

import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIndvOthrDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 额度中心-中间表-额度重算中个人额度中其他额度mapper
 *
 * <AUTHOR>
 * @date 2025-08-21 13:10:51
 */
public interface LbTReclcIndvOthrMapper extends CommonMapper<LbTReclcIndvOthrDo> {

    // ==================== 其他额度重算相关方法 ====================

    /**
     * 2.1.清空其他额度中间表
     */
    int truncateOtherLimit();

    /**
     * 2.2.插入其他额度客户编号和额度编号
     */
    int insertOtherLimit();

    /**
     * 2.3.更新其他额度中间表金额信息
     */
    int mergeOtherLimitAmount();

    /**
     * 2.4.更新额度实例金额信息
     */
    int mergeOtherLimitInstance();
}