/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.entity;

import java.math.BigDecimal;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 网贷系统-历史表-合同信息文件数据实体
 * 对应LB_H_OL_CTR_INFO表结构，用于文件同步作业的数据处理
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/22 16:00
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LbHOlCtrInfoData {

    /** 合同编号 - 主键字段 */
    private String contractId;

    /** 客户编号 */
    private String userId;

    /** 合同名称 */
    private String contractName;

    /** 合同类型[EnumCompactType:9-征信授权查询委托书;19-最高额借款合同;] */
    private String contractType;

    /** 合同下载地址 */
    private String contractFileUrl;

    /** 关联ID */
    private String relationId;

    /** 关联ID类型[EnumRelationIdType] */
    private String relationIdType;

    /** 产品编号 */
    private String productId;

    /** 合同状态[EnumContractStatus:1-待审核;2-已审核;3-已作废;4-已终止;5-已到期;] */
    private String contractStatus;

    /** 关联三方合同编号 */
    private String thirdpartContractId;

    /** 客户姓名 */
    private String userName;

    /** 客户手机 */
    private String userPhone;

    /** 客户证件类型 */
    private String certificateType;

    /** 客户证件号码 */
    private String certificateNo;

    /** 签订时间 */
    private Date signTime;

    /** 有效开始时间 */
    private Date effectiveStartTime;

    /** 有效截止时间 */
    private Date effectiveEndTime;

    /** 合作方编号 */
    private String thirdpartId;

    /** 线上标识[1-线上;0-线下;] */
    private String onlineFlag;

    /** 合同模式[EnumContractMode:1-电子合同;2-纸质合同;] */
    private String contractMode;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 操作人编号 */
    private String operatorId;

    /** 所属组织id */
    private String ownOrganId;

    /** 租户号 - 主键字段 */
    private String tenantId;

    /** 合同额度状态[EnumContractLimitStatus:0-冻结;1-正常;] */
    private String limitStatus;

    /** 支用借据编号 */
    private String loanInvoiceId;

    /** 支用申请编号 */
    private String loanApplyId;

    /** 合同冻结类型[EnumContractLimitMode:1-系统;1-人工;] */
    private String limitMode;

    /** 合同金额 */
    private BigDecimal contractAmount;

    /** 数据日期 - 主键字段 */
    private String dataDate;
} 