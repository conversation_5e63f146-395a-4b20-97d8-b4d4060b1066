package com.hsjry.core.limit.batch.dal.dao.intf;


import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbSCoreBtxpjDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSCoreBtxpjQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 核心系统-落地表-贴现票据信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-08-20 03:17:24
 */
public interface LbSCoreBtxpjDao extends IBaseDao<LbSCoreBtxpjDo> {
    /**
     * 分页查询核心系统-落地表-贴现票据信息
     *
     * @param lbSCoreBtxpjQuery 条件
     * @return PageInfo<LbSCoreBtxpjDo>
     */
    PageInfo<LbSCoreBtxpjDo> selectPage(LbSCoreBtxpjQuery lbSCoreBtxpjQuery, PageParam pageParam);

    /**
     * 根据key查询核心系统-落地表-贴现票据信息
     *
     * @param faredm
     * @param txnjjh
     * @return
     */
    LbSCoreBtxpjDo selectByKey(String faredm, String txnjjh);

    /**
     * 根据key删除核心系统-落地表-贴现票据信息
     *
     * @param faredm
     * @param txnjjh
     * @return
     */
    int deleteByKey(String faredm, String txnjjh);

    /**
     * 查询核心系统-落地表-贴现票据信息信息
     *
     * @param lbSCoreBtxpjQuery 条件
     * @return List<LbSCoreBtxpjDo>
     */
    List<LbSCoreBtxpjDo> selectByExample(LbSCoreBtxpjQuery lbSCoreBtxpjQuery);

    /**
     * 新增核心系统-落地表-贴现票据信息信息
     *
     * @param lbSCoreBtxpj 条件
     * @return int>
     */
    int insertBySelective(LbSCoreBtxpjDo lbSCoreBtxpj);

    /**
     * 修改核心系统-落地表-贴现票据信息信息
     *
     * @param lbSCoreBtxpj
     * @return
     */
    int updateBySelective(LbSCoreBtxpjDo lbSCoreBtxpj);

    /**
     * 修改核心系统-落地表-贴现票据信息信息
     *
     * @param lbSCoreBtxpj
     * @param lbSCoreBtxpjQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbSCoreBtxpjDo lbSCoreBtxpj, LbSCoreBtxpjQuery lbSCoreBtxpjQuery);

    /**
     * 批量插入核心系统贴现票据信息表-落地信息
     *
     * @param lbSCoreBtxpjList 批量数据
     * @return int
     */
    int insertList(List<LbSCoreBtxpjDo> lbSCoreBtxpjList);

    /**
     * 清空核心系统贴现票据信息表-落地所有数据
     *
     * @return int
     */
    int deleteAll();
}
