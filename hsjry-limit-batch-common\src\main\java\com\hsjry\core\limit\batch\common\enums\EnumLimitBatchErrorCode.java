package com.hsjry.core.limit.batch.common.enums;

import com.hsjry.lang.common.stereotype.enums.IEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 额度中心-批量 错误码
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0 2023/4/23 14:03
 */
@Getter
@AllArgsConstructor
public enum EnumLimitBatchErrorCode implements IEnum {
    /** 文件金额币种与实体金额币种应保持一致 */
    FILE_ENTITY_CURRENCY_SAME("LCB1001", "文件金额币种与实体金额币种应保持一致@&@文件金额币种[{}]与实体金额币种[{}]应保持一致"),

    /** 限额规则停用提醒提前天数未配置 */
    DISABLE_ADVANCE_DAY("LCB3001", "限额规则停用提醒提前天数未配置"),
    /** 限额规则不存在 */
    AMT_LIMIT_RULE_NOT_EXIST("LCB3002", "限额规则不存在@&@限额规则[{}]不存在"),
    /** 限额规则已同步存量业务，不可重复同步 */
    AMT_LIMIT_RULE_STOCK_BUSINESS_FLAG_YES("LCB3003", "限额规则已同步存量业务，不可重复同步@&@限额规则[{}]已同步存量业务，不可重复同步"),
    /** 未知的待同步存量业务限额规则 */
    NO_AMT_LIMIT_RULE_STOCK("LCB3004", "未知的待同步存量业务限额规则"),
    /** 限额存量数据重算失败 */
    AMT_LIMIT_STOCK_CAL_FAIL("LCB3005", "限额存量数据重算失败@&@限额明细[{}],序号[{}]，限额存量数据重算失败"),

    /** 借据对应实体不存在 */
    LOAN_INVOICE_ENTITY_NOT_EXIST("LCB5001", "借据对应实体不存在@&@借据[{}]对应实体不存在"),
    /** 借据币种与实体发放币种不一致 */
    LOAN_INVOICE_ENTITY_CURRENCY_NOT_SAME("LCB5002", "借据币种与实体发放币种不一致@&@借据币种[{}]与实体发放币种[{}]不一致"),

    /** 文件分片异常 */
    FILE_SHARDING_ERROR("LCB9001", "文件分片异常"),
    /** 文件分片数据读取异常 */
    FILE_SHARDING_READ_ERROR("LCB9002", "文件分片数据读取异常"),
    /** 文件分片处理异常 */
    FILE_SHARDING_HANDLE_ERROR("LCB9003", "文件分片处理异常"),
    /** 文件数据异常 */
    FILE_DATA_ERROR("LCB9004", "文件数据异常"),
    /** 文件下载异常 */
    FILE_DOWNLOAD_ERROR("LCB9005", "文件下载异常"),
    /** 任务时间格式错误 */
    BUSINESS_DATE_FORMAT_ERROR("LCB9006", "任务时间格式错误"),
    ;

    /** 状态码 */
    private String code;

    /** 状态描述 */
    private String description;

    /**
     * 根据编码查找枚举
     *
     * @param code 编码
     * @return {@link EnumLimitBatchErrorCode } 实例
     **/
    public static EnumLimitBatchErrorCode find(String code) {
        for (EnumLimitBatchErrorCode instance : EnumLimitBatchErrorCode.values()) {
            if (instance.getCode()
                .equals(code)) {
                return instance;
            }
        }
        return null;
    }
}