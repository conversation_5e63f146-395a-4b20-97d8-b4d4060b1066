package com.hsjry.core.limit.batch.biz.convert.copy;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.hsjry.core.limit.batch.common.dto.file.LbCLimitRelationDto;
import com.hsjry.core.limit.batch.dal.dao.model.LbCLimitRelationDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitRelationDo;

/**
 * 额度实例所属对象信息转换器
 *
 * <AUTHOR>
 * @date 2025/7/10
 */
@Mapper(componentModel = "spring")
public interface LbCLimitRelationCnvs {

    LbCLimitRelationCnvs INSTANCE = Mappers.getMapper(LbCLimitRelationCnvs.class);

    LbCLimitRelationDo do2Copy(LcCustLimitRelationDo model);

    LbCLimitRelationDo dtoToDo(LbCLimitRelationDto dto);

    LbCLimitRelationDto do2Dto(LbCLimitRelationDo model);
}
