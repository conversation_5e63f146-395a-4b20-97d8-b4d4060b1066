/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.convert.file;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.hsjry.core.limit.batch.biz.entity.LbSItnstLcInfoData;
import com.hsjry.core.limit.batch.dal.dao.model.LbSItnstLcInfoDo;

/**
 * 国结系统信用证信息文件数据转换器
 * 使用MapStruct进行高性能对象映射
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/11 11:41
 */
@Mapper(componentModel = "spring")
public interface LbSItnstLcInfoCnvs {

    LbSItnstLcInfoCnvs INSTANCE = Mappers.getMapper(LbSItnstLcInfoCnvs.class);

    /**
     * 单个Data转DO
     * 基础转换方法，使用MapStruct自动映射
     *
     * @param data 源数据对象
     * @return 目标DO对象
     */
    LbSItnstLcInfoDo data2Do(LbSItnstLcInfoData data);

    /**
     * 单个DO转Data
     * 反向转换，用于数据回显或其他场景
     *
     * @param model DO对象
     * @return Data对象
     */
    LbSItnstLcInfoData do2Data(LbSItnstLcInfoDo model);

    /**
     * 批量Data转DO列表
     * 利用MapStruct的批量转换能力
     *
     * @param dataList 源数据列表
     * @return 目标DO列表
     */
    List<LbSItnstLcInfoDo> dataListToDoList(List<LbSItnstLcInfoData> dataList);

    /**
     * 批量DO转Data列表
     * 反向批量转换
     *
     * @param modelList DO列表
     * @return Data列表
     */
    List<LbSItnstLcInfoData> doListToDataList(List<LbSItnstLcInfoDo> modelList);

}
