package com.hsjry.core.limit.batch.biz.convert.file;

import com.hsjry.core.limit.batch.biz.entity.LbHCoreBcdhpData;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCoreBcdhpDo;

/**
 * 银承汇票历史表转换器
 * 负责LbHCoreBcdhpData与LbHCoreBcdhpDo之间的转换
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/22 10:30
 */
public class LbHCoreBcdhpConverter {

    /**
     * 将数据实体转换为DAO对象
     *
     * @param data 数据实体
     * @return DAO对象
     */
    public static LbHCoreBcdhpDo data2Do(LbHCoreBcdhpData data) {
        return LbHCoreBcdhpCnvs.INSTANCE.data2Do(data);
    }

    /**
     * 将DAO对象转换为数据实体
     *
     * @param doObj DAO对象
     * @return 数据实体
     */
    public static LbHCoreBcdhpData do2Data(LbHCoreBcdhpDo dataObject) {
        return LbHCoreBcdhpCnvs.INSTANCE.do2Data(dataObject);
    }
} 