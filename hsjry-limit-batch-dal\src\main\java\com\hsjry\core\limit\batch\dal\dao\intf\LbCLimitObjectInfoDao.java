package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbCLimitObjectInfoDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbCLimitObjectInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbCLimitObjectInfoQuery;
import com.hsjry.core.limit.batch.dal.dao.query.LbCLimitObjectInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度实例所属对象信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-03 10:03:12
 */
public interface LbCLimitObjectInfoDao extends IBaseDao<LbCLimitObjectInfoDo> {
    /**
     * 分页查询额度实例所属对象信息
     *
     * @param lbCLimitObjectInfoQuery 条件
     * @return PageInfo<LbCLimitObjectInfoDo>
     */
    PageInfo<LbCLimitObjectInfoDo> selectPage(LbCLimitObjectInfoQuery lbCLimitObjectInfoQuery, PageParam pageParam);

    /**
     * 根据key查询额度实例所属对象信息
     *
     * @param custLimitId
     * @return
     */
    LbCLimitObjectInfoDo selectByKey(String custLimitId);

    /**
     * 根据key删除额度实例所属对象信息
     *
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custLimitId);

    /**
     * 查询额度实例所属对象信息信息
     *
     * @param lbCLimitObjectInfoQuery 条件
     * @return List<LbCLimitObjectInfoDo>
     */
    List<LbCLimitObjectInfoDo> selectByExample(LbCLimitObjectInfoQuery lbCLimitObjectInfoQuery);

    /**
     * 新增额度实例所属对象信息信息
     *
     * @param lbCLimitObjectInfo 条件
     * @return int>
     */
    int insertBySelective(LbCLimitObjectInfoDo lbCLimitObjectInfo);

    /**
     * 修改额度实例所属对象信息信息
     *
     * @param lbCLimitObjectInfo
     * @return
     */
    int updateBySelective(LbCLimitObjectInfoDo lbCLimitObjectInfo);

    /**
     * 修改额度实例所属对象信息信息
     *
     * @param lbCLimitObjectInfo
     * @param lbCLimitObjectInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbCLimitObjectInfoDo lbCLimitObjectInfo,
        LbCLimitObjectInfoQuery lbCLimitObjectInfoQuery);

    /**
     * 批量插入额度实例所属对象信息
     *
     * @param lbCLimitObjectInfoList 批量数据
     * @return int
     */
    @Override
    int insertList(List<LbCLimitObjectInfoDo> lbCLimitObjectInfoList);

    /**
     * 清空额度实例所属对象信息所有数据
     *
     * @return int
     */
    int deleteAll();

    /**
     * 查询分片数据
     *
     * @param query 查询条件
     * @return List<LbCLimitObjectInfoDo>
     */
    List<LbCLimitObjectInfoDo> selectShardList(LbCLimitObjectInfoQuery query);

    /**
     * 获取第一个对象，limit m，1
     *
     * @param query 查询条件
     * @return LbCLimitObjectInfoDo
     */
    LbCLimitObjectInfoDo selectFirstOne(LbCLimitObjectInfoQuery query);

    /**
     * 获取当前组的数据量
     *
     * @param query 查询条件
     * @return Integer
     */
    Integer selectCountByCurrentGroup(LbCLimitObjectInfoQuery query);
}
