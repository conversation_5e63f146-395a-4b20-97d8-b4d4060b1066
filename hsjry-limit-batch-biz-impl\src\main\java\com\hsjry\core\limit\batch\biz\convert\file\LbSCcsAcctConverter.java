/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.convert.file;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.hsjry.core.limit.batch.biz.entity.LbSCcsAcctData;
import com.hsjry.core.limit.batch.dal.dao.model.LbSCcsAcctDo;

import lombok.extern.slf4j.Slf4j;

/**
 * 信用卡落地表文件数据转换器
 * 提供高性能的数据转换功能，支持批量处理和内存优化
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/21
 */
@Slf4j
@Component
public class LbSCcsAcctConverter {

    /** 默认初始容量 */
    private static final int DEFAULT_INITIAL_CAPACITY = 16;
    /** 负载因子 */
    private static final float LOAD_FACTOR = 0.75f;

    /**
     * Data转DO（高性能版本）
     *
     * @param data 源数据对象
     * @return 目标DO对象
     */
    public static LbSCcsAcctDo data2Do(LbSCcsAcctData data) {
        return LbSCcsAcctCnvs.INSTANCE.data2Do(data);
    }

    /**
     * DO转Data（高性能版本）
     *
     * @param dataObject DO对象
     * @return 目标Data对象
     */
    public static LbSCcsAcctData do2Data(LbSCcsAcctDo dataObject) {
        return LbSCcsAcctCnvs.INSTANCE.do2Data(dataObject);
    }

    /**
     * Data列表转DO列表（高性能版本）
     * 使用并行流和预估容量优化性能
     *
     * @param dataList 源数据列表
     * @return 目标DO列表
     */
    public List<LbSCcsAcctDo> dataListToDoList(List<LbSCcsAcctData> dataList) {
        if (Objects.isNull(dataList)) {
            return Collections.emptyList();
        }
        
        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (dataList.size() / LOAD_FACTOR) + 1, DEFAULT_INITIAL_CAPACITY);
        // 使用并行流提升性能
        return dataList.parallelStream()
                .filter(Objects::nonNull)
                .map(LbSCcsAcctConverter::data2Do)
                .collect(Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
    }

    /**
     * DO列表转Data列表（高性能版本）
     * 使用并行流和预估容量优化性能
     *
     * @param doList DO列表
     * @return 目标Data列表
     */
    public List<LbSCcsAcctData> doListToDataList(List<LbSCcsAcctDo> doList) {
        if (Objects.isNull(doList)) {
            return Collections.emptyList();
        }
        
        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (doList.size() / LOAD_FACTOR) + 1, DEFAULT_INITIAL_CAPACITY);
        // 使用并行流提升性能
        return doList.parallelStream()
                .filter(Objects::nonNull)
                .map(LbSCcsAcctConverter::do2Data)
                .collect(Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
    }
} 