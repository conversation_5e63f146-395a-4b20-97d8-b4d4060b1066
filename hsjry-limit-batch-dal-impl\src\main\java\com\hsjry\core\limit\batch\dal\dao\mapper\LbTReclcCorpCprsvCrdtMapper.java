package com.hsjry.core.limit.batch.dal.dao.mapper;

import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpCprsvCrdtDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 额度中心-中间表-额度重算中对公客户中综合授信额度mapper
 *
 * <AUTHOR>
 * @date 2025-08-21 11:31:28
 */
public interface LbTReclcCorpCprsvCrdtMapper extends CommonMapper<LbTReclcCorpCprsvCrdtDo> {

    // ==================== 综合授信额度重算相关方法 ====================

    /**
     * 2.1.清空综合授信额度中间表
     */
    int truncateComprehensiveCreditLimit();

    /**
     * 2.2.插入综合授信额度客户编号和额度编号
     */
    int insertComprehensiveCreditLimit();

    /**
     * 2.3.更新综合授信额度中间表金额信息
     */
    int mergeComprehensiveCreditLimitAmount();

    /**
     * 2.4.更新额度实例金额信息
     */
    int mergeComprehensiveCreditLimitInstance();
}