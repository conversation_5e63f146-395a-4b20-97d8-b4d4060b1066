package com.hsjry.core.limit.batch.dal.dao.intf;

import com.hsjry.core.limit.batch.dal.dao.model.LbHElcblTfrdcnLmtRlseDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbHElcblTfrdcnLmtRlseQuery;

import java.util.List;

import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 电票日终转贴现额度释放同步-历史数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbHElcblTfrdcnLmtRlseDao extends IBaseDao<LbHElcblTfrdcnLmtRlseDo> {
    /**
     * 分页查询电票日终转贴现额度释放同步-历史
     *
     * @param lbHElcblTfrdcnLmtRlseQuery 条件
     * @return PageInfo<LbHElcblTfrdcnLmtRlseDo>
     */
    PageInfo<LbHElcblTfrdcnLmtRlseDo> selectPage(LbHElcblTfrdcnLmtRlseQuery lbHElcblTfrdcnLmtRlseQuery,
        PageParam pageParam);

    /**
     * 根据key查询电票日终转贴现额度释放同步-历史
     *
     * @param batchNumb
     * @param billNumb
     * @param billRangeStart
     * @param billRangeEnd
     * @param dataDate
     * @return
     */
    LbHElcblTfrdcnLmtRlseDo selectByKey(String batchNumb, String billNumb, String billRangeStart, String billRangeEnd,
        String dataDate);

    /**
     * 根据key删除电票日终转贴现额度释放同步-历史
     *
     * @param batchNumb
     * @param billNumb
     * @param billRangeStart
     * @param billRangeEnd
     * @param dataDate
     * @return
     */
    int deleteByKey(String batchNumb, String billNumb, String billRangeStart, String billRangeEnd, String dataDate);

    /**
     * 查询电票日终转贴现额度释放同步-历史信息
     *
     * @param lbHElcblTfrdcnLmtRlseQuery 条件
     * @return List<LbHElcblTfrdcnLmtRlseDo>
     */
    List<LbHElcblTfrdcnLmtRlseDo> selectByExample(LbHElcblTfrdcnLmtRlseQuery lbHElcblTfrdcnLmtRlseQuery);

    /**
     * 新增电票日终转贴现额度释放同步-历史信息
     *
     * @param lbHElcblTfrdcnLmtRlse 条件
     * @return int>
     */
    int insertBySelective(LbHElcblTfrdcnLmtRlseDo lbHElcblTfrdcnLmtRlse);

    /**
     * 修改电票日终转贴现额度释放同步-历史信息
     *
     * @param lbHElcblTfrdcnLmtRlse
     * @return
     */
    int updateBySelective(LbHElcblTfrdcnLmtRlseDo lbHElcblTfrdcnLmtRlse);

    /**
     * 修改电票日终转贴现额度释放同步-历史信息
     *
     * @param lbHElcblTfrdcnLmtRlse
     * @param lbHElcblTfrdcnLmtRlseQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbHElcblTfrdcnLmtRlseDo lbHElcblTfrdcnLmtRlse,
        LbHElcblTfrdcnLmtRlseQuery lbHElcblTfrdcnLmtRlseQuery);

    int deleteByDataDate(String dataDate);
}
