/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.biz;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.hsjry.base.common.fs.service.FileProcessServiceFactory;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobConstant;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.StatisticsData;
import com.hsjry.base.common.job.util.JobUtil;
import com.hsjry.base.common.model.enums.limit.EnumLimitHandlerStatus;
import com.hsjry.base.common.utils.NumberUtils;
import com.hsjry.core.limit.batch.biz.PushShardingPathService;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.core.BusinessDataCore;
import com.hsjry.core.limit.center.dal.dao.intf.LcSliceBatchSerialDao;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.LcSliceBatchSerialQuery;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.exception.JobBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

import lombok.extern.slf4j.Slf4j;

/**
 * 信贷下档远程文件合并(单点执行)
 * 1.下载每个corebatch节点的文件(以ip结尾的文件)
 * 2.合并这些文件
 * 3.最终上传
 */
@Slf4j
@Service
public class MergeCreditFileRemoteBizImpl implements BaseOrdinaryBiz {

    @Autowired
    private LcSliceBatchSerialDao lcSliceBatchSerialDao;
    @Autowired
    protected FileProcessServiceFactory serviceFactory;
    @Autowired
    private PushShardingPathService pushShardingPathService;

    @Override
    public String initBatchSerialNo(JobInitDto jobInitDto) {
        return String.join(JobConstant.BATCH_SEPARATOR, jobInitDto.getTenantId(),
            jobInitDto.getBusinessDate().toString(), JobUtil.parseInpara(jobInitDto.getInPara()).getJobTradeCode());
    }

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.MERGE_CREDIT_REMOTE_FILE;
    }

    @Override
    public void execBaseJob(JobInitDto jobInitDto) {

        //统计分片信息
        StatisticsData statisticsData = statisticsDataByPage(jobInitDto.getBatchSerialNo(),
            JobConstant.BATH_DB_PAGE_SIZE, null);
        //1.整理节点信息，需要从sftp下载每个ip对应的一个文件
        Set<String> execIpSet = statisticsData.getExecIpSet();
        log.info("jobInitDto={}", GsonUtil.obj2Json(jobInitDto));
        String tradeCode = JSONObject.parseObject(jobInitDto.getInPara()).getString("JOB_TRADE_CODE");
        EnumJobTrade enumJobTrade = EnumJobTrade.find(tradeCode);
        //2.下载每个节点合并好的文件,并合并，返回合并后的文件路径
        String absoluteLocalPath = pushShardingPathService.downloadEveryNodeAndMergeFiles(enumJobTrade,
            jobInitDto.getBusinessDate(), execIpSet, serviceFactory);
        File file = new File(absoluteLocalPath);
        //上传
        if (file.exists()) {
            byte[] bFile = null;
            try {
                bFile = Files.readAllBytes(Paths.get(absoluteLocalPath));
            } catch (IOException e) {
                log.error("读取本地客户额度文件失败，path={},e={}", absoluteLocalPath, e);
                throw new HsjryBizException("999999", "读取本地客户额度文件失败");
            }
            String absRemote = pushShardingPathService.getRemoteFilePath(enumJobTrade, jobInitDto.getBusinessDate())
                + File.separator + file.getName();
            serviceFactory.getInstance().uploadFile(absRemote, bFile);
        }
    }

    /**
     * 分页统计
     *
     * @param batchSerialNo
     * @param batchPageSize
     * @param businessDataCoreImpl
     * @return
     */
    private StatisticsData statisticsDataByPage(String batchSerialNo, int batchPageSize,
        BusinessDataCore businessDataCoreImpl) {
        //初始化 统计项
        StatisticsData statisticsDataBo = this.initStatisticsDataBo();

        LcSliceBatchSerialQuery assetSliceBatchSerialQuery = LcSliceBatchSerialQuery.builder().batchSerialNo(
            batchSerialNo).build();
        int startPageNum = 1;
        PageParam pageParam = PageParam.Builder.getInstance()
            .addPageNum(startPageNum)
            .addPageSize(batchPageSize)
            .build();
        boolean hasNextPage;
        do {
            PageInfo<LcSliceBatchSerialDo> sliceBatchSerialDoPageInfoList = lcSliceBatchSerialDao.selectPage(
                assetSliceBatchSerialQuery, pageParam);
            //检查分页数据
            checkPageData(assetSliceBatchSerialQuery, sliceBatchSerialDoPageInfoList);
            List<LcSliceBatchSerialDo> assetSliceBatchSerialDoList = sliceBatchSerialDoPageInfoList.getList();

            statisticsData(statisticsDataBo, assetSliceBatchSerialDoList, businessDataCoreImpl);
            //下次分页 条件
            hasNextPage = sliceBatchSerialDoPageInfoList.isHasNextPage();
            pageParam.setPageNum(++startPageNum);
        } while (hasNextPage);
        return statisticsDataBo;
    }

    /**
     * 多个分片流水 统计 amount 数据
     *
     * @param statisticsDataBo 统计内容
     * @param assetSliceBatchSerialDoList 待统计数据
     */
    private void statisticsData(final StatisticsData statisticsDataBo,
        List<LcSliceBatchSerialDo> assetSliceBatchSerialDoList, BusinessDataCore businessDataCoreImpl) {
        if (CollectionUtil.isNotEmpty(assetSliceBatchSerialDoList)) {
            assetSliceBatchSerialDoList.forEach(assetSliceBatchSerialDo -> {
                //统计每一分片的数据
                statisticsSliceShardingDate(statisticsDataBo, assetSliceBatchSerialDo, businessDataCoreImpl);
            });
        }
    }

    /**
     * 统计每一分片的数据
     * 校验数据状态
     *
     * @param statisticsDataBo 统计数据
     * @param lcSliceBatchSerialDo 单个分片流水数据
     */
    private void statisticsSliceShardingDate(StatisticsData statisticsDataBo, LcSliceBatchSerialDo lcSliceBatchSerialDo,
        BusinessDataCore businessDataCoreImpl) {
        //校验每一条分片流水的状态
        if (!EnumLimitHandlerStatus.SUCCESS.getCode().equals(lcSliceBatchSerialDo.getSharedStatus())) {
            log.info("线上文件汇总统计时校验分片流水数据状态异常！，分片流水为{}",
                GsonUtil.obj2Json(lcSliceBatchSerialDo));
            throw new JobBizException(EnumBatchJobError.SHARDING_INFO_ERROR.getCode(),
                EnumBatchJobError.SHARDING_INFO_ERROR.getDescription(), getClass());
        }
        //TODO: 日终时，发生额文件中，对账数据的统计项，汇总
        // statisticsSliceSharding(statisticsDataBo.getStatisticsData(), statisticsDataBo, lcSliceBatchSerialDo,
        //     businessDataCoreImpl);
        //统计 execIp
        statisticsDataBo.getExecIpSet().add(lcSliceBatchSerialDo.getExecIp());
        //当前任务批次处理成功的数据量
        setSuccessDataCount(statisticsDataBo, lcSliceBatchSerialDo);
    }

    /**
     * （累加）统计 数据分片实际成功处理的条数 总量
     *
     * @param statisticsDataBo 批次流水分片统计
     * @param lcSliceBatchSerialDo 分片流水
     */
    private void setSuccessDataCount(final StatisticsData statisticsDataBo, LcSliceBatchSerialDo lcSliceBatchSerialDo) {
        int number = NumberUtils.getIntValue(statisticsDataBo.getSuccessDataCount()) + NumberUtils.getIntValue(
            lcSliceBatchSerialDo.getSharedPassCount());
        statisticsDataBo.setSuccessDataCount(number);
    }

    /**
     * 检查分页数据 若未查询到数据 异常处理
     *
     * @param lcSliceBatchSerialQuery 分片流水查询条件
     * @param lcSliceBatchSerialDoPageInfo 分片数据信息
     */
    private void checkPageData(LcSliceBatchSerialQuery lcSliceBatchSerialQuery,
        PageInfo<LcSliceBatchSerialDo> lcSliceBatchSerialDoPageInfo) {
        if (lcSliceBatchSerialDoPageInfo == null || lcSliceBatchSerialDoPageInfo.getTotal() < 1) {
            log.info("未查询到分片数据！，查询条件批次总流水为【{}】,{}", lcSliceBatchSerialQuery.getBatchSerialNo(),
                lcSliceBatchSerialQuery.getBatchNum());
            throw new JobBizException(EnumBatchJobError.SHARDING_INFO_ERROR.getCode(),
                EnumBatchJobError.SHARDING_INFO_ERROR.getDescription(), getClass());
        }
    }

    /**
     * 初始化 统计项
     *
     * @return 统计项
     */
    @NonNull
    private StatisticsData initStatisticsDataBo() {
        //IP统计项
        Set<String> execIpSet = new HashSet<>();
        //资产发生额核对文件 统计内容
        Map<String, BigDecimal> sumMap = new TreeMap<>();
        String businessData = null;
        StatisticsData statisticsDataBo = new StatisticsData();
        statisticsDataBo.setStatisticsData(sumMap);
        statisticsDataBo.setExecIpSet(execIpSet);
        statisticsDataBo.setSuccessDataCount(0);
        statisticsDataBo.setBusinessData(businessData);
        return statisticsDataBo;
    }

}
