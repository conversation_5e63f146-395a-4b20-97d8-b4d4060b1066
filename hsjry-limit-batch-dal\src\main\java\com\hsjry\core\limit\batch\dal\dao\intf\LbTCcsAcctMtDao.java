package com.hsjry.core.limit.batch.dal.dao.intf;

import com.hsjry.core.limit.batch.dal.dao.model.LbTCcsAcctMtDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTCcsAcctMtQuery;

import java.util.List;

import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 信用卡-中间表-信用卡额度信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbTCcsAcctMtDao extends IBaseDao<LbTCcsAcctMtDo> {
    /**
     * 分页查询信用卡-中间表-信用卡额度信息
     *
     * @param lbTCcsAcctMtQuery 条件
     * @return PageInfo<LbTCcsAcctMtDo>
     */
    PageInfo<LbTCcsAcctMtDo> selectPage(LbTCcsAcctMtQuery lbTCcsAcctMtQuery, PageParam pageParam);

    /**
     * 根据key查询信用卡-中间表-信用卡额度信息
     *
     * @param xaccount
     * @param bank
     * @return
     */
    LbTCcsAcctMtDo selectByKey(Long xaccount, Integer bank);

    /**
     * 根据key删除信用卡-中间表-信用卡额度信息
     *
     * @param xaccount
     * @param bank
     * @return
     */
    int deleteByKey(Long xaccount, Integer bank);

    /**
     * 查询信用卡-中间表-信用卡额度信息信息
     *
     * @param lbTCcsAcctMtQuery 条件
     * @return List<LbTCcsAcctMtDo>
     */
    List<LbTCcsAcctMtDo> selectByExample(LbTCcsAcctMtQuery lbTCcsAcctMtQuery);

    /**
     * 新增信用卡-中间表-信用卡额度信息信息
     *
     * @param lbTCcsAcctMt 条件
     * @return int>
     */
    int insertBySelective(LbTCcsAcctMtDo lbTCcsAcctMt);

    /**
     * 修改信用卡-中间表-信用卡额度信息信息
     *
     * @param lbTCcsAcctMt
     * @return
     */
    int updateBySelective(LbTCcsAcctMtDo lbTCcsAcctMt);

    /**
     * 修改信用卡-中间表-信用卡额度信息信息
     *
     * @param lbTCcsAcctMt
     * @param lbTCcsAcctMtQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTCcsAcctMtDo lbTCcsAcctMt, LbTCcsAcctMtQuery lbTCcsAcctMtQuery);
}
