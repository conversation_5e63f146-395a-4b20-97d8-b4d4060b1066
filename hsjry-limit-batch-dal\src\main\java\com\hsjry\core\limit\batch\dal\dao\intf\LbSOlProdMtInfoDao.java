package com.hsjry.core.limit.batch.dal.dao.intf;

import com.hsjry.core.limit.batch.dal.dao.model.LbSOlProdMtInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSOlProdMtInfoQuery;

import java.util.List;

import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度产品信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbSOlProdMtInfoDao extends IBaseDao<LbSOlProdMtInfoDo> {
    /**
     * 分页查询额度产品信息
     *
     * @param lbSOlProdMtInfoQuery 条件
     * @return PageInfo<LbSOlProdMtInfoDo>
     */
    PageInfo<LbSOlProdMtInfoDo> selectPage(LbSOlProdMtInfoQuery lbSOlProdMtInfoQuery, PageParam pageParam);

    /**
     * 根据key查询额度产品信息
     *
     * @param creditLimitId
     * @return
     */
    LbSOlProdMtInfoDo selectByKey(String creditLimitId);

    /**
     * 根据key删除额度产品信息
     *
     * @param creditLimitId
     * @return
     */
    int deleteByKey(String creditLimitId);

    /**
     * 查询额度产品信息信息
     *
     * @param lbSOlProdMtInfoQuery 条件
     * @return List<LbSOlProdMtInfoDo>
     */
    List<LbSOlProdMtInfoDo> selectByExample(LbSOlProdMtInfoQuery lbSOlProdMtInfoQuery);

    /**
     * 新增额度产品信息信息
     *
     * @param lbSOlProdMtInfo 条件
     * @return int>
     */
    int insertBySelective(LbSOlProdMtInfoDo lbSOlProdMtInfo);

    /**
     * 修改额度产品信息信息
     *
     * @param lbSOlProdMtInfo
     * @return
     */
    int updateBySelective(LbSOlProdMtInfoDo lbSOlProdMtInfo);

    /**
     * 修改额度产品信息信息
     *
     * @param lbSOlProdMtInfo
     * @param lbSOlProdMtInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbSOlProdMtInfoDo lbSOlProdMtInfo, LbSOlProdMtInfoQuery lbSOlProdMtInfoQuery);
}
