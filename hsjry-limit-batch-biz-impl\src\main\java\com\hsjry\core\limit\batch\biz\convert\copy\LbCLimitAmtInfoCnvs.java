package com.hsjry.core.limit.batch.biz.convert.copy;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.hsjry.core.limit.batch.common.dto.file.LbCLimitAmtInfoDto;
import com.hsjry.core.limit.batch.dal.dao.model.LbCLimitAmtInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitAmtInfoDo;

/**
 * 额度实例金额信息转换器
 *
 * <AUTHOR>
 * @date 2025-07-09 12:31:05
 */
@Mapper(componentModel = "spring")
public interface LbCLimitAmtInfoCnvs {
    LbCLimitAmtInfoCnvs INSTANCE = Mappers.getMapper(LbCLimitAmtInfoCnvs.class);

    LbCLimitAmtInfoDo do2Copy(LcCustLimitAmtInfoDo model);

    LbCLimitAmtInfoDo dtoToDo(LbCLimitAmtInfoDto dto);

    LbCLimitAmtInfoDto do2Dto(LbCLimitAmtInfoDo model);
} 