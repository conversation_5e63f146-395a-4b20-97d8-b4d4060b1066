package com.hsjry.core.limit.batch.dal.dao.intf;

import com.hsjry.core.limit.batch.dal.dao.model.LbSCoreAdkmxDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSCoreAdkmxQuery;

import java.util.List;

import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 核心系统贷款账户交易明细表-落地表（存储贷款账户交易明细历史数据）数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbSCoreAdkmxDao extends IBaseDao<LbSCoreAdkmxDo> {
    /**
     * 分页查询核心系统贷款账户交易明细表-落地表（存储贷款账户交易明细历史数据）
     *
     * @param lbSCoreAdkmxQuery 条件
     * @return PageInfo<LbSCoreAdkmxDo>
     */
    PageInfo<LbSCoreAdkmxDo> selectPage(LbSCoreAdkmxQuery lbSCoreAdkmxQuery, PageParam pageParam);

    /**
     * 根据key查询核心系统贷款账户交易明细表-落地表（存储贷款账户交易明细历史数据）
     *
     * @param faredm
     * @param dkjeju
     * @param mxxhao
     * @return
     */
    LbSCoreAdkmxDo selectByKey(String faredm, String dkjeju, java.math.BigDecimal mxxhao);

    /**
     * 根据key删除核心系统贷款账户交易明细表-落地表（存储贷款账户交易明细历史数据）
     *
     * @param faredm
     * @param dkjeju
     * @param mxxhao
     * @return
     */
    int deleteByKey(String faredm, String dkjeju, java.math.BigDecimal mxxhao);

    /**
     * 查询核心系统贷款账户交易明细表-落地表（存储贷款账户交易明细历史数据）信息
     *
     * @param lbSCoreAdkmxQuery 条件
     * @return List<LbSCoreAdkmxDo>
     */
    List<LbSCoreAdkmxDo> selectByExample(LbSCoreAdkmxQuery lbSCoreAdkmxQuery);

    /**
     * 新增核心系统贷款账户交易明细表-落地表（存储贷款账户交易明细历史数据）信息
     *
     * @param lbSCoreAdkmx 条件
     * @return int>
     */
    int insertBySelective(LbSCoreAdkmxDo lbSCoreAdkmx);

    /**
     * 修改核心系统贷款账户交易明细表-落地表（存储贷款账户交易明细历史数据）信息
     *
     * @param lbSCoreAdkmx
     * @return
     */
    int updateBySelective(LbSCoreAdkmxDo lbSCoreAdkmx);

    /**
     * 修改核心系统贷款账户交易明细表-落地表（存储贷款账户交易明细历史数据）信息
     *
     * @param lbSCoreAdkmx
     * @param lbSCoreAdkmxQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbSCoreAdkmxDo lbSCoreAdkmx, LbSCoreAdkmxQuery lbSCoreAdkmxQuery);
}
