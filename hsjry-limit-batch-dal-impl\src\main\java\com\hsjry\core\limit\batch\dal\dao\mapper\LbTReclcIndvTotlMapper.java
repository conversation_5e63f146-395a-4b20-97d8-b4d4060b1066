package com.hsjry.core.limit.batch.dal.dao.mapper;

import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIndvTotlDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 额度中心-中间表-额度重算中个人额度中客户总额度mapper
 *
 * <AUTHOR>
 * @date 2025-08-21 13:10:51
 */
public interface LbTReclcIndvTotlMapper extends CommonMapper<LbTReclcIndvTotlDo> {

    // ==================== 个人客户总额度重算相关方法 ====================

    /**
     * 3.1.清空个人客户总额度中间表
     */
    int truncateTotalLimit();

    /**
     * 3.2.插入个人客户总额度客户编号和额度编号
     */
    int insertTotalLimit();

    /**
     * 3.3.更新个人客户总额度中间表金额信息
     */
    int mergeTotalLimitAmount();

    /**
     * 3.4.更新额度实例金额信息
     */
    int mergeTotalLimitInstance();
}