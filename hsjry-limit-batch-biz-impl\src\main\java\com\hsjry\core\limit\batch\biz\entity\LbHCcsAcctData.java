/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.entity;

import java.math.BigDecimal;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 信用卡-历史表-第一币种贷记帐户文件数据实体
 *
 * 🎯 专门用于历史表文件解析和数据转换的完整285字段数据结构
 * 对应 LB_H_CCS_ACCT 表结构，确保历史文件数据的完整性和准确性
 *
 * 📈 完整性指标：
 * - 字段覆盖率：286/286 (100% 包含主键+dataDate)
 * - 与DO类映射：完全一致的字段名称和类型
 * - 与枚举定义：按EnumBatchFileType.H_CCS_ACCT顺序排列
 *
 * 🔄 数据流转路径：
 * 历史文件数据 → LbHCcsAcctData → LbHCcsAcctDo → 数据库
 *
 * ⚡ 性能特性：
 * - 支持并行解析处理
 * - 优化的字段类型转换
 * - 内存使用效率优化
 * - 批量处理友好设计
 * - 历史数据按日期分片处理
 *
 * 🛡️ 数据验证：
 * - 字段类型验证：Integer, BigDecimal, String
 * - 业务规则验证：金额范围、日期有效性等
 * - 空值处理：合理的空值策略
 * - 历史数据日期一致性验证
 *
 * <AUTHOR>
 * @version V4.0.1
 * @since 4.0.1 2025/1/21
 */
@Data
@NoArgsConstructor
public class LbHCcsAcctData {

    /** 账号 */
    private Integer xaccount;
    /** 银行 */
    private Integer bank;
    /** 公司编号（公司卡/商务卡使用） */
    private String business;
    /** 帐户类别 */
    private Integer category;
    /** 帐户拥有者证件号码 */
    private String custrNbr;
    /** 帐单日 */
    private Integer cycleNbr;
    /** 开户日期 */
    private Integer dayOpened;
    /** 帐户名称 */
    private String accName1;
    /** 帐户年费/管理费代码 */
    private String accType;
    /** 最近地址修改日期 */
    private Integer addChgday;
    /** 帐单地址类型 */
    private String addrType;
    /** 第二帐单地址类型 */
    private String addrType2;
    /** 逾期金额1 */
    private BigDecimal age1;
    /** 逾期金额2 */
    private BigDecimal age2;
    /** 逾期金额3 */
    private BigDecimal age3;
    /** 逾期金额4 */
    private BigDecimal age4;
    /** 逾期金额5 */
    private BigDecimal age5;
    /** 逾期金额6 */
    private BigDecimal age6;
    /** 发卡专案代码 */
    private String appSource;
    /** 本期预授权的预借现金总额（没有用） */
    private BigDecimal authCash;
    /** 超额授权允许的百分比 */
    private BigDecimal authOver;
    /** 帐户超额授权允许开关 */
    private String authovYn;
    /** 授权未请款金额（用此字段） */
    private BigDecimal authsAmt;
    /** 复利余额 */
    private BigDecimal balCmpint;
    /** 消费余额（未出账单组成） */
    private BigDecimal balFree;
    /** 日记息余额（未出账单组成） */
    private BigDecimal balInt;
    /** 日记息余额符号 */
    private String balIntflag;
    /** 不记息余额（未出账单组成） */
    private BigDecimal balNoint;
    /** 利息余额（未出账单组成） */
    private BigDecimal balOrint;
    /** 自扣还款账号1 */
    private String bankacct1;
    /** 自扣还款账号关联代码1 */
    private String bankcode1;
    /** 自扣还款账号2 */
    private String bankacct2;
    /** 自扣还款账号关联代码2 */
    private String bankcode2;
    /** 自扣还款账号3 */
    private String bankacct3;
    /** 自扣还款账号关联代码3 */
    private String bankcode3;
    /** 自扣还款账号4 */
    private String bankacct4;
    /** 自扣还款账号关联代码4 */
    private String bankcode4;
    /** 分行 */
    private Integer branch;
    /** 最近修改分行日期 */
    private Integer branchDay;
    /** 预借现金比例 */
    private BigDecimal caPcnt;
    /** 年费 */
    private BigDecimal cardFees;
    /** 已挂失卡片数量 */
    private Integer cardsCanc;
    /** 帐户关联卡片数量 */
    private Integer cardsIssd;
    /** 第一次交易为预借现金日期 */
    private Integer cash1st;
    /** 预借现金手续费 */
    private BigDecimal cashAdfee;
    /** 预借现金金额 */
    private BigDecimal cashAdvce;
    /** 今日预借现金金额 */
    private BigDecimal cashTday;
    /** 第一次预借现金日期 */
    private Integer cash1stac;
    /** 客户类别更改日期 */
    private Integer classChdy;
    /** 客户类别 */
    private String classCode;
    /** 最近关账日期 */
    private Integer closeChdy;
    /** 帐户状态（关帐代码） */
    private String closeCode;
    /** 账户进入COLL队列日期 */
    private Integer collsDay;
    /** 信用处理日期（暂无用） */
    private Integer crdactDay;
    /** 下次信用处理日期（暂无用） */
    private Integer crdnxtDay;
    /** 对此账户采取过的行动（暂无用） */
    private String credActiv;
    /** 贷记调整金额 */
    private BigDecimal credAdj;
    /** 信用额度更改日期 */
    private Integer credChday;
    /** 帐户信用额度 */
    private Integer credLimit;
    /** 退货金额（退已出帐单消费金额） */
    private BigDecimal credVouch;
    /** 外币信用额度（共享额度时无效） */
    private Integer credlimX;
    /** 货币代码 */
    private Integer currNum;
    /** 到期还款处理日 */
    private Integer cutoffDay;
    /** 账单日已修改次数 */
    private Integer cyChgcnt;
    /** 账单日修改日期 */
    private Integer cyChgday;
    /** 结帐还款周期生效日期 */
    private Integer cyEffday;
    /** 一年内修改次数 */
    private Integer cyYrcnt;
    /** 新账单日 */
    private Integer cycleNew;
    /** 前账单日 */
    private Integer cyclePrv;
    /** 借记调整金额 */
    private BigDecimal debitAdj;
    /** 第一次自动扣款失败的日期 */
    private Integer dishnrday;
    /** 自前次帐单起，税贷方余额（暂无用） */
    private BigDecimal dutyCredt;
    /** 自前次帐单起，税借方余额（暂无用） */
    private BigDecimal dutyDebit;
    /** 购汇还款代码 */
    private String exchCode;
    /** 购汇还款功能 */
    private String exchFlag;
    /** 购汇比例 */
    private Integer exchPerc;
    /** 购售汇还款汇率选项 */
    private Integer exchRtdt;
    /** 要收年费的月份 */
    private Integer feeMonth;
    /** 费用及稅款合计（暂无用） */
    private BigDecimal feesTaxes;
    /** 固定购汇金额 */
    private BigDecimal fixExamt;
    /** 担保标志 */
    private Integer guarnFlag;
    /** 预借现金的最高金额 */
    private Integer hiCashadv;
    /** 最高金额预借现金的日期 */
    private Integer hiCasmmyy;
    /** 最高贷方金额的日期 */
    private Integer hiCrdmmyy;
    /** 最高的贷方金额 */
    private Integer hiCredit;
    /** 最高的借方金额 */
    private Integer hiDebit;
    /** 最高借方金额的日期 */
    private Integer hiDebmmyy;
    /** 最高的分期付款金额 */
    private BigDecimal hiMpPur;
    /** 最高的分期付款日期 */
    private Integer hiMpmmyy;
    /** 最高的超限金额 */
    private Integer hiOlimit;
    /** 最高的超限日期 */
    private Integer hiOlimmyy;
    /** 最高消费金额 */
    private Integer hiPurchse;
    /** 最高消费的日期 */
    private Integer hiPurmmyy;
    /** 取现金额应收利息 */
    private BigDecimal intCash;
    /** 应收复利 */
    private BigDecimal intChdcmp;
    /** 最近一次利率代码修改日期 */
    private Integer intChdy;
    /** 应收透支利息 */
    private BigDecimal intChgd;
    /** 应计复利利息 */
    private BigDecimal intCmpond;
    /** 利息代码 */
    private Integer intCode;
    /** 当期应计利息 */
    private BigDecimal intCunot;
    /** 当期应计复利 */
    private BigDecimal intCurcmp;
    /** 存款利息 */
    private BigDecimal intEarned;
    /** 上期应计利息 */
    private BigDecimal intNotion;
    /** 记息处理日期 */
    private Integer intProcdy;
    /** 透支利率 */
    private BigDecimal intRate;
    /** （暂不用） */
    private BigDecimal intRatecr;
    /** 最近一次计息日期 */
    private Integer intUptody;
    /** 语言代码 */
    private Integer langCode;
    /** 最近一次交易的产生日期 */
    private Integer lastTrday;
    /** 最近一次授权交易的日期 */
    private Integer lastauthdy;
    /** 最近一次爭议款产生日期 */
    private Integer lastntdate;
    /** 最近一次缴款金额 */
    private BigDecimal lastpayamt;
    /** 最近一次缴款日期 */
    private Integer lastpayday;
    /** 转销戶的帐戶余额 */
    private BigDecimal losses;
    /** 当前帐单月份 */
    private Integer monthNbr;
    /** 最近一次监控代码修改日期 */
    private Integer montrChdy;
    /** 监控代码 */
    private String montrCode;
    /** 今日分期付款金额 */
    private BigDecimal mpAmTdy;
    /** 分期付款授权总额 */
    private BigDecimal mpAuths;
    /** 分期付款目前余额 */
    private BigDecimal mpBal;
    /** 分期付款本期分摊金额 */
    private BigDecimal mpBilAmt;
    /** 分期付款信用额度 */
    private Integer mpLimit;
    /** 分期付款当日发生笔数 */
    private Integer mpNoTdy;
    /** 分期付款目前剩余本金 */
    private BigDecimal mpRemPpl;
    /** 分期付款额度修改日期 */
    private Integer mplmChday;
    /** 当前逾期期数 */
    private Integer mthsOdue;
    /** 本次帐期的预借现金笔数 */
    private Integer nbrCashad;
    /** 本次帐期的费用及稅款笔数 */
    private Integer nbrFeedty;
    /** 本次帐期帐戶已超额後的交易次数 */
    private Integer nbrOlimit;
    /** 本次帐期的其他费用笔数 */
    private Integer nbrOthers;
    /** 本次帐期的缴款笔数 */
    private Integer nbrPaymnt;
    /** 本次帐期的一般消费笔数 */
    private Integer nbrPurch;
    /** 本次帐期的所有交易笔数 */
    private Integer nbrTrans;
    /** 进入OCT队列次数 */
    private Integer octCount;
    /** 进入OCT队列的日期 */
    private Integer octDayin;
    /** 贷款逾期状态标志 */
    private Integer odueFlag;
    /** 已逾期但未达到逾期最小金额的金额 */
    private BigDecimal odueHeld;
    /** 当期超限状态标志 */
    private Integer olflag;
    /** 其它费用总额 */
    private BigDecimal otherFees;
    /** 已全额还款标志 */
    private String payFlag;
    /** 第一次缴款的标示 */
    private String pay1stInd;
    /** 当期已还款金额 */
    private BigDecimal paymtClrd;
    /** 当日还款金额 */
    private BigDecimal paymtTday;
    /** 未出帐单的还款在途金额（暂无用） */
    private BigDecimal paymtUncl;
    /** 已收滞纳金金额 */
    private BigDecimal penChrg;
    /** 应收滞纳金金额 */
    private BigDecimal penchgAcc;
    /** 当期调整积分 */
    private Integer pointAdj;
    /** 当期调整积分符号 */
    private String ptAdjflag;
    /** 当期已兑换积分 */
    private Integer pointClm;
    /** 累计可兑换积分 */
    private Integer pointCum;
    /** 累计可兑换积分符号 */
    private String ptCumflag;
    /** 累计新增的不可兑换积分 */
    private Integer pointCum2;
    /** 当期新增积分 */
    private Integer pointEar;
    /** 积分冻结标志 */
    private String pointFrez;
    /** 积分冻结日期 */
    private Integer pointFzda;
    /** （暂无用） */
    private Integer postDd;
    /** 上次分行修改的日期 */
    private Integer prevBrday;
    /** 前分行编号 */
    private Integer prevBrnch;
    /** 当期消费金额 */
    private BigDecimal purchases;
    /** 未出帐单的质疑金额总计 */
    private BigDecimal queryAmt;
    /** 交易质疑代码 */
    private String queryCode;
    /** 显示质疑金额的帐单日期 */
    private Integer queryStmt;
    /** 细分类代码修改日期 */
    private Integer reclaChdy;
    /** 细分类代码 */
    private String reclaCode;
    /** 已销戶沖回金额（暂无用） */
    private BigDecimal recvryAmt;
    /** 自动扣款金额 */
    private BigDecimal repayAmt;
    /** 外币自扣还款金额 */
    private BigDecimal repayAmtx;
    /** 本币自扣还款代码 */
    private String repayCode;
    /** 外币自扣还款代码 */
    private String repayCodx;
    /** 自动扣款日 */
    private Integer repayDay;
    /** 自动扣款百分比 */
    private Integer repayPct;
    /** 外币自动扣款百分比 */
    private Integer repayPctx;
    /** 最近一次自动扣款日修改的日期 */
    private Integer repyChgdy;
    /** 申请评分 */
    private Integer scorePts;
    /** 帐单金额太小而不出帐单的次数 */
    private Integer statements;
    /** 最近一期已超额的帐单金额 */
    private BigDecimal stmAmtOl;
    /** 帐单消费余额 */
    private BigDecimal stmBalfre;
    /** 帐单日记息余额 */
    private BigDecimal stmBalint;
    /** 帐单日记息余额符号 */
    private String stmbalintflag;
    /** 帐单总余额 */
    private BigDecimal stmBalnce;
    /** 账单总余额符号 */
    private String stmBalflag;
    /** 帐单利息余额 */
    private BigDecimal stmBalori;
    /** 最近一期帐单的結帐日 */
    private Integer stmClosdy;
    /** 帐单代码 */
    private String stmCode;
    /** 当期新增最小还款额金额，扣除上期最小还款额 */
    private BigDecimal stmInstl;
    /** 帐单最小还款额 */
    private BigDecimal stmMindue;
    /** 帐单免息余额 */
    private BigDecimal stmNoint;
    /** 帐单超限标志 */
    private Integer stmOlflag;
    /** 最近一期帐单的逾期金额 */
    private BigDecimal stmOverdu;
    /** 最近一期帐单的未结清缴款金额 */
    private BigDecimal stmPayUn;
    /** 最近一期帐单的质疑金额 */
    private BigDecimal stmQryamt;
    /** 最近一期帐单的自动扣款金额 */
    private BigDecimal stmRepay;
    /** 帐单产生日 */
    private Integer stmtDd;
    /** 处理帐单标志（暂无用） */
    private String stmtPull;
    /** 今日过帐交易总金额（除还款及预借现金） */
    private BigDecimal todayAmt;
    /** 今日交易总金额（除还款及预借现金） */
    private String todayAmtflag;
    /** 过帐交易总金额输入日期 */
    private Integer todayRel;
    /** 未实现的缴款金额比例 */
    private BigDecimal unclPct;
    /** 核销的日期 */
    private Integer wroffChdy;
    /** 核销代码 */
    private String wroffCode;
    /** 卡片最高卡种级别 */
    private Integer prodLevel;
    /** 卡片最高卡种产品编号 */
    private Integer prodNbr;
    /** 帐户卡片前次状态 */
    private String acctPrsts;
    /** 帐户卡片状态 */
    private String acctSts;
    /** 首次申请队列代码 */
    private String appApque;
    /** 当期奖励积分 */
    private Integer pointEnc;
    /** 当期合作单位转入积分 */
    private Integer pointImp;
    /** 当期合作单位转出积分 */
    private Integer pointExp;
    /** 当期合作单位转出积分符号 */
    private String ptExpflag;
    /** 进入风控队列次数 */
    private Integer riskCount;
    /** 进入风控队列日期 */
    private Integer riskDayin;
    /** 分期付款未出帐单余额 */
    private BigDecimal balMp;
    /** 分期付款未出帐单余额符号 */
    private String balMpflag;
    /** 分期付款已出帐单余额 */
    private BigDecimal stmBalmp;
    /** 分期付款已出帐单余额符号 */
    private String stmBmflag;
    /** 临时额度调整原因 */
    private String lmtRsn;
    /** 联名卡卡片积分累计 */
    private Integer pointCard;
    /** 联名卡卡片积分累计符号 */
    private String ptCdflag;
    /** 临时额度 */
    private Integer tempLimit;
    /** 临时额度生效日期 */
    private Integer tlmtBeg;
    /** 临时额度失效日期 */
    private Integer tlmtEnd;
    /** 临时额度生效期内调整次数 */
    private Integer tlmtNo;
    /** 核销原因代码 */
    private String canclResn;
    /** 停记滞纳金 */
    private BigDecimal shadowPen;
    /** 停记透支利息 */
    private BigDecimal shadowInt;
    /** 停记复利 */
    private BigDecimal shadowCmp;
    /** 本期应收费用1 */
    private BigDecimal balNint01;
    /** 本期应收费用2 */
    private BigDecimal balNint02;
    /** 本期应收费用3 */
    private BigDecimal balNint03;
    /** 本期应收费用4 */
    private BigDecimal balNint04;
    /** 本期应收费用5 */
    private BigDecimal balNint05;
    /** 本期应收费用6 */
    private BigDecimal balNint06;
    /** 本期应收费用7 */
    private BigDecimal balNint07;
    /** 本期应收费用8 */
    private BigDecimal balNint08;
    /** 本期应收费用9 */
    private BigDecimal balNint09;
    /** 本期应收费用10 */
    private BigDecimal balNint10;
    /** 上期应收费用1 */
    private BigDecimal stmNint01;
    /** 上期应收费用2 */
    private BigDecimal stmNint02;
    /** 上期应收费用3 */
    private BigDecimal stmNint03;
    /** 上期应收费用4 */
    private BigDecimal stmNint04;
    /** 上期应收费用5 */
    private BigDecimal stmNint05;
    /** 上期应收费用6 */
    private BigDecimal stmNint06;
    /** 上期应收费用7 */
    private BigDecimal stmNint07;
    /** 上期应收费用8 */
    private BigDecimal stmNint08;
    /** 上期应收费用9 */
    private BigDecimal stmNint09;
    /** 上期应收费用10 */
    private BigDecimal stmNint10;
    /** 外币币种 */
    private Integer currNum2;
    /** 核销处理标记 */
    private Integer wrofFlag;
    /** 账户分层代码1 */
    private String layercoder1;
    /** 账户分层代码2 */
    private String layercoder2;
    /** 商户类型和商户代码交易控制选项 */
    private String mcntrlYn;
    /** 不上报征信数据原因 */
    private String ncredRsn;
    /** 基本额度 */
    private Integer bscCred;
    /** 客户参考资料编号 */
    private String custrRef;
    /** 征信机构代码 */
    private String pbcBrnch;
    /** 停止分期业务 */
    private Integer stopmpYn;
    /** 综合授信额度 */
    private Integer credLmt2;
    /** 表外费用 */
    private BigDecimal balCmpfee;
    /** 本期大额分期余额 */
    private BigDecimal balLmp;
    /** 上期大额分期余额 */
    private BigDecimal stmLmp;
    /** 到期年月 */
    private Integer outMonth;
    /** 记录变更日期 */
    private Integer etlDay;
    /** 止付原因 */
    private Integer stpay;
    /** 不允许调高账户额度标识 */
    private String adjFlag;
    /** 用卡专案代码 */
    private String useSource;
    /** 当前利率代码失效期 */
    private Integer itcdEnddy;
    /** 账户新利率代码 */
    private Integer intCdnew;
    /** 新利率代码生效期 */
    private Integer itcnBegdy;
    /** 新利率代码失效期 */
    private Integer itcnEnddy;
    /** 利率代码有效期 */
    private Integer intEffect;
    /** 打标日期 */
    private Integer markDay;
    /** 上上期消费计息金额 */
    private BigDecimal pstmBalfr;
    /** 上上期分期计息金额 */
    private BigDecimal pstmBalmp;
    /** 上上期表内利息计息金额 */
    private BigDecimal pstmBalor;
    /** 上上期表外利息计息金额 */
    private BigDecimal pbalCmpin;
    /** 账户预借现金最高限额 */
    private Integer maxCash;
    /** 当日还款限额 */
    private BigDecimal paytdyLmt;
    /** 开户时间 */
    private String glbDtime;
    /** 取现应收利息 */
    private BigDecimal intChgdC;
    /** 本期取现利息余额 */
    private BigDecimal balOrintc;
    /** 上期取现利息余额 */
    private BigDecimal stmBalorc;
    /** 开通大额子额度功能 */
    private Integer sublmtL;
    /** 本期现金分期余额 */
    private BigDecimal balBmp;
    /** 本期现金分期余额符号位 */
    private String balBmpFlg;
    /** 上期现金分期余额 */
    private BigDecimal stmBmp;
    /** 上期现金分期余额符号位 */
    private String stmBmpFlg;
    /** 其他费用总额符号位 */
    private String otherFeesFlg;
    /** 单独核算利息子余额细分项 */
    private String orintsArr;
    /** 单独核算不计息表外利息 */
    private BigDecimal balNcmpis;
    /** 上上期单独核算不计息表内利息 */
    private BigDecimal pstmNoris;
    /** 上上期单独核算不计息表外利息 */
    private BigDecimal pbalNcmps;
    /** 是否开通超限功能 */
    private String overamtYn;
    /** 功能标志 */
    private String infoFlag;
    /** 最小还款额按比例计算部分 */
    private Integer mindueTyp;
    /** 费用及稅款合计符号位 */
    private String feesTaxesFlg;
    /** 数据日期 */
    private String dataDate;
}
