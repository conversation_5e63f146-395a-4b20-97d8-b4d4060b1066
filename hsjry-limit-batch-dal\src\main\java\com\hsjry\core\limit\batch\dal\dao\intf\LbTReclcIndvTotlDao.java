package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIndvTotlDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcIndvTotlQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中个人额度中客户总额度数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-08-21 13:10:51
 */
public interface LbTReclcIndvTotlDao extends IBaseDao<LbTReclcIndvTotlDo> {
    /**
     * 分页查询额度中心-中间表-额度重算中个人额度中客户总额度
     *
     * @param lbTReclcIndvTotlQuery 条件
     * @return PageInfo<LbTReclcIndvTotlDo>
     */
    PageInfo<LbTReclcIndvTotlDo> selectPage(LbTReclcIndvTotlQuery lbTReclcIndvTotlQuery, PageParam pageParam);

    /**
     * 根据key查询额度中心-中间表-额度重算中个人额度中客户总额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    LbTReclcIndvTotlDo selectByKey(String custNo, String custLimitId);

    /**
     * 根据key删除额度中心-中间表-额度重算中个人额度中客户总额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custNo, String custLimitId);

    /**
     * 查询额度中心-中间表-额度重算中个人额度中客户总额度信息
     *
     * @param lbTReclcIndvTotlQuery 条件
     * @return List<LbTReclcIndvTotlDo>
     */
    List<LbTReclcIndvTotlDo> selectByExample(LbTReclcIndvTotlQuery lbTReclcIndvTotlQuery);

    /**
     * 新增额度中心-中间表-额度重算中个人额度中客户总额度信息
     *
     * @param lbTReclcIndvTotl 条件
     * @return int>
     */
    int insertBySelective(LbTReclcIndvTotlDo lbTReclcIndvTotl);

    /**
     * 修改额度中心-中间表-额度重算中个人额度中客户总额度信息
     *
     * @param lbTReclcIndvTotl
     * @return
     */
    int updateBySelective(LbTReclcIndvTotlDo lbTReclcIndvTotl);

    /**
     * 修改额度中心-中间表-额度重算中个人额度中客户总额度信息
     *
     * @param lbTReclcIndvTotl
     * @param lbTReclcIndvTotlQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTReclcIndvTotlDo lbTReclcIndvTotl, LbTReclcIndvTotlQuery lbTReclcIndvTotlQuery);

    // ==================== 个人客户总额度重算相关方法 ====================

    /**
     * 3.1.清空个人客户总额度中间表
     */
    int truncateTotalLimit();

    /**
     * 3.2.插入个人客户总额度客户编号和额度编号
     */
    int insertTotalLimit();

    /**
     * 3.3.更新个人客户总额度中间表金额信息
     */
    int mergeTotalLimitAmount();

    /**
     * 3.4.更新额度实例金额信息
     */
    int mergeTotalLimitInstance();
}
