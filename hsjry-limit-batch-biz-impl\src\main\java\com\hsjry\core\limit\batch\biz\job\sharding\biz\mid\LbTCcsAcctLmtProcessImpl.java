package com.hsjry.core.limit.batch.biz.job.sharding.biz.mid;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.common.EnumCertificateKind;
import com.hsjry.base.common.model.enums.customer.EnumUserType;
import com.hsjry.base.common.model.enums.limit.EnumCustLimitOperateStatus;
import com.hsjry.base.common.model.enums.limit.EnumCustLimitOperateType;
import com.hsjry.base.common.model.enums.limit.EnumCustLimitStatus;
import com.hsjry.base.common.model.enums.limit.EnumExcessOccupationType;
import com.hsjry.base.common.model.enums.limit.EnumLimitClassification;
import com.hsjry.base.common.model.enums.limit.EnumLimitGrantType;
import com.hsjry.base.common.model.enums.limit.EnumLimitLevel;
import com.hsjry.base.common.model.enums.limit.EnumLimitRelationType;
import com.hsjry.base.common.model.enums.limit.EnumLimitTermUnit;
import com.hsjry.base.common.model.enums.limit.EnumLmtTplNode;
import com.hsjry.base.common.model.enums.limit.EnumOccupationType;
import com.hsjry.base.common.model.enums.limit.EnumUsageType;
import com.hsjry.core.limit.batch.biz.convert.mid.LbTCcsAcctLmtConverter;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.limit.AbstractShardingPrepareBiz;
import com.hsjry.core.limit.batch.common.constants.LimitBatchConstants;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.common.enums.EnumLimitTemplateId;
import com.hsjry.core.limit.batch.common.utils.GenerateUtils;
import com.hsjry.core.limit.batch.dal.dao.intf.LbSCcsAcctDao;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTCcsAcctLmtDao;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTIndvCustInfoDao;
import com.hsjry.core.limit.batch.dal.dao.model.LbSCcsAcctDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTCcsAcctLmtDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTIndvCustInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSCcsAcctQuery;
import com.hsjry.core.limit.batch.dal.dao.query.LbTIndvCustInfoQuery;
import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitAmtInfoDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitInfoDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitObjectInfoDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitOperateSerialDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitRelationDao;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitAmtInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitObjectInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitOperateSerialDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitRelationDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitAmtInfoQuery;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitInfoQuery;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitObjectInfoQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.stereotype.enums.EnumBool;
import com.hsjry.lang.common.utils.DateUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 信用卡额度信息处理分片实现类
 * 负责从lb_s_ccs_acct表读取数据,计算可用余额,关联客户编号,并写入lb_t_ccs_acct_lmt表
 *
 * <AUTHOR>
 * @date 2025-07-10 12:31:05
 */
@Slf4j
@Service("lbTCcsAcctLmtProcessImpl")
@RequiredArgsConstructor
public class LbTCcsAcctLmtProcessImpl extends AbstractShardingPrepareBiz<LbSCcsAcctQuery>
    implements JobCoreBusiness<LbSCcsAcctDo> {

    private final LbSCcsAcctDao lbSCcsAcctDao;
    private final LbTCcsAcctLmtDao lbTCcsAcctLmtDao;
    private final LbTIndvCustInfoDao lbTIndvCustInfoDao;
    private final LcCustLimitInfoDao custLimitInfoDao;
    private final LcCustLimitAmtInfoDao custLimitAmtInfoDao;
    private final LcCustLimitRelationDao custLimitRelationDao;
    private final LcCustLimitObjectInfoDao custLimitObjectInfoDao;
    private final LcCustLimitOperateSerialDao custLimitOperateSerialDao;

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.LB_T_CCS_ACCT_LMT_PROCESS;
    }

    @Override
    public Integer selectCountByCurrentGroupFromDb(LbSCcsAcctQuery query) {
        // 对于全量数据处理,返回固定值1,表示只需要一个分片处理
        return 1;
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);
        log.info(prefixLog + "====================== 分片逻辑 start ================================================");

        List<JobShared> jobSharedList = Lists.newArrayList();
        // SQL批处理数量,暂定为分片数量
        Integer batchFixNum = jobInitDto.getFixNum();

        // 构造查询条件,查询当前分批处理的排序最大对象
        LbSCcsAcctQuery query = LbSCcsAcctQuery.builder().build();

        // 分片流水
        int batchNum = 0;

        // 为单一分片数据处理模式生成分片
        batchNum = countBatchNum(batchFixNum, query, null, batchNum, jobInitDto, jobSharedList, "0", false);

        log.info(prefixLog + "====================== 分片逻辑 end ================================================");
        return jobSharedList;
    }

    @Override
    public ShardingResult<LbSCcsAcctDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {

        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info(prefixLog + "开始查询分片数据,分片号:{}", jobShared.getBatchNum());

        ShardingResult<LbSCcsAcctDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);

        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }

        // 创建查询条件 - 查询所有信用卡账户数据
        LbSCcsAcctQuery query = LbSCcsAcctQuery.builder().build();

        log.info(prefixLog + "=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");

        // 查询所有信用卡账户数据
        List<LbSCcsAcctDo> dataList = lbSCcsAcctDao.selectByExample(query);
        shardingResult.setShardingResultList(dataList);

        log.info("分片数据查询完成,分片号:{},数据量:{}", jobShared.getBatchNum(),
            CollectionUtils.isEmpty(dataList) ? 0 : dataList.size());

        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LbSCcsAcctDo> shardingResult) {
        // 获取分片数据列表
        List<LbSCcsAcctDo> ccsAcctDataList = shardingResult.getShardingResultList();
        Integer batchNum = shardingResult.getJobShared().getBatchNum();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            shardingResult.getJobShared().getBusinessDate(), getJobTrade().getCode(), getJobTrade().getDescription(),
            shardingResult.getJobShared().getBatchSerialNo());

        log.info(prefixLog + "=========分片执行开始:[{}]===========", batchNum);

        // 判空处理，若分片数据为空直接返回
        if (CollectionUtils.isEmpty(ccsAcctDataList)) {
            log.info(prefixLog + "=========分片执行结束:分片号[{}]数量为空===========", batchNum);
            return;
        }

        try {
            LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();

            // 只在第一个分片中清空目标表
            if (sliceBatchSerialDo.getBatchNum() == 1) {
                log.info(prefixLog + "第一个分片,清空目标表 LB_T_CCS_ACCT_LMT");
                lbTCcsAcctLmtDao.deleteAll();
            }

            log.info(prefixLog + "开始处理[{}]条信用卡账户数据", ccsAcctDataList.size());

            // 批量查询客户信息用于关联客户编号
            Map<String, String> custNoMap = buildCustNoMapping();
            log.info(prefixLog + "客户编号映射关系构建完成,共[{}]条", custNoMap.size());

            // 数据转换和业务逻辑处理，使用并行流提升性能
            List<LbTCcsAcctLmtDo> processedDataList = ccsAcctDataList.parallelStream().map(
                ccsAcct -> processBusinessLogic(ccsAcct, custNoMap, prefixLog)).filter(Objects::nonNull).collect(
                Collectors.toList());

            if (CollectionUtils.isNotEmpty(processedDataList)) {
                // 批量插入数据
                processBatchInsert(processedDataList, prefixLog);
                log.info(prefixLog + "插入[{}]条信用卡额度信息数据", processedDataList.size());
            }

            // 处理额度实例所属对象信息逻辑
            processLimitObjectInfo(processedDataList, prefixLog);

            // 更新分片流水成功
            normalUpdateSliceSerial(ccsAcctDataList.size(), sliceBatchSerialDo);

        } catch (Exception e) {
            // 异常处理，记录详细日志并抛出运行时异常
            log.error(prefixLog + "分片执行异常:[{}]", batchNum, e);
            throw new RuntimeException(prefixLog + "处理异常", e);
        }

        log.info(prefixLog + "=========分片执行结束:[{}]===========", batchNum);
    }

    /**
     * 构建证件号码到客户编号的映射关系
     *
     * @return 证件号码 -> 客户编号的映射
     */
    private Map<String, String> buildCustNoMapping() {
        // 查询所有个人客户信息
        LbTIndvCustInfoQuery custQuery = LbTIndvCustInfoQuery.builder().build();
        List<LbTIndvCustInfoDo> custInfoList = lbTIndvCustInfoDao.selectByExample(custQuery);
        // 过滤空值，构建映射
        return custInfoList.stream().filter(
            cust -> StringUtil.isNotEmpty(cust.getCertNo()) && StringUtil.isNotEmpty(cust.getCustNo())).collect(
            Collectors.toMap(LbTIndvCustInfoDo::getCertNo, LbTIndvCustInfoDo::getCustNo,
                (existing, replacement) -> existing // 处理重复键,保留第一个
            ));
    }

    /**
     * 处理核心业务逻辑：数据转换、可用余额计算、客户编号关联
     *
     * @param ccsAcct 信用卡账户数据
     * @param custNoMap 证件号码到客户编号的映射
     * @param prefixLog 日志前缀
     * @return 处理后的信用卡额度信息
     */
    private LbTCcsAcctLmtDo processBusinessLogic(LbSCcsAcctDo ccsAcct, Map<String, String> custNoMap,
        String prefixLog) {
        try {
            // 1. 基础字段映射转换
            LbTCcsAcctLmtDo lmtDo = LbTCcsAcctLmtConverter.do2Copy(ccsAcct);
            if (Objects.isNull(lmtDo)) {
                log.warn(prefixLog + "账户[{}]基础转换失败,跳过处理", ccsAcct.getXaccount());
                return null;
            }

            // 2. 客户编号关联逻辑
            String custNo = null;
            if (StringUtil.isNotEmpty(ccsAcct.getCustrNbr())) {
                custNo = custNoMap.get(ccsAcct.getCustrNbr());
                if (StringUtil.isEmpty(custNo)) {
                    log.warn(prefixLog + "账户[{}]证件号码[{}]未找到对应客户编号", ccsAcct.getXaccount(),
                        ccsAcct.getCustrNbr());
                }
            }
            lmtDo.setCustNo(custNo);

            // 3. 可用余额计算逻辑
            BigDecimal usedLmt = calculateUsedLmt(ccsAcct);
            // 账户信用额度
            BigDecimal credLimit = convertToBigDecimal(ccsAcct.getCredLimit());
            // 计算可用余额 = 信用额度 - 已用额度
            BigDecimal availableBalance = credLimit.subtract(usedLmt);
            // 确保可用余额不为负数
            availableBalance = availableBalance.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : availableBalance;
            lmtDo.setUsedLmt(usedLmt);
            lmtDo.setAvlBal(availableBalance);
            return lmtDo;
        } catch (Exception e) {
            // 单条数据异常不中断主流程，记录日志
            log.error(prefixLog + "账户[{}]业务逻辑处理异常", ccsAcct.getXaccount(), e);
            return null;
        }
    }

    /**
     * 计算已用额度
     */
    private BigDecimal calculateUsedLmt(LbSCcsAcctDo ccsAcct) {
        BigDecimal usedLmt = BigDecimal.ZERO;
        usedLmt = addIfNotNull(usedLmt, ccsAcct.getBalCmpint());
        usedLmt = addIfNotNull(usedLmt, ccsAcct.getBalFree());
        usedLmt = addIfNotNull(usedLmt, ccsAcct.getBalInt());
        usedLmt = addIfNotNull(usedLmt, ccsAcct.getBalNoint());
        usedLmt = addIfNotNull(usedLmt, ccsAcct.getBalOrint());
        usedLmt = addIfNotNull(usedLmt, ccsAcct.getMpRemPpl());
        usedLmt = addIfNotNull(usedLmt, ccsAcct.getStmBalfre());
        usedLmt = addIfNotNull(usedLmt, ccsAcct.getStmBalint());
        usedLmt = addIfNotNull(usedLmt, ccsAcct.getStmBalori());
        usedLmt = addIfNotNull(usedLmt, ccsAcct.getStmNoint());
        usedLmt = addIfNotNull(usedLmt, ccsAcct.getBalMp());
        usedLmt = addIfNotNull(usedLmt, ccsAcct.getStmBalmp());
        return usedLmt;
    }

    /**
     * Integer转BigDecimal工具方法
     */
    private BigDecimal convertToBigDecimal(Integer value) {
        return value == null ? BigDecimal.ZERO : new BigDecimal(value);
    }

    /**
     * 安全的BigDecimal加法,处理null值
     */
    private BigDecimal addIfNotNull(BigDecimal sum, BigDecimal value) {
        return value == null ? sum : sum.add(value);
    }

    /**
     * 处理额度实例所属对象信息逻辑
     * 根据客户编号查询额度实例所属对象信息，如果不存在则创建相关记录
     */
    private void processLimitObjectInfo(List<LbTCcsAcctLmtDo> processedDataList, String prefixLog) {
        if (CollectionUtils.isEmpty(processedDataList)) {
            return;
        }

        log.info(prefixLog + "开始处理额度实例所属对象信息逻辑");

        // 按客户编号去重，避免重复处理
        Map<String, LbTCcsAcctLmtDo> custNoToAcctLmtMap = processedDataList.stream().filter(
            acct -> StringUtil.isNotEmpty(acct.getCustNo())).collect(
            Collectors.toMap(LbTCcsAcctLmtDo::getCustNo, acct -> acct, (existing, replacement) -> existing));

        for (Map.Entry<String, LbTCcsAcctLmtDo> entry : custNoToAcctLmtMap.entrySet()) {
            String custNo = entry.getKey();
            LbTCcsAcctLmtDo acctLmtDo = entry.getValue();

            // 根据客户编号查询额度实例所属对象信息是否存在
            LcCustLimitObjectInfoQuery query = LcCustLimitObjectInfoQuery.builder().userId(custNo).build();
            List<LcCustLimitObjectInfoDo> existingList = custLimitObjectInfoDao.selectByExample(query);

            if (CollectionUtils.isEmpty(existingList)) {
                // 不存在则创建所有相关的额度记录
                createAllLimitRecords(acctLmtDo, prefixLog);
            } else {
                log.info(prefixLog + "客户编号[{}]的额度实例所属对象信息已存在,进入补录判断逻辑", custNo);
                // 1. 查询该客户所有额度对象下的额度实例信息
                List<String> custLimitIdList = existingList.stream()
                    .map(LcCustLimitObjectInfoDo::getCustLimitId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
                List<LcCustLimitInfoDo> limitInfoList = custLimitInfoDao.selectByExample(
                    LcCustLimitInfoQuery.builder().limitObjectId(custNo).custLimitIdList(custLimitIdList).build());
                // 2. 判断是否存在GRXFDED节点
                boolean hasGrxfded = limitInfoList.stream().anyMatch(
                    info -> EnumLmtTplNode.GRXFDED.getTemplateNodeId().equals(info.getTemplateNodeId()));
                // 3. 判断是否存在GRJYDED节点
                boolean hasGrjyded = limitInfoList.stream().anyMatch(
                    info -> EnumLmtTplNode.GRJYDED.getTemplateNodeId().equals(info.getTemplateNodeId()));
                // 新增：判断是否存在GRXYKED节点
                boolean hasGrxyked = limitInfoList.stream().anyMatch(
                    info -> EnumLmtTplNode.GRXYKED.getTemplateNodeId().equals(info.getTemplateNodeId()));
                // 新增逻辑：存在GRXFDED且不存在GRXYKED时，补录信用卡额度
                if (hasGrxfded && !hasGrxyked) {
                    log.info(prefixLog + "客户编号[{}]存在消费贷额度节点但不存在信用卡额度节点,进行信用卡额度补录",
                        custNo);
                    // 1. 构造GRXYKED节点的LimitRecord
                    LimitRecord grxykedRecord = createLimitRecord(EnumLmtTplNode.GRXYKED, "信用卡额度", acctLmtDo,
                        "XYKED");
                    List<LimitRecord> grxykedList = Lists.newArrayList(grxykedRecord);

                    // 2. 插入额度实例所属对象信息
                    insertLimitObjectInfos(grxykedList, prefixLog);
                    // 3. 插入额度实例信息
                    insertLimitInfos(grxykedList, prefixLog);
                    // 4. 插入额度实例金额信息
                    insertLimitAmtInfos(grxykedList, acctLmtDo, prefixLog);
                    // 5. 更新GRXFDED、GRZED节点额度金额
                    updateLimitAmtInfoAmount(limitInfoList, acctLmtDo, prefixLog);
                    // 6. 插入额度实例关联（GRXYKED->GRXFDED）
                    insertSingleLimitRelation(grxykedRecord, limitInfoList, custNo, prefixLog);
                    // 7. 插入额度操作流水
                    insertLimitOperateSerials(grxykedList, acctLmtDo, prefixLog);
                    log.info(prefixLog + "客户编号[{}]信用卡额度节点补录完成", custNo);
                }
                // 4. 仅当不存在GRXFDED且存在GRJYDED时，进行补录
                if (!hasGrxfded && hasGrjyded) {
                    log.info(prefixLog + "客户编号[{}]存在经营贷额度节点但不存在消费贷额度节点,进行额度补录", custNo);
                    // 4.1 构造GRXFDED和GRXYKED节点的额度记录
                    List<LimitRecord> limitRecordList = Lists.newArrayList();
                    limitRecordList.add(createLimitRecord(EnumLmtTplNode.GRXFDED, "消费贷额度", acctLmtDo, "XFDED"));
                    limitRecordList.add(createLimitRecord(EnumLmtTplNode.GRXYKED, "信用卡额度", acctLmtDo, "XYKED"));
                    // 4.2 补录额度实例所属对象信息
                    insertLimitObjectInfos(limitRecordList, prefixLog);
                    // 4.3 补录额度实例信息
                    insertLimitInfos(limitRecordList, prefixLog);
                    // 4.4 补录额度实例金额信息（GRXFDED、GRXYKED）
                    insertLimitAmtInfos(limitRecordList, acctLmtDo, prefixLog);
                    // 4.5 更新GRZED节点额度金额（累加cred_limit）
                    // 查询GRZED节点的custLimitId
                    String grzedCustLimitId = limitInfoList.stream()//
                        .filter(info -> EnumLmtTplNode.GRZED.getTemplateNodeId().equals(info.getTemplateNodeId()))//
                        .map(LcCustLimitInfoDo::getCustLimitId).findFirst().orElse(null);
                    if (StringUtil.isNotEmpty(grzedCustLimitId)) {
                        // 查询额度金额表
                        LcCustLimitAmtInfoQuery amtQuery = LcCustLimitAmtInfoQuery.builder().custLimitId(
                            grzedCustLimitId).build();
                        List<LcCustLimitAmtInfoDo> amtInfoList = custLimitAmtInfoDao.selectByExample(amtQuery);
                        if (CollectionUtils.isNotEmpty(amtInfoList)) {
                            LcCustLimitAmtInfoDo grzedAmtInfo = amtInfoList.get(0);
                            // 累加total_amount和real_occupy_amount
                            BigDecimal oldTotal = Optional.ofNullable(grzedAmtInfo.getTotalAmount()).orElse(
                                BigDecimal.ZERO);
                            BigDecimal oldReal = Optional.ofNullable(grzedAmtInfo.getRealOccupyAmount()).orElse(
                                BigDecimal.ZERO);
                            BigDecimal addUsedLmt = Optional.ofNullable(acctLmtDo.getUsedLmt()).orElse(BigDecimal.ZERO);
                            BigDecimal addCredLimit = Optional.ofNullable(acctLmtDo.getCredLimit()).orElse(
                                BigDecimal.ZERO);
                            grzedAmtInfo.setTotalAmount(oldTotal.add(addCredLimit));
                            grzedAmtInfo.setRealOccupyAmount(oldReal.add(addUsedLmt));
                            custLimitAmtInfoDao.updateByPrimaryKeySelective(grzedAmtInfo);
                            log.info(prefixLog
                                    + "客户编号[{}]的GRZED节点额度金额已累加，账户信用额度新增额度:[{}],已用额度新增额度:[{}]",
                                custNo, addCredLimit, addUsedLmt);
                        }
                    }
                    // 4.6 补录额度实例关联（GRXYKED->GRXFDED, GRXFDED->GRZED）
                    insertLimitRelations(limitRecordList, custNo, prefixLog);
                    // 4.7 补录额度操作流水（GRXYKED、GRXFDED各2条）
                    insertLimitOperateSerials(limitRecordList, acctLmtDo, prefixLog);
                    log.info(prefixLog + "客户编号[{}]额度补录完成", custNo);
                } else {
                    log.info(prefixLog + "客户编号[{}]额度节点无需补录,hasGrxfded:[{}], hasGrjyded:[{}]", custNo,
                        hasGrxfded, hasGrjyded);
                }
                // 新增逻辑：存在GRXFDED且存在GRXYKED时，仅更新GRXYKED节点额度金额
                if (hasGrxfded && hasGrxyked) {
                    // 1. 找到GRXYKED节点的custLimitId
                    String grxykedCustLimitId = limitInfoList.stream().filter(
                        info -> EnumLmtTplNode.GRXYKED.getTemplateNodeId().equals(info.getTemplateNodeId())).map(
                        LcCustLimitInfoDo::getCustLimitId).findFirst().orElse(null);
                    if (StringUtil.isNotEmpty(grxykedCustLimitId)) {
                        // 2. 查询额度金额表
                        LcCustLimitAmtInfoQuery amtQuery = LcCustLimitAmtInfoQuery.builder().custLimitId(
                            grxykedCustLimitId).build();
                        List<LcCustLimitAmtInfoDo> amtInfoList = custLimitAmtInfoDao.selectByExample(amtQuery);
                        if (CollectionUtils.isNotEmpty(amtInfoList)) {
                            LcCustLimitAmtInfoDo amtInfo = amtInfoList.get(0);
                            // 3. 累加额度金额
                            BigDecimal oldTotal = Optional.ofNullable(amtInfo.getTotalAmount()).orElse(BigDecimal.ZERO);
                            BigDecimal oldReal = Optional.ofNullable(amtInfo.getRealOccupyAmount()).orElse(
                                BigDecimal.ZERO);
                            BigDecimal addUsedLmt = Optional.ofNullable(acctLmtDo.getUsedLmt()).orElse(BigDecimal.ZERO);
                            BigDecimal addCredLimit = Optional.ofNullable(acctLmtDo.getCredLimit()).orElse(
                                BigDecimal.ZERO);
                            amtInfo.setTotalAmount(oldTotal.add(addUsedLmt));
                            amtInfo.setRealOccupyAmount(oldReal.add(addCredLimit));
                            custLimitAmtInfoDao.updateByPrimaryKeySelective(amtInfo);
                            log.info(prefixLog
                                    + "客户编号[{}]的GRXYKED节点额度金额已累加，新增used_lmt:[{}], cred_limit:[{}]", custNo,
                                addUsedLmt, addCredLimit);
                        } else {
                            log.warn(prefixLog + "客户编号[{}]的GRXYKED节点额度金额信息未找到,无法更新", custNo);
                        }
                    } else {
                        log.warn(prefixLog + "客户编号[{}]的GRXYKED节点custLimitId未找到,无法更新额度金额", custNo);
                    }
                    // 其余表保持不变
                }
            }
        }

        log.info(prefixLog + "额度实例所属对象信息逻辑处理完成");
    }

    /**
     * 创建所有额度相关记录
     */
    private void createAllLimitRecords(LbTCcsAcctLmtDo acctLmtDo, String prefixLog) {
        String custNo = acctLmtDo.getCustNo();

        try {
            // 创建三种类型的额度记录
            List<LimitRecord> limitRecordList = createLimitRecords(acctLmtDo);
            // 批量插入额度实例所属对象信息
            insertLimitObjectInfos(limitRecordList, prefixLog);
            // 批量插入额度实例信息
            insertLimitInfos(limitRecordList, prefixLog);

            // 批量插入额度实例金额信息
            insertLimitAmtInfos(limitRecordList, acctLmtDo, prefixLog);

            // 批量插入额度实例关联信息
            insertLimitRelations(limitRecordList, custNo, prefixLog);

            // 批量插入额度操作流水
            insertLimitOperateSerials(limitRecordList, acctLmtDo, prefixLog);

            log.info(prefixLog + "客户编号[{}]的所有额度记录创建完成", custNo);

        } catch (Exception e) {
            log.error(prefixLog + "创建客户编号[{}]的额度记录异常", custNo, e);
            throw new RuntimeException("创建额度记录异常", e);
        }
    }

    /**
     * 创建额度记录基础信息
     */
    private List<LimitRecord> createLimitRecords(LbTCcsAcctLmtDo acctLmtDo) {
        List<LimitRecord> records = Lists.newArrayList();

        // 创建三种类型的额度记录

        records.add(createLimitRecord(EnumLmtTplNode.GRXYKED, "信用卡额度", acctLmtDo, "XYKED"));
        records.add(createLimitRecord(EnumLmtTplNode.GRXFDED, "消费贷额度", acctLmtDo, "XFDED"));
        records.add(createLimitRecord(EnumLmtTplNode.GRZED, "个人客户总额度", acctLmtDo, "GRZED"));

        return records;
    }

    /**
     * 创建单个额度记录
     */
    private LimitRecord createLimitRecord(EnumLmtTplNode enumLmtTplNode, String limitTypeName,
        LbTCcsAcctLmtDo acctLmtDo, String limitTypeId) {
        LimitRecord record = new LimitRecord();
        record.setTemplateNodeId(enumLmtTplNode.getTemplateNodeId());
        record.setLimitTypeName(limitTypeName);
        record.setCustLimitId(GenerateUtils.custLimitId(enumLmtTplNode));
        record.setLimitTypeId(limitTypeId);
        record.setAcctLmtDo(acctLmtDo);
        return record;
    }

    /**
     * 批量插入额度实例所属对象信息
     */
    private void insertLimitObjectInfos(List<LimitRecord> limitRecordList, String prefixLog) {
        Date now = BusinessDateUtil.getDate();

        List<LcCustLimitObjectInfoDo> objectInfos = limitRecordList.stream().map(record -> {
            LbTCcsAcctLmtDo acctLmtDo = record.getAcctLmtDo();
            LcCustLimitObjectInfoDo objectInfo = new LcCustLimitObjectInfoDo();

            objectInfo.setTenantId(LimitBatchConstants.TENANT_ID);
            objectInfo.setCreateTime(now);
            objectInfo.setUpdateTime(now);
            objectInfo.setCustLimitId(record.getCustLimitId());
            objectInfo.setUserId(acctLmtDo.getCustNo());
            objectInfo.setOutUserId(null);
            objectInfo.setUserType(EnumUserType.INDIVIDUAL_CUSTOMER.getCode());
            objectInfo.setUserName(acctLmtDo.getAccName());
            objectInfo.setUserCertificateKind(EnumCertificateKind.RESIDENT_IDENTITY_CARD.getCode());
            objectInfo.setUserCertificateNo(acctLmtDo.getCustrNbr());
            objectInfo.setIbFinancialProdId(null);
            objectInfo.setIbFinancialProdCoreId(null);
            objectInfo.setIbFinancialProdType(null);
            objectInfo.setIbFinancialProdName(null);
            objectInfo.setIbFinancialType(null);
            objectInfo.setIbFinancialId(null);

            return objectInfo;
        }).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(objectInfos)) {
            custLimitObjectInfoDao.insertList(objectInfos);
            log.info(prefixLog + "批量插入额度实例所属对象信息{}条", objectInfos.size());
        }
    }

    /**
     * 批量插入额度实例信息
     */
    private void insertLimitInfos(List<LimitRecord> limitRecordList, String prefixLog) {
        Date now = BusinessDateUtil.getDate();
        Date endDate = DateUtil.parseDate(LimitBatchConstants.END_DATE_STR, "yyyy-MM-dd");

        List<LcCustLimitInfoDo> limitInfoList = limitRecordList.stream().map(record -> {
            LbTCcsAcctLmtDo acctLmtDo = record.getAcctLmtDo();
            LcCustLimitInfoDo limitInfo = new LcCustLimitInfoDo();

            limitInfo.setTenantId(LimitBatchConstants.TENANT_ID);
            limitInfo.setCreateTime(now);
            limitInfo.setUpdateTime(now);
            limitInfo.setCustLimitId(record.getCustLimitId());
            limitInfo.setLimitStatus(EnumCustLimitStatus.VALID.getCode());
            limitInfo.setOperatorId(getManagerId()); // 管护客户经理ID
            limitInfo.setOwnOrganId(getManagerOrganId()); // 管护客户经理所在机构ID
            limitInfo.setEffectiveStartTime(now);
            limitInfo.setEffectiveEndTime(endDate);
            limitInfo.setLimitTerm(calculateDaysBetween(now, endDate));
            limitInfo.setLimitTermUnit(EnumLimitTermUnit.DAY.getCode()); // 日
            limitInfo.setLimitGraceTerm(null);
            limitInfo.setLimitGraceTermUnit(null);
            limitInfo.setLimitLastTime(null);
            limitInfo.setLimitApprovalDate(now);
            limitInfo.setLimitEnableEndTime(null);
            limitInfo.setLimitEnableTerm(null);
            limitInfo.setLimitEnableTermUnit(null);
            limitInfo.setOutCustLimitId(null);
            // cust_limit_type_id赋值逻辑调整
            String templateNodeId = record.getTemplateNodeId();
            if (EnumLmtTplNode.GRXYKED.getTemplateNodeId().equals(templateNodeId)) {
                limitInfo.setCustLimitTypeId("XYKED");
            } else if (EnumLmtTplNode.GRXFDED.getTemplateNodeId().equals(templateNodeId)
                || EnumLmtTplNode.GRZED.getTemplateNodeId().equals(templateNodeId)) {
                limitInfo.setCustLimitTypeId(templateNodeId);
            } else {
                limitInfo.setCustLimitTypeId(record.getLimitTypeId()); // 兜底
            }
            limitInfo.setTemplateNodeId(record.getTemplateNodeId());
            limitInfo.setLimitTemplateId(EnumLimitTemplateId.GRKHEDTX.getCode());
            limitInfo.setLimitObjectId(acctLmtDo.getCustNo());
            limitInfo.setLimitCoreObjectId(null);
            limitInfo.setLimitObjectType(EnumUserType.INDIVIDUAL_CUSTOMER.getCode());
            limitInfo.setUseOccupyTimes(0);
            limitInfo.setOccupyTimesLimit(null);
            limitInfo.setProductId(null);
            // 额度层级赋值逻辑，根据额度节点类型直接设置
            if (EnumLmtTplNode.GRXYKED.getTemplateNodeId().equals(templateNodeId)) {
                limitInfo.setLimitLevel(EnumLimitLevel.END.getCode()); // 末级节点003
            } else if (EnumLmtTplNode.GRXFDED.getTemplateNodeId().equals(templateNodeId)) {
                limitInfo.setLimitLevel(EnumLimitLevel.MIDDLE.getCode()); // 中间节点002
            } else if (EnumLmtTplNode.GRZED.getTemplateNodeId().equals(templateNodeId)) {
                limitInfo.setLimitLevel(EnumLimitLevel.ROOT.getCode()); // 根节点001
            }
            // 额度分类赋值逻辑，根据额度节点类型直接设置
            if (EnumLmtTplNode.GRXYKED.getTemplateNodeId().equals(templateNodeId)
                || EnumLmtTplNode.GRXFDED.getTemplateNodeId().equals(templateNodeId)) {
                limitInfo.setLimitClassification(EnumLimitClassification.CREDIT_LIMIT.getCode()); // 授信额度 001
            } else if (EnumLmtTplNode.GRZED.getTemplateNodeId().equals(templateNodeId)) {
                limitInfo.setLimitClassification(null); // 个人客户总额度不设置分类
            }
            limitInfo.setLimitUsageType(EnumUsageType.CYCLE_CREDIT.getCode());
            limitInfo.setLimitGrantType(EnumLimitGrantType.COLLECT.getCode());
            // 占用方式赋值逻辑，根据额度节点类型直接设置
            if (EnumLmtTplNode.GRXYKED.getTemplateNodeId().equals(templateNodeId)) {
                limitInfo.setLimitOccupationType(EnumOccupationType.BALANCE.getCode()); // 余额占用 002
            } else if (EnumLmtTplNode.GRXFDED.getTemplateNodeId().equals(templateNodeId)
                || EnumLmtTplNode.GRZED.getTemplateNodeId().equals(templateNodeId)) {
                limitInfo.setLimitOccupationType(null); // 其他节点不设置占用方式
            }
            limitInfo.setUpFlag(EnumBool.NO.getCode());
            limitInfo.setContractLimitFlag(EnumBool.NO.getCode());
            limitInfo.setSoleFlag(EnumBool.YES.getCode());
            limitInfo.setExchangeRateVersion(null);
            limitInfo.setContractRecalFlag(null);
            limitInfo.setSeq(null); // TODO: 需要找到代码逻辑
            // 额度节点为信用卡额度时，设置为超额占用，其余为null
            if (EnumLmtTplNode.GRXYKED.getTemplateNodeId().equals(templateNodeId)) {
                limitInfo.setExcessOccupationType(EnumExcessOccupationType.EXCESS_OCCUPY.getCode());
            } else {
                limitInfo.setExcessOccupationType(null);
            }
            limitInfo.setInstId(GenerateUtils.instId(EnumLmtTplNode.find(record.getTemplateNodeId()))); // 生成唯一实例编号
            limitInfo.setRelationId(null);
            limitInfo.setBizLine(null);
            limitInfo.setSupportSharedFlag(null);
            limitInfo.setSupportShareFlag(null);
            limitInfo.setVirtualContractFlag(EnumBool.NO.getCode());
            limitInfo.setHappenType(null);
            limitInfo.setRegroupTag(null);
            limitInfo.setMainGuarType(null);
            limitInfo.setFtmDwnAvlDt(null);
            limitInfo.setBlngLglpsnCoreInsNo(null);
            limitInfo.setBlngLglpsnCoreInsNm(null);

            return limitInfo;
        }).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(limitInfoList)) {
            custLimitInfoDao.insertList(limitInfoList);
            log.info(prefixLog + "批量插入额度实例信息{}条", limitInfoList.size());
        }
    }

    /**
     * 批量插入额度实例金额信息
     */
    private void insertLimitAmtInfos(List<LimitRecord> limitRecordList, LbTCcsAcctLmtDo acctLmtDo, String prefixLog) {
        Date now = BusinessDateUtil.getDate();

        List<LcCustLimitAmtInfoDo> amtInfoList = limitRecordList.stream().map(record -> {
            LcCustLimitAmtInfoDo amtInfo = new LcCustLimitAmtInfoDo();
            amtInfo.setTenantId(LimitBatchConstants.TENANT_ID);
            amtInfo.setCreateTime(now);
            amtInfo.setUpdateTime(now);
            amtInfo.setCustLimitId(record.getCustLimitId());
            amtInfo.setTotalAmount(acctLmtDo.getCredLimit());
            amtInfo.setPreOccupyAmount(BigDecimal.ZERO);
            amtInfo.setTmpForbidAmount(BigDecimal.ZERO);
            amtInfo.setRealOccupyAmount(acctLmtDo.getUsedLmt());
            amtInfo.setUsedAmount(BigDecimal.ZERO);
            amtInfo.setComprPreOccupyAmount(BigDecimal.ZERO);
            amtInfo.setComprRealOccupyAmount(BigDecimal.ZERO);
            amtInfo.setSubComprPreOccupyAmount(BigDecimal.ZERO);
            amtInfo.setSubComprRealOccupyAmount(BigDecimal.ZERO);
            amtInfo.setSharedPreOccupyAmount(BigDecimal.ZERO);
            amtInfo.setSharedRealOccupyAmount(BigDecimal.ZERO);
            amtInfo.setLowRiskAmount(BigDecimal.ZERO);
            amtInfo.setPreOccupyLowRiskAmt(BigDecimal.ZERO);
            amtInfo.setTmpForbidLowRiskAmt(BigDecimal.ZERO);
            amtInfo.setRealOccupyLowRiskAmt(BigDecimal.ZERO);
            amtInfo.setUsedLowRiskAmt(BigDecimal.ZERO);
            amtInfo.setComprPreOccupyLowRisk(BigDecimal.ZERO);
            amtInfo.setComprRealOccupyLowRisk(BigDecimal.ZERO);
            amtInfo.setSubComprPreOccupyLowRisk(BigDecimal.ZERO);
            amtInfo.setSubRealOccupyLowRisk(BigDecimal.ZERO);
            amtInfo.setSharedPreOccupyLowRiskAmt(BigDecimal.ZERO);
            amtInfo.setSharedRealOccupyLowRisk(BigDecimal.ZERO);
            amtInfo.setCurrency(LimitBatchConstants.CURRENCY_CNY);
            amtInfo.setLowRiskCurrency(LimitBatchConstants.CURRENCY_CNY);
            // amtInfo.setSharedAmountLimit(null);
            // amtInfo.setShareAmountLimit(null);
            amtInfo.setLowRiskAmountId(null);
            amtInfo.setCustNo(acctLmtDo.getCustNo());

            return amtInfo;
        }).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(amtInfoList)) {
            custLimitAmtInfoDao.insertList(amtInfoList);
            log.info(prefixLog + "批量插入额度实例金额信息{}条", amtInfoList.size());
        }
    }

    /**
     * 批量插入额度实例关联信息
     */
    private void insertLimitRelations(List<LimitRecord> limitRecordList, String custNo, String prefixLog) {
        Date now = new Date();

        // 找到对应的额度记录
        LimitRecord grxykedRecord = findRecordByType(limitRecordList, EnumLmtTplNode.GRXYKED);
        LimitRecord grxfdedRecord = findRecordByType(limitRecordList, EnumLmtTplNode.GRXFDED);
        LimitRecord grzedRecord = findRecordByType(limitRecordList, EnumLmtTplNode.GRZED);

        List<LcCustLimitRelationDo> relations = Lists.newArrayList();

        // 1. GRXYKED-信用卡额度 和 GRXFDED-消费贷额度 关系
        if (Objects.nonNull(grxykedRecord) && Objects.nonNull(grxfdedRecord)) {
            LcCustLimitRelationDo relation1 = new LcCustLimitRelationDo();
            relation1.setTenantId(LimitBatchConstants.TENANT_ID);
            relation1.setCreateTime(now);
            relation1.setUpdateTime(now);
            relation1.setLimitRelationId(GenerateUtils.relationId(EnumLmtTplNode.GRXYKED));
            relation1.setCurrentNodeLimitId(grxykedRecord.getCustLimitId());
            relation1.setParentNodeLimitId(grxfdedRecord.getCustLimitId());
            relation1.setLimitRelationType(EnumLimitRelationType.DEFAULT.getCode());
            relation1.setCustNo(custNo);
            relations.add(relation1);
        }

        // 2. GRXFDED-消费贷额度 和 GRZED-个人客户总额度 关系
        if (Objects.nonNull(grxfdedRecord) && Objects.nonNull(grzedRecord)) {
            LcCustLimitRelationDo relation2 = new LcCustLimitRelationDo();
            relation2.setTenantId(LimitBatchConstants.TENANT_ID);
            relation2.setCreateTime(now);
            relation2.setUpdateTime(now);
            relation2.setLimitRelationId(GenerateUtils.relationId(EnumLmtTplNode.GRXFDED));
            relation2.setCurrentNodeLimitId(grxfdedRecord.getCustLimitId());
            relation2.setParentNodeLimitId(grzedRecord.getCustLimitId());
            relation2.setLimitRelationType(EnumLimitRelationType.DEFAULT.getCode());
            relation2.setCustNo(custNo);
            relations.add(relation2);
        }

        if (CollectionUtils.isNotEmpty(relations)) {
            custLimitRelationDao.insertList(relations);
            log.info(prefixLog + "批量插入额度实例关联信息{}条", relations.size());
        }
    }

    /**
     * 批量插入额度操作流水
     */
    private void insertLimitOperateSerials(List<LimitRecord> limitRecordList, LbTCcsAcctLmtDo acctLmtDo,
        String prefixLog) {
        Date now = BusinessDateUtil.getDate();

        List<LcCustLimitOperateSerialDo> operateSerialList = Lists.newArrayList();

        // 为每个额度记录创建两条操作流水：发放额度和生效额度
        for (LimitRecord record : limitRecordList) {
            // 1. 发放额度操作流水
            LcCustLimitOperateSerialDo grantSerial = createOperateSerial(record, acctLmtDo, now,
                EnumCustLimitOperateType.GRANT.getCode(), acctLmtDo.getCredLimit());
            operateSerialList.add(grantSerial);

            // 2. 生效额度操作流水
            LcCustLimitOperateSerialDo effectiveSerial = createOperateSerial(record, acctLmtDo, now,
                EnumCustLimitOperateType.VALID.getCode(), BigDecimal.ZERO);
            operateSerialList.add(effectiveSerial);
        }

        if (CollectionUtils.isNotEmpty(operateSerialList)) {
            custLimitOperateSerialDao.insertList(operateSerialList);
            log.info(prefixLog + "批量插入额度操作流水{}条", operateSerialList.size());
        }
    }

    /**
     * 创建操作流水记录
     */
    private LcCustLimitOperateSerialDo createOperateSerial(LimitRecord record, LbTCcsAcctLmtDo acctLmtDo, Date now,
        String operateType, BigDecimal operateAmount) {
        LcCustLimitOperateSerialDo operateSerial = new LcCustLimitOperateSerialDo();

        operateSerial.setGlobalSerialNo(GenerateUtils.globalSerialNo(EnumLmtTplNode.find(record.templateNodeId)));
        operateSerial.setSerialNo(GenerateUtils.serialNo(EnumLmtTplNode.find(record.templateNodeId)));
        operateSerial.setChannelNo(LimitBatchConstants.CHANNEL_NO_CCS);
        operateSerial.setBizDatetime(now);
        operateSerial.setInboundSerialNo(GenerateUtils.inboundSerialNo(EnumLmtTplNode.find(record.templateNodeId)));
        operateSerial.setInboundSerialDatetime(now);
        operateSerial.setTenantId(LimitBatchConstants.TENANT_ID);
        operateSerial.setCreateTime(now);
        operateSerial.setUpdateTime(now);
        operateSerial.setClosSerialNo(GenerateUtils.closSerialNo(EnumLmtTplNode.find(record.templateNodeId)));
        operateSerial.setStatus(EnumCustLimitOperateStatus.SUCCESS.getCode());
        operateSerial.setOperatorId(getManagerId());
        operateSerial.setOwnOrganId(getManagerOrganId());
        operateSerial.setOperateType(operateType);
        operateSerial.setOperateAmount(operateAmount);
        operateSerial.setOperateAmountId(null);
        operateSerial.setOperateAmountCurrency(LimitBatchConstants.CURRENCY_CNY);
        operateSerial.setOperateLowRiskAmount(BigDecimal.ZERO);
        operateSerial.setOperateLowRiskAmtId(null);
        operateSerial.setOperateLowRiskCurrency(LimitBatchConstants.CURRENCY_CNY);
        operateSerial.setRelationId(null);
        operateSerial.setLastInboundSerialNo(null);
        operateSerial.setCustLimitId(record.getCustLimitId());
        operateSerial.setOperateDirection(null);
        operateSerial.setFailReason(null);
        operateSerial.setRemark(null);
        operateSerial.setEntityId(null);
        operateSerial.setExchangeRateVersion(null);
        operateSerial.setContractRecalFlag(null);
        operateSerial.setOperatePath(null);
        operateSerial.setCustNo(acctLmtDo.getCustNo());

        return operateSerial;
    }

    /**
     * 根据类型查找额度记录
     */
    private LimitRecord findRecordByType(List<LimitRecord> limitRecordList, EnumLmtTplNode enumLmtTplNode) {
        return limitRecordList.stream()//
            .filter(record -> enumLmtTplNode.getTemplateNodeId().equals(record.getTemplateNodeId()))//
            .findFirst().orElse(null);
    }

    /**
     * 获取管护客户经理ID
     * TODO: 需要根据实际业务逻辑实现
     */
    private String getManagerId() {
        return "DEFAULT_MANAGER";
    }

    /**
     * 获取管护客户经理所在机构ID
     * TODO: 需要根据实际业务逻辑实现
     */
    private String getManagerOrganId() {
        return "DEFAULT_ORGAN";
    }

    /**
     * 计算两个日期之间的天数
     */
    private Integer calculateDaysBetween(Date startDate, Date endDate) {
        LocalDate startLocalDate = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endLocalDate = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return Math.toIntExact(ChronoUnit.DAYS.between(startLocalDate, endLocalDate));
    }

    /**
     * 批量插入数据,支持大批量数据的分批处理
     */
    private void processBatchInsert(List<LbTCcsAcctLmtDo> dataList, String prefixLog) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }
        final int batchSize = 1000; // 每批处理1000条
        int totalSize = dataList.size();
        for (int i = 0; i < totalSize; i += batchSize) {
            int endIndex = Math.min(i + batchSize, totalSize);
            List<LbTCcsAcctLmtDo> batchData = dataList.subList(i, endIndex);

            try {
                int insertCount = lbTCcsAcctLmtDao.insertList(batchData);
                log.info(prefixLog + "批量插入第{}批数据完成,预期{}条,实际插入{}条", (i / batchSize + 1),
                    batchData.size(), insertCount);
            } catch (Exception e) {
                log.error(prefixLog + "批量插入第{}批数据异常", (i / batchSize + 1), e);
                throw e;
            }
        }
    }

    /**
     * 额度记录内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    private static class LimitRecord {
        private String templateNodeId;
        private String limitTypeName;
        private String custLimitId;
        private String limitTypeId;
        private LbTCcsAcctLmtDo acctLmtDo;
    }

    /**
     * 新增：补录信用卡额度时，更新GRXFDED和GRZED节点额度金额
     */
    private void updateLimitAmtInfoAmount(List<LcCustLimitInfoDo> limitInfoList, LbTCcsAcctLmtDo acctLmtDo,
        String prefixLog) {
        // 查找GRXFDED和GRZED节点的custLimitId
        String grxfdedCustLimitId = limitInfoList.stream().filter(
            info -> EnumLmtTplNode.GRXFDED.getTemplateNodeId().equals(info.getTemplateNodeId())).map(
            LcCustLimitInfoDo::getCustLimitId).findFirst().orElse(null);
        String grzedCustLimitId = limitInfoList.stream().filter(
            info -> EnumLmtTplNode.GRZED.getTemplateNodeId().equals(info.getTemplateNodeId())).map(
            LcCustLimitInfoDo::getCustLimitId).findFirst().orElse(null);
        BigDecimal addValue = Optional.ofNullable(acctLmtDo.getCredLimit()).orElse(BigDecimal.ZERO);
        // 更新GRXFDED
        if (grxfdedCustLimitId != null) {
            LcCustLimitAmtInfoQuery amtQuery = LcCustLimitAmtInfoQuery.builder()
                .custLimitId(grxfdedCustLimitId)
                .build();
            List<LcCustLimitAmtInfoDo> amtInfoList = custLimitAmtInfoDao.selectByExample(amtQuery);
            if (CollectionUtils.isNotEmpty(amtInfoList)) {
                LcCustLimitAmtInfoDo amtInfo = amtInfoList.get(0);
                BigDecimal oldTotal = Optional.ofNullable(amtInfo.getTotalAmount()).orElse(BigDecimal.ZERO);
                BigDecimal oldReal = Optional.ofNullable(amtInfo.getRealOccupyAmount()).orElse(BigDecimal.ZERO);
                amtInfo.setTotalAmount(oldTotal.add(addValue));
                amtInfo.setRealOccupyAmount(oldReal.add(addValue));
                custLimitAmtInfoDao.updateByPrimaryKeySelective(amtInfo);
                log.info(prefixLog + "客户编号[{}]的GRXFDED节点额度金额已累加,新增额度:[{}]", acctLmtDo.getCustNo(),
                    addValue);
            }
        }
        // 更新GRZED
        if (grzedCustLimitId != null) {
            LcCustLimitAmtInfoQuery amtQuery = LcCustLimitAmtInfoQuery.builder().custLimitId(grzedCustLimitId).build();
            List<LcCustLimitAmtInfoDo> amtInfoList = custLimitAmtInfoDao.selectByExample(amtQuery);
            if (CollectionUtils.isNotEmpty(amtInfoList)) {
                LcCustLimitAmtInfoDo amtInfo = amtInfoList.get(0);
                BigDecimal oldTotal = Optional.ofNullable(amtInfo.getTotalAmount()).orElse(BigDecimal.ZERO);
                BigDecimal oldReal = Optional.ofNullable(amtInfo.getRealOccupyAmount()).orElse(BigDecimal.ZERO);
                amtInfo.setTotalAmount(oldTotal.add(addValue));
                amtInfo.setRealOccupyAmount(oldReal.add(addValue));
                custLimitAmtInfoDao.updateByPrimaryKeySelective(amtInfo);
                log.info(prefixLog + "客户编号[{}]的GRZED节点额度金额已累加,新增额度:[{}]", acctLmtDo.getCustNo(),
                    addValue);
            }
        }
    }

    /**
     * 新增：只插入GRXYKED与GRXFDED的额度实例关联
     */
    private void insertSingleLimitRelation(LimitRecord grxykedRecord, List<LcCustLimitInfoDo> limitInfoList,
        String custNo, String prefixLog) {
        // 查找GRXFDED节点的custLimitId
        String grxfdedCustLimitId = limitInfoList.stream().filter(
            info -> EnumLmtTplNode.GRXFDED.getTemplateNodeId().equals(info.getTemplateNodeId())).map(
            LcCustLimitInfoDo::getCustLimitId).findFirst().orElse(null);
        if (Objects.nonNull(grxykedRecord) && grxfdedCustLimitId != null) {
            Date now = BusinessDateUtil.getDate();
            LcCustLimitRelationDo relation = new LcCustLimitRelationDo();
            relation.setTenantId(LimitBatchConstants.TENANT_ID);
            relation.setCreateTime(now);
            relation.setUpdateTime(now);
            relation.setLimitRelationId(GenerateUtils.relationId(EnumLmtTplNode.GRXYKED));
            relation.setCurrentNodeLimitId(grxykedRecord.getCustLimitId());
            relation.setParentNodeLimitId(grxfdedCustLimitId);
            relation.setLimitRelationType(EnumLimitRelationType.DEFAULT.getCode());
            relation.setCustNo(custNo);
            custLimitRelationDao.insert(relation);
            log.info(prefixLog + "插入额度实例关联信息:信用卡额度[{}] -> 消费贷额度[{}]",
                grxykedRecord.getCustLimitId(), grxfdedCustLimitId);
        }
    }
}