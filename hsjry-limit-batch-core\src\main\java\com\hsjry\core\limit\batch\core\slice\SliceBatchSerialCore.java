/*
 * yunrong.cn Inc. Copyright (c) 2014-2019 All Rights Reserved
 */

package com.hsjry.core.limit.batch.core.slice;

import com.hsjry.base.common.job.dto.StatisticsData;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.lang.common.stereotype.enums.EnumBool;

/**
 * 分片流水记录
 *
 * <AUTHOR>
 * @version V3.0
 * @since 3.0.1 2020/10/17 15:59
 */
public interface SliceBatchSerialCore {

    /**
     * 更新或插入 分片流水
     *
     * @param lcSliceBatchSerialDo 分片流水信息
     */
    void updateSuccessSliceBatchSerial(LcSliceBatchSerialDo lcSliceBatchSerialDo);

    /**
     * 更新或插入 分片流水
     *
     * @param lcSliceBatchSerialDo 分片流水信息
     */
    void updateFailSliceBatchSerial(LcSliceBatchSerialDo lcSliceBatchSerialDo);

    /**
     * 按批次 分页统计 汇总统计
     * (分页统计 单个任务 只调用一次)
     *
     * @param batchSerialNo 批次总流水
     * @param batchPageSize 批处理 分页查询数据库时 分页大小
     * @return 汇总统计项
     */
    StatisticsData statisticsDataByPage(String batchSerialNo, Integer batchPageSize);

    /**
     * 更新流水的结束标记
     *
     * @param lcSliceBatchSerialDo
     * @param finishFlag
     */
    void updateSliceSerialFinishFlag(LcSliceBatchSerialDo lcSliceBatchSerialDo, EnumBool finishFlag);
}
