/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2015 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.common.utils;

import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.common.enums.EnumSerialModalName;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.DateUtil;
import com.hsjry.lang.sequence.SequenceTool;

import lombok.extern.slf4j.Slf4j;

/**
 * 主键生成工具
 *
 * <AUTHOR>
 * @version $Id: SequenceUtil.java, v 0.1 Oct 22, 2015 2:24:14 PM sunjm13534 Exp $
 */
@Slf4j
public class BusinessSequenceUtil {

    /**
     * 获取主键
     *
     * @param modal
     * @return
     */
    public static String getTypeSerialNo(EnumSerialModalName modal) {
        long start = System.currentTimeMillis();
        String yyyyMMdd = DateUtil.getDate(BusinessDateUtil.getDate(), DateUtil.DATE_FORMAT_2);
        String yyyyMM = DateUtil.getDate(BusinessDateUtil.getDate(), DateUtil.DATE_FORMAT_4);
        String serialNo = AppParamUtil.getTenantId() + modal.getCode() + yyyyMMdd + SequenceTool.nextId(
            AppParamUtil.getTenantId() + ":" + modal.getType() + ":" + yyyyMM);
        log.info("serialNo获取消耗时间:{}", System.currentTimeMillis() - start);
        return serialNo;
    }
}
