package com.hsjry.core.limit.batch.biz.job.sharding.biz.limit;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.limit.EnumEntityOperateStatus;
import com.hsjry.base.common.model.enums.limit.EnumEntityOperateType;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.center.dal.dao.intf.EntityOperateSerialBatchDao;
import com.hsjry.core.limit.center.dal.dao.model.LcEntityOperateSerialDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.EntityOperateSerialBatchQuery;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * 实体预发放失效
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0 2023/3/22 14:21
 */
@Service
@Slf4j
public class InvalidPreGrantEntityImpl extends AbstractShardingPrepareBiz<EntityOperateSerialBatchQuery>
    implements JobCoreBusiness<LcEntityOperateSerialDo> {
    @Autowired
    private EntityOperateSerialBatchDao entityOperateSerialBatchDao;

    @Override
    public Integer selectCountByCurrentGroupFromDb(EntityOperateSerialBatchQuery query) {
        return entityOperateSerialBatchDao.selectCountByCurrentGroup(query);
    }

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.ENTITY_PRE_GRANT_INVALID;
    }

    @Override
    public ShardingResult<LcEntityOperateSerialDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        ShardingResult<LcEntityOperateSerialDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }
        JSONObject inParam = JSON.parseObject(jobInitDto.getInPara());
        List<String> inboundSerialNoList = JSONArray.parseArray(inParam.getString("inboundSerialNoList"), String.class);
        //原始查询条件
        EntityOperateSerialBatchQuery operateSerialBatchQuery = GsonUtil.json2Obj(jobShared.getExtParam(),
            EntityOperateSerialBatchQuery.class);
        EntityOperateSerialBatchQuery query = EntityOperateSerialBatchQuery.builder()
            .offset(jobShared.getOffset())
            .limit(jobShared.getLimit())
            .operateType(operateSerialBatchQuery.getOperateType())
            .inboundSerialNoList(inboundSerialNoList)
            .preGrantExpiryDateStr(operateSerialBatchQuery.getPreGrantExpiryDateStr())
            .operateStatus(operateSerialBatchQuery.getOperateStatus())
            .build();
        log.info("=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");
        List<LcEntityOperateSerialDo> list = entityOperateSerialBatchDao.selectShardList(query);
        shardingResult.setShardingResultList(list);
        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LcEntityOperateSerialDo> shardingResult) {
        List<LcEntityOperateSerialDo> entityOperateSerialDoList = shardingResult.getShardingResultList();
        if (CollectionUtils.isEmpty(entityOperateSerialDoList)) {
            log.info("=========分片执行结束:" + shardingResult.getJobShared().getBatchNum() + "数量为空=========");
            return;
        }

        //更新分片流水成功
        normalUpdateSliceSerial(entityOperateSerialDoList.size(), shardingResult.getLcSliceBatchSerialDo());
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        log.info("====================== 接入业务{}分片逻辑 start ================================================",
            getJobTrade().getDescription());
        List<JobShared> jobSharedList = new ArrayList<>();
        //sql 批处理数量 暂定为分片数量，不放大
        Integer batchFixNum = jobInitDto.getFixNum();
        //当前分组的最大值，为下次 批处理的最小值
        LcEntityOperateSerialDo maxLimitInfoDo = new LcEntityOperateSerialDo();
        JSONObject inParam = JSON.parseObject(jobInitDto.getInPara());
        List<String> inboundSerialNoList = JSONArray.parseArray(inParam.getString("inboundSerialNoList"), String.class);
        //构造查询条件 查询当前 分批处理的 排序 最大 对象
        EntityOperateSerialBatchQuery query = EntityOperateSerialBatchQuery.builder()
            .inboundSerialNoList(inboundSerialNoList)
            .operateType(EnumEntityOperateType.PRE_GRANT.getCode())
            .preGrantExpiryDateStr(String.valueOf(jobInitDto.getBusinessDate()))
            .operateStatus(EnumEntityOperateStatus.SUCCESS.getCode())
            .offset(batchFixNum - 1)
            .limit(1)
            .build();
        //分片流水
        int batchNum = 0;
        while (maxLimitInfoDo != null) {
            query.setEntityId(maxLimitInfoDo.getEntityId());
            maxLimitInfoDo = entityOperateSerialBatchDao.selectFirstOne(query);
            //统计分片 数量
            batchNum = countBatchNum(batchFixNum, query, maxLimitInfoDo, batchNum, jobInitDto, jobSharedList,
                query.getEntityId(), false);
        }
        log.info("====================== 接入业务{}分片逻辑 end ================================================",
            getJobTrade().getDescription());
        return jobSharedList;
    }

}
