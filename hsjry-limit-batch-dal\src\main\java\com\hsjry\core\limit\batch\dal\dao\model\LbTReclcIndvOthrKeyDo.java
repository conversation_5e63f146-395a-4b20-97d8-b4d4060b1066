package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度中心-中间表-额度重算中个人额度中其他额度主键
 *
 * <AUTHOR>
 * @date 2025-08-21 13:10:51
 */
@Table(name = "lb_t_reclc_indv_othr")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LbTReclcIndvOthrKeyDo implements Serializable {

    private static final long serialVersionUID = 1958517154135605249L;
    /** 客户编号 */
    @Id
    @Column(name = "cust_no")
    private String custNo;
    /** 额度编号 */
    @Id
    @Column(name = "cust_limit_id")
    private String custLimitId;
}