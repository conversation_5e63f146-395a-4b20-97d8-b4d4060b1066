package com.hsjry.core.limit.batch.dal.dao.model;

import java.util.ArrayList;
import java.util.List;

/**
 * 额度中心-中间表-额度重算中对公客户中合作方额度Example
 *
 * <AUTHOR>
 * @date 2025-08-21 11:31:28
 */
public class LbTReclcCorpCoPrtnExample {
    /** 排序字段 */
    protected String orderByClause;

    /** 是否只查询记录不同的数据 */
    protected boolean distinct;

    /** or条件查询集合 */
    protected List<Criteria> oredCriteria;

    /**
     * 初始化or条件查询集合
     */
    public LbTReclcCorpCoPrtnExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * 排序字段set方法
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * 排序字段get方法
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * 不同记录set方法.
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * 不同记录get方法.
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * 排序集合get方法
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * or
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * or
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * 初始化标准规范
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * 初始化标准规范
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * 清除数据
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andCustNoIsNull() {
            addCriterion("cust_no is null");
            return (Criteria) this;
        }

        public Criteria andCustNoIsNotNull() {
            addCriterion("cust_no is not null");
            return (Criteria) this;
        }

        public Criteria andCustNoEqualTo(String value) {
            addCriterion("cust_no =", value, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoNotEqualTo(String value) {
            addCriterion("cust_no <>", value, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoGreaterThan(String value) {
            addCriterion("cust_no >", value, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoGreaterThanOrEqualTo(String value) {
            addCriterion("cust_no >=", value, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoLessThan(String value) {
            addCriterion("cust_no <", value, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoLessThanOrEqualTo(String value) {
            addCriterion("cust_no <=", value, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoLike(String value) {
            addCriterion("cust_no like", value, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoNotLike(String value) {
            addCriterion("cust_no not like", value, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoIn(List<String> values) {
            addCriterion("cust_no in", values, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoNotIn(List<String> values) {
            addCriterion("cust_no not in", values, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoBetween(String value1, String value2) {
            addCriterion("cust_no between", value1, value2, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustNoNotBetween(String value1, String value2) {
            addCriterion("cust_no not between", value1, value2, "custNo");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdIsNull() {
            addCriterion("cust_limit_id is null");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdIsNotNull() {
            addCriterion("cust_limit_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdEqualTo(String value) {
            addCriterion("cust_limit_id =", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdNotEqualTo(String value) {
            addCriterion("cust_limit_id <>", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdGreaterThan(String value) {
            addCriterion("cust_limit_id >", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdGreaterThanOrEqualTo(String value) {
            addCriterion("cust_limit_id >=", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdLessThan(String value) {
            addCriterion("cust_limit_id <", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdLessThanOrEqualTo(String value) {
            addCriterion("cust_limit_id <=", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdLike(String value) {
            addCriterion("cust_limit_id like", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdNotLike(String value) {
            addCriterion("cust_limit_id not like", value, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdIn(List<String> values) {
            addCriterion("cust_limit_id in", values, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdNotIn(List<String> values) {
            addCriterion("cust_limit_id not in", values, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdBetween(String value1, String value2) {
            addCriterion("cust_limit_id between", value1, value2, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andCustLimitIdNotBetween(String value1, String value2) {
            addCriterion("cust_limit_id not between", value1, value2, "custLimitId");
            return (Criteria) this;
        }

        public Criteria andTemplateNodeIdIsNull() {
            addCriterion("template_node_id is null");
            return (Criteria) this;
        }

        public Criteria andTemplateNodeIdIsNotNull() {
            addCriterion("template_node_id is not null");
            return (Criteria) this;
        }

        public Criteria andTemplateNodeIdEqualTo(String value) {
            addCriterion("template_node_id =", value, "templateNodeId");
            return (Criteria) this;
        }

        public Criteria andTemplateNodeIdNotEqualTo(String value) {
            addCriterion("template_node_id <>", value, "templateNodeId");
            return (Criteria) this;
        }

        public Criteria andTemplateNodeIdGreaterThan(String value) {
            addCriterion("template_node_id >", value, "templateNodeId");
            return (Criteria) this;
        }

        public Criteria andTemplateNodeIdGreaterThanOrEqualTo(String value) {
            addCriterion("template_node_id >=", value, "templateNodeId");
            return (Criteria) this;
        }

        public Criteria andTemplateNodeIdLessThan(String value) {
            addCriterion("template_node_id <", value, "templateNodeId");
            return (Criteria) this;
        }

        public Criteria andTemplateNodeIdLessThanOrEqualTo(String value) {
            addCriterion("template_node_id <=", value, "templateNodeId");
            return (Criteria) this;
        }

        public Criteria andTemplateNodeIdLike(String value) {
            addCriterion("template_node_id like", value, "templateNodeId");
            return (Criteria) this;
        }

        public Criteria andTemplateNodeIdNotLike(String value) {
            addCriterion("template_node_id not like", value, "templateNodeId");
            return (Criteria) this;
        }

        public Criteria andTemplateNodeIdIn(List<String> values) {
            addCriterion("template_node_id in", values, "templateNodeId");
            return (Criteria) this;
        }

        public Criteria andTemplateNodeIdNotIn(List<String> values) {
            addCriterion("template_node_id not in", values, "templateNodeId");
            return (Criteria) this;
        }

        public Criteria andTemplateNodeIdBetween(String value1, String value2) {
            addCriterion("template_node_id between", value1, value2, "templateNodeId");
            return (Criteria) this;
        }

        public Criteria andTemplateNodeIdNotBetween(String value1, String value2) {
            addCriterion("template_node_id not between", value1, value2, "templateNodeId");
            return (Criteria) this;
        }

        public Criteria andLimitStatusIsNull() {
            addCriterion("limit_status is null");
            return (Criteria) this;
        }

        public Criteria andLimitStatusIsNotNull() {
            addCriterion("limit_status is not null");
            return (Criteria) this;
        }

        public Criteria andLimitStatusEqualTo(String value) {
            addCriterion("limit_status =", value, "limitStatus");
            return (Criteria) this;
        }

        public Criteria andLimitStatusNotEqualTo(String value) {
            addCriterion("limit_status <>", value, "limitStatus");
            return (Criteria) this;
        }

        public Criteria andLimitStatusGreaterThan(String value) {
            addCriterion("limit_status >", value, "limitStatus");
            return (Criteria) this;
        }

        public Criteria andLimitStatusGreaterThanOrEqualTo(String value) {
            addCriterion("limit_status >=", value, "limitStatus");
            return (Criteria) this;
        }

        public Criteria andLimitStatusLessThan(String value) {
            addCriterion("limit_status <", value, "limitStatus");
            return (Criteria) this;
        }

        public Criteria andLimitStatusLessThanOrEqualTo(String value) {
            addCriterion("limit_status <=", value, "limitStatus");
            return (Criteria) this;
        }

        public Criteria andLimitStatusLike(String value) {
            addCriterion("limit_status like", value, "limitStatus");
            return (Criteria) this;
        }

        public Criteria andLimitStatusNotLike(String value) {
            addCriterion("limit_status not like", value, "limitStatus");
            return (Criteria) this;
        }

        public Criteria andLimitStatusIn(List<String> values) {
            addCriterion("limit_status in", values, "limitStatus");
            return (Criteria) this;
        }

        public Criteria andLimitStatusNotIn(List<String> values) {
            addCriterion("limit_status not in", values, "limitStatus");
            return (Criteria) this;
        }

        public Criteria andLimitStatusBetween(String value1, String value2) {
            addCriterion("limit_status between", value1, value2, "limitStatus");
            return (Criteria) this;
        }

        public Criteria andLimitStatusNotBetween(String value1, String value2) {
            addCriterion("limit_status not between", value1, value2, "limitStatus");
            return (Criteria) this;
        }

        public Criteria andTotalAmountIsNull() {
            addCriterion("total_amount is null");
            return (Criteria) this;
        }

        public Criteria andTotalAmountIsNotNull() {
            addCriterion("total_amount is not null");
            return (Criteria) this;
        }

        public Criteria andTotalAmountEqualTo(java.math.BigDecimal value) {
            addCriterion("total_amount =", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountNotEqualTo(java.math.BigDecimal value) {
            addCriterion("total_amount <>", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountGreaterThan(java.math.BigDecimal value) {
            addCriterion("total_amount >", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("total_amount >=", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountLessThan(java.math.BigDecimal value) {
            addCriterion("total_amount <", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("total_amount <=", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountLike(java.math.BigDecimal value) {
            addCriterion("total_amount like", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountNotLike(java.math.BigDecimal value) {
            addCriterion("total_amount not like", value, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountIn(List<java.math.BigDecimal> values) {
            addCriterion("total_amount in", values, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountNotIn(List<java.math.BigDecimal> values) {
            addCriterion("total_amount not in", values, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("total_amount between", value1, value2, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andTotalAmountNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("total_amount not between", value1, value2, "totalAmount");
            return (Criteria) this;
        }

        public Criteria andPreOccupyAmountIsNull() {
            addCriterion("pre_occupy_amount is null");
            return (Criteria) this;
        }

        public Criteria andPreOccupyAmountIsNotNull() {
            addCriterion("pre_occupy_amount is not null");
            return (Criteria) this;
        }

        public Criteria andPreOccupyAmountEqualTo(java.math.BigDecimal value) {
            addCriterion("pre_occupy_amount =", value, "preOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andPreOccupyAmountNotEqualTo(java.math.BigDecimal value) {
            addCriterion("pre_occupy_amount <>", value, "preOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andPreOccupyAmountGreaterThan(java.math.BigDecimal value) {
            addCriterion("pre_occupy_amount >", value, "preOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andPreOccupyAmountGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("pre_occupy_amount >=", value, "preOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andPreOccupyAmountLessThan(java.math.BigDecimal value) {
            addCriterion("pre_occupy_amount <", value, "preOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andPreOccupyAmountLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("pre_occupy_amount <=", value, "preOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andPreOccupyAmountLike(java.math.BigDecimal value) {
            addCriterion("pre_occupy_amount like", value, "preOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andPreOccupyAmountNotLike(java.math.BigDecimal value) {
            addCriterion("pre_occupy_amount not like", value, "preOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andPreOccupyAmountIn(List<java.math.BigDecimal> values) {
            addCriterion("pre_occupy_amount in", values, "preOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andPreOccupyAmountNotIn(List<java.math.BigDecimal> values) {
            addCriterion("pre_occupy_amount not in", values, "preOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andPreOccupyAmountBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("pre_occupy_amount between", value1, value2, "preOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andPreOccupyAmountNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("pre_occupy_amount not between", value1, value2, "preOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountIsNull() {
            addCriterion("real_occupy_amount is null");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountIsNotNull() {
            addCriterion("real_occupy_amount is not null");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountEqualTo(java.math.BigDecimal value) {
            addCriterion("real_occupy_amount =", value, "realOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountNotEqualTo(java.math.BigDecimal value) {
            addCriterion("real_occupy_amount <>", value, "realOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountGreaterThan(java.math.BigDecimal value) {
            addCriterion("real_occupy_amount >", value, "realOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("real_occupy_amount >=", value, "realOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountLessThan(java.math.BigDecimal value) {
            addCriterion("real_occupy_amount <", value, "realOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("real_occupy_amount <=", value, "realOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountLike(java.math.BigDecimal value) {
            addCriterion("real_occupy_amount like", value, "realOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountNotLike(java.math.BigDecimal value) {
            addCriterion("real_occupy_amount not like", value, "realOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountIn(List<java.math.BigDecimal> values) {
            addCriterion("real_occupy_amount in", values, "realOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountNotIn(List<java.math.BigDecimal> values) {
            addCriterion("real_occupy_amount not in", values, "realOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("real_occupy_amount between", value1, value2, "realOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andRealOccupyAmountNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("real_occupy_amount not between", value1, value2, "realOccupyAmount");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountIsNull() {
            addCriterion("low_risk_amount is null");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountIsNotNull() {
            addCriterion("low_risk_amount is not null");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountEqualTo(java.math.BigDecimal value) {
            addCriterion("low_risk_amount =", value, "lowRiskAmount");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountNotEqualTo(java.math.BigDecimal value) {
            addCriterion("low_risk_amount <>", value, "lowRiskAmount");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountGreaterThan(java.math.BigDecimal value) {
            addCriterion("low_risk_amount >", value, "lowRiskAmount");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("low_risk_amount >=", value, "lowRiskAmount");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountLessThan(java.math.BigDecimal value) {
            addCriterion("low_risk_amount <", value, "lowRiskAmount");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("low_risk_amount <=", value, "lowRiskAmount");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountLike(java.math.BigDecimal value) {
            addCriterion("low_risk_amount like", value, "lowRiskAmount");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountNotLike(java.math.BigDecimal value) {
            addCriterion("low_risk_amount not like", value, "lowRiskAmount");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountIn(List<java.math.BigDecimal> values) {
            addCriterion("low_risk_amount in", values, "lowRiskAmount");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountNotIn(List<java.math.BigDecimal> values) {
            addCriterion("low_risk_amount not in", values, "lowRiskAmount");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("low_risk_amount between", value1, value2, "lowRiskAmount");
            return (Criteria) this;
        }

        public Criteria andLowRiskAmountNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("low_risk_amount not between", value1, value2, "lowRiskAmount");
            return (Criteria) this;
        }

        public Criteria andPreOccupyLowRiskAmtIsNull() {
            addCriterion("pre_occupy_low_risk_amt is null");
            return (Criteria) this;
        }

        public Criteria andPreOccupyLowRiskAmtIsNotNull() {
            addCriterion("pre_occupy_low_risk_amt is not null");
            return (Criteria) this;
        }

        public Criteria andPreOccupyLowRiskAmtEqualTo(java.math.BigDecimal value) {
            addCriterion("pre_occupy_low_risk_amt =", value, "preOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andPreOccupyLowRiskAmtNotEqualTo(java.math.BigDecimal value) {
            addCriterion("pre_occupy_low_risk_amt <>", value, "preOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andPreOccupyLowRiskAmtGreaterThan(java.math.BigDecimal value) {
            addCriterion("pre_occupy_low_risk_amt >", value, "preOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andPreOccupyLowRiskAmtGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("pre_occupy_low_risk_amt >=", value, "preOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andPreOccupyLowRiskAmtLessThan(java.math.BigDecimal value) {
            addCriterion("pre_occupy_low_risk_amt <", value, "preOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andPreOccupyLowRiskAmtLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("pre_occupy_low_risk_amt <=", value, "preOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andPreOccupyLowRiskAmtLike(java.math.BigDecimal value) {
            addCriterion("pre_occupy_low_risk_amt like", value, "preOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andPreOccupyLowRiskAmtNotLike(java.math.BigDecimal value) {
            addCriterion("pre_occupy_low_risk_amt not like", value, "preOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andPreOccupyLowRiskAmtIn(List<java.math.BigDecimal> values) {
            addCriterion("pre_occupy_low_risk_amt in", values, "preOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andPreOccupyLowRiskAmtNotIn(List<java.math.BigDecimal> values) {
            addCriterion("pre_occupy_low_risk_amt not in", values, "preOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andPreOccupyLowRiskAmtBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("pre_occupy_low_risk_amt between", value1, value2, "preOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andPreOccupyLowRiskAmtNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("pre_occupy_low_risk_amt not between", value1, value2, "preOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtIsNull() {
            addCriterion("real_occupy_low_risk_amt is null");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtIsNotNull() {
            addCriterion("real_occupy_low_risk_amt is not null");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtEqualTo(java.math.BigDecimal value) {
            addCriterion("real_occupy_low_risk_amt =", value, "realOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtNotEqualTo(java.math.BigDecimal value) {
            addCriterion("real_occupy_low_risk_amt <>", value, "realOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtGreaterThan(java.math.BigDecimal value) {
            addCriterion("real_occupy_low_risk_amt >", value, "realOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtGreaterThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("real_occupy_low_risk_amt >=", value, "realOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtLessThan(java.math.BigDecimal value) {
            addCriterion("real_occupy_low_risk_amt <", value, "realOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtLessThanOrEqualTo(java.math.BigDecimal value) {
            addCriterion("real_occupy_low_risk_amt <=", value, "realOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtLike(java.math.BigDecimal value) {
            addCriterion("real_occupy_low_risk_amt like", value, "realOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtNotLike(java.math.BigDecimal value) {
            addCriterion("real_occupy_low_risk_amt not like", value, "realOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtIn(List<java.math.BigDecimal> values) {
            addCriterion("real_occupy_low_risk_amt in", values, "realOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtNotIn(List<java.math.BigDecimal> values) {
            addCriterion("real_occupy_low_risk_amt not in", values, "realOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("real_occupy_low_risk_amt between", value1, value2, "realOccupyLowRiskAmt");
            return (Criteria) this;
        }

        public Criteria andRealOccupyLowRiskAmtNotBetween(java.math.BigDecimal value1, java.math.BigDecimal value2) {
            addCriterion("real_occupy_low_risk_amt not between", value1, value2, "realOccupyLowRiskAmt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}