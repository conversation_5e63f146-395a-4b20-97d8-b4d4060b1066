/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.job.file;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * 网贷历史借据信息文件的同步处理任务
 * 负责H_OL_LOAN_INFO文件的批量同步处理
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/21 16:30
 */
@Slf4j
@Service("lbHOlLoanInfoFileSyncJob")
public class LbHOlLoanInfoFileSyncJob extends AbstractBaseBatchJob {

    public LbHOlLoanInfoFileSyncJob() {
        log.info("LbHOlLoanInfoFileSyncJob Bean初始化完成");
    }

    @Autowired
    @Qualifier("lbHOlLoanInfoFileSyncBizImpl")
    private BaseOrdinaryBiz lbHOlLoanInfoFileSyncBizImpl;

    @Override
    public BaseOrdinaryBiz getBaseOrdinaryBiz() {
        return lbHOlLoanInfoFileSyncBizImpl;
    }
}
