package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpCoPrtnLvlDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcCorpCoPrtnLvlQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中对公客户中合作方层额度数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-08-21 11:31:28
 */
public interface LbTReclcCorpCoPrtnLvlDao extends IBaseDao<LbTReclcCorpCoPrtnLvlDo> {
    /**
     * 分页查询额度中心-中间表-额度重算中对公客户中合作方层额度
     *
     * @param lbTReclcCorpCoPrtnLvlQuery 条件
     * @return PageInfo<LbTReclcCorpCoPrtnLvlDo>
     */
    PageInfo<LbTReclcCorpCoPrtnLvlDo> selectPage(LbTReclcCorpCoPrtnLvlQuery lbTReclcCorpCoPrtnLvlQuery,
        PageParam pageParam);

    /**
     * 根据key查询额度中心-中间表-额度重算中对公客户中合作方层额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    LbTReclcCorpCoPrtnLvlDo selectByKey(String custNo, String custLimitId);

    /**
     * 根据key删除额度中心-中间表-额度重算中对公客户中合作方层额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custNo, String custLimitId);

    /**
     * 查询额度中心-中间表-额度重算中对公客户中合作方层额度信息
     *
     * @param lbTReclcCorpCoPrtnLvlQuery 条件
     * @return List<LbTReclcCorpCoPrtnLvlDo>
     */
    List<LbTReclcCorpCoPrtnLvlDo> selectByExample(LbTReclcCorpCoPrtnLvlQuery lbTReclcCorpCoPrtnLvlQuery);

    /**
     * 新增额度中心-中间表-额度重算中对公客户中合作方层额度信息
     *
     * @param lbTReclcCorpCoPrtnLvl 条件
     * @return int>
     */
    int insertBySelective(LbTReclcCorpCoPrtnLvlDo lbTReclcCorpCoPrtnLvl);

    /**
     * 修改额度中心-中间表-额度重算中对公客户中合作方层额度信息
     *
     * @param lbTReclcCorpCoPrtnLvl
     * @return
     */
    int updateBySelective(LbTReclcCorpCoPrtnLvlDo lbTReclcCorpCoPrtnLvl);

    /**
     * 修改额度中心-中间表-额度重算中对公客户中合作方层额度信息
     *
     * @param lbTReclcCorpCoPrtnLvl
     * @param lbTReclcCorpCoPrtnLvlQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTReclcCorpCoPrtnLvlDo lbTReclcCorpCoPrtnLvl,
        LbTReclcCorpCoPrtnLvlQuery lbTReclcCorpCoPrtnLvlQuery);

    // ==================== 合作方层额度重算相关方法 ====================

    /**
     * 4.1.清空合作方层额度中间表
     */
    int truncateCoPartnerLevelLimit();

    /**
     * 4.2.插入合作方层额度客户编号和额度编号
     */
    int insertCoPartnerLevelLimit();

    /**
     * 4.3.更新合作方层额度中间表金额信息
     */
    int mergeCoPartnerLevelLimitAmount();

    /**
     * 4.4.更新额度实例金额信息
     */
    int mergeCoPartnerLevelLimitInstance();
}
