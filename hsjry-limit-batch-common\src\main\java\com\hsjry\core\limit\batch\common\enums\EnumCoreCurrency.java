package com.hsjry.core.limit.batch.common.enums;

import com.hsjry.lang.common.stereotype.enums.IEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 核心币种映射
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0 2024/4/15 15:24
 */
@Getter
@AllArgsConstructor
public enum EnumCoreCurrency implements IEnum {
    /** 人民币 */
    CNY("01", "CNY", "人民币"),
    /** 英镑 */
    GBP("12", "GBP", "英镑"),
    /** 港币 */
    HKD("13", "HKD", "港币"),
    /** 美元 */
    USD("14", "USD", "美元"),
    /** 瑞士法郎 */
    CHF("15", "CHF", "瑞士法郎"),
    /** 德国马克 */
    DEM("16", "DEM", "德国马克"),
    /** 法国法郎 */
    FRF("17", "FRF", "法国法郎"),
    /** 新加坡元 */
    SGD("18", "SGD", "新加坡元"),
    /** 荷兰盾 */
    NLG("20", "NLG", "荷兰盾"),
    /** 瑞典克朗 */
    SEK("21", "SEK", "瑞典克朗"),
    /** 丹麦克朗 */
    DKK("22", "DKK", "丹麦克朗"),
    /** 挪威克朗 */
    NOK("23", "NOK", "挪威克朗"),
    /** 日元 */
    JPY("27", "JPY", "日元"),
    /** 澳大利亚元 */
    AUD("29", "AUD", "澳大利亚元"),
    /** 马来西亚林吉特 */
    MYR("32", "MYR", "马来西亚林吉特"),
    /** 欧元 */
    EUR("33", "EUR", "欧元"),
    /** 阿尔及利亚第纳 */
    DZD("41", "DZD", "阿尔及利亚第纳"),
    /** 阿尔尼亚列克 */
    ALL("61", "ALL", "阿尔尼亚列克"),
    /** 澳门元 */
    MOP("81", "MOP", "澳门元"),
    /** 泰国铢 */
    THB("84", "THB", "泰国铢"),
    /** 印度罗比 */
    INR("85", "INR", "印度罗比"),
    /** 新西兰元 */
    NZD("87", "NZD", "新西兰元"),
    ;

    /** 状态码 */
    private String code;

    /** 目标码 */
    private String targetCode;

    /** 状态描述 */
    private String description;

    /**
     * 根据编码查找枚举
     *
     * @param code 编码
     * @return {@link EnumCoreCurrency } 实例
     **/
    public static EnumCoreCurrency find(String code) {
        for (EnumCoreCurrency instance : EnumCoreCurrency.values()) {
            if (instance.getCode()
                .equals(code)) {
                return instance;
            }
        }
        return null;
    }
}