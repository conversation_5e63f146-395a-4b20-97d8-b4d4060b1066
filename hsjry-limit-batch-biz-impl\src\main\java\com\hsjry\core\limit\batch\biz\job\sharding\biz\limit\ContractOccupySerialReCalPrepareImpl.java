// /**
//  * hsjry.com Inc.
//  * Copyright (c) 2014-2023 All Rights Reserved.
//  */
// package com.hsjry.core.limit.batch.biz.job.sharding.biz.limit;
//
// import java.math.BigDecimal;
// import java.util.ArrayList;
// import java.util.Date;
// import java.util.List;
// import java.util.Map;
// import java.util.stream.Collectors;
//
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Service;
// import org.springframework.util.CollectionUtils;
//
// import com.google.common.collect.Lists;
// import com.google.common.collect.Maps;
// import com.hsjry.base.common.job.dto.IEnumTrade;
// import com.hsjry.base.common.job.dto.JobInitDto;
// import com.hsjry.base.common.job.dto.JobShared;
// import com.hsjry.base.common.model.enums.limit.EnumCustLimitOperateStatus;
// import com.hsjry.base.common.model.enums.limit.EnumCustLimitOperateType;
// import com.hsjry.base.common.model.enums.limit.EnumCustLimitStatus;
// import com.hsjry.base.common.model.enums.limit.EnumReCalFlag;
// import com.hsjry.base.common.utils.AppParamUtil;
// import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
// import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
// import com.hsjry.core.limit.batch.common.enums.EnumCalObject;
// import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
// import com.hsjry.core.limit.center.dal.dao.intf.CustLimitOperateSerialBatchDao;
// import com.hsjry.core.limit.batch.dal.dao.intf.LcRecalContractOccupyPlanDao;
// import com.hsjry.core.limit.batch.dal.dao.model.LcRecalContractOccupyPlanDo;
// import com.hsjry.core.limit.batch.dal.dao.model.LcSliceBatchSerialDo;
// import com.hsjry.core.limit.center.dal.dao.query.CustLimitOperateSerialBatchQuery;
// import com.hsjry.core.limit.center.dal.dao.query.CustLimitOperateSerialQuery;
// import com.hsjry.core.limit.center.core.IAmtDetailCore;
// import com.hsjry.core.limit.center.core.ICustLimitQueryCore;
// import com.hsjry.core.limit.center.core.IExchangeRateCore;
// import com.hsjry.core.limit.center.core.bo.AmtDetailBo;
// import com.hsjry.core.limit.center.core.bo.CustLimitAmountBo;
// import com.hsjry.core.limit.center.core.bo.CustLimitBo;
// import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitInfoDao;
// import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoDo;
// import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitOperateSerialDo;
// import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitInfoQuery;
// import com.hsjry.lang.business.date.BusinessDateUtil;
// import com.hsjry.lang.common.utils.CollectionUtil;
// import com.hsjry.lang.common.utils.GsonUtil;
// import com.hsjry.lang.common.utils.StringUtil;
//
// import lombok.extern.slf4j.Slf4j;
//
// /**
//  * <AUTHOR>
//  * @version V4.0
//  * @since 4.0.1 2023/3/16 15:42
//  */
// @Service
// @Slf4j
// public class ContractOccupySerialReCalPrepareImpl
//     extends AbstractOccupyReCalPrepareImpl<CustLimitOperateSerialBatchQuery>
//     implements JobCoreBusiness<LcCustLimitOperateSerialDo> {
//     @Autowired
//     private IExchangeRateCore exchangeRateCore;
//     @Autowired
//     private CustLimitOperateSerialBatchDao custLimitOperateSerialBatchDao;
//     @Autowired
//     private LcCustLimitInfoDao lcCustLimitInfoDao;
//     @Autowired
//     private IAmtDetailCore iAmtDetailCore;
//     @Autowired
//     private LcRecalContractOccupyPlanDao lcRecalContractOccupyPlanDao;
//     @Autowired
//     private ICustLimitQueryCore iCustLimitQueryCore;
//
//     @Override
//     public Integer selectCountByCurrentGroupFromDb(CustLimitOperateSerialBatchQuery query) {
//         return custLimitOperateSerialBatchDao.selectCountByCurrentGroup(query);
//     }
//
//     @Override
//     public IEnumTrade getJobTrade() {
//         return EnumJobTrade.CON_LIMIT_SER_RE_CAL_PREP;
//     }
//
//     @Override
//     public ShardingResult<LcCustLimitOperateSerialDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
//         JobInitDto jobInitDto, JobShared jobShared) {
//         ShardingResult<LcCustLimitOperateSerialDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo,
//             jobShared);
//         if (StringUtil.isBlank(jobShared.getExtParam())) {
//             return shardingResult;
//         }
//         //原始查询条件
//         CustLimitOperateSerialBatchQuery limitOperateSerialBatchQuery = GsonUtil.json2Obj(jobShared.getExtParam(),
//             CustLimitOperateSerialBatchQuery.class);
//         CustLimitOperateSerialBatchQuery query = CustLimitOperateSerialBatchQuery.builder()
//             .offset(jobShared.getOffset())
//             .limit(jobShared.getLimit())
//             .closSerialNo(limitOperateSerialBatchQuery.getClosSerialNo())
//             .status(limitOperateSerialBatchQuery.getStatus())
//             .operateType(limitOperateSerialBatchQuery.getOperateType())
//             .contractRecalFlag(limitOperateSerialBatchQuery.getContractRecalFlag())
//             .exchangeRateVersion(limitOperateSerialBatchQuery.getExchangeRateVersion())
//             .build();
//         log.info("=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");
//         List<LcCustLimitOperateSerialDo> list = custLimitOperateSerialBatchDao.selectShardList(query);
//         shardingResult.setShardingResultList(list);
//         return shardingResult;
//     }
//
//     @Override
//     public void execJobCoreBusiness(ShardingResult<LcCustLimitOperateSerialDo> shardingResult) {
//         log.info("=========分片执行开始:[{}]===========", shardingResult.getJobShared()
//             .getBatchNum());
//         List<LcCustLimitOperateSerialDo> limitOperateSerialDoList = shardingResult.getShardingResultList();
//         if (CollectionUtils.isEmpty(limitOperateSerialDoList)) {
//             log.info("=========分片执行结束:" + shardingResult.getJobShared()
//                 .getBatchNum() + "数量为空=========");
//             return;
//         }
//         //获取额度编号列表
//         List<String> operateCustLimitIdList = limitOperateSerialDoList.stream()
//             .map(LcCustLimitOperateSerialDo::getCustLimitId)
//             .collect(Collectors.toList());
//         //获取未失效的额度
//         List<LcCustLimitInfoDo> notInvalidCustLimitInfoDoList = lcCustLimitInfoDao.selectByExample(
//             LcCustLimitInfoQuery.builder()
//                 .custLimitIdList(operateCustLimitIdList)
//                 .statusList(
//                     Lists.newArrayList(EnumCustLimitStatus.INEFFECTIVE.getCode(), EnumCustLimitStatus.VALID.getCode(),
//                         EnumCustLimitStatus.FROZEN.getCode(), EnumCustLimitStatus.BREAK.getCode(),
//                         EnumCustLimitStatus.EXPIRE.getCode()))
//                 .build());
//
//         //未失效的额度为空
//         if (CollectionUtil.isEmpty(notInvalidCustLimitInfoDoList)) {
//             //无需重算
//             return;
//         }
//         List<String> notInvalidCustLimitIdList = notInvalidCustLimitInfoDoList.stream()
//             .map(LcCustLimitInfoDo::getCustLimitId)
//             .collect(Collectors.toList());
//         List<LcCustLimitOperateSerialDo> operateSerialDoList = limitOperateSerialDoList.stream()
//             .filter(o -> notInvalidCustLimitIdList.contains(o.getCustLimitId()))
//             .collect(Collectors.toList());
//
//         List<String> amountIdList = Lists.newArrayList();
//         //操作流水获取金额编号
//         for (LcCustLimitOperateSerialDo operateSerialDo : operateSerialDoList) {
//             if (StringUtil.isNotBlank(operateSerialDo.getOperateLowRiskAmtId())) {
//                 amountIdList.add(operateSerialDo.getOperateLowRiskAmtId());
//             }
//             if (StringUtil.isNotBlank(operateSerialDo.getOperateAmountId())) {
//                 amountIdList.add(operateSerialDo.getOperateAmountId());
//             }
//         }
//         //金额明细分组 key amountId
//         Map<String, List<AmtDetailBo>> amtGroup = iAmtDetailCore.queryGroup(amountIdList);
//         Map<String, CustLimitBo> custLimitBoMap = Maps.newHashMap();
//         if (CollectionUtil.isNotEmpty(notInvalidCustLimitIdList)) {
//             List<CustLimitBo> custLimitBoList = iCustLimitQueryCore.queryCustLimitBoList(notInvalidCustLimitIdList);
//             if (CollectionUtil.isNotEmpty(custLimitBoList)) {
//                 custLimitBoMap.putAll(custLimitBoList.stream()
//                     .collect(Collectors.toMap(CustLimitBo::getCustLimitId, o -> o)));
//             }
//         }
//         //计划列表
//         List<LcRecalContractOccupyPlanDo> insertList = Lists.newArrayList();
//         for (LcCustLimitOperateSerialDo lcCustLimitOperateSerialDo : operateSerialDoList) {
//             try {
//                 CustLimitBo custLimitBo = custLimitBoMap.get(lcCustLimitOperateSerialDo.getCustLimitId());
//                 CustLimitAmountBo custLimitAmountBo = custLimitBo.getCustLimitAmountBo();
//                 insertList.add(buildPlan(lcCustLimitOperateSerialDo, custLimitBo, custLimitAmountBo, amtGroup));
//             } catch (Exception e) {
//                 log.error("操作流水[{}],额度[{}],合同额度预占流水重算准备异常", lcCustLimitOperateSerialDo.getClosSerialNo(),
//                     lcCustLimitOperateSerialDo.getCustLimitId());
//             }
//
//         }
//         lcRecalContractOccupyPlanDao.insertList(insertList);
//         //更新分片流水成功
//         normalUpdateSliceSerial(limitOperateSerialDoList.size(), shardingResult.getLcSliceBatchSerialDo());
//         log.info("=========分片执行结束:[{}]数量为[{}]===========", shardingResult.getJobShared()
//             .getBatchNum(), limitOperateSerialDoList.size());
//
//     }
//
//     /**
//      * 构建计划
//      *
//      * @param lcCustLimitOperateSerialDo
//      * @param custLimitAmountBo
//      * @param amtGroup
//      * @return
//      */
//     private LcRecalContractOccupyPlanDo buildPlan(LcCustLimitOperateSerialDo lcCustLimitOperateSerialDo,
//         CustLimitBo custLimitBo, CustLimitAmountBo custLimitAmountBo, Map<String, List<AmtDetailBo>> amtGroup) {
//         Date now = BusinessDateUtil.getDate();
//         //撤销预占金额
//         BigDecimal oldPre = cal(Lists.newArrayList(lcCustLimitOperateSerialDo), custLimitAmountBo.getCurrency(), false,
//             amtGroup, lcCustLimitOperateSerialDo.getExchangeRateVersion());
//         //撤销预占低风险
//         BigDecimal oldPreLowRisk = cal(Lists.newArrayList(lcCustLimitOperateSerialDo), custLimitAmountBo.getCurrency(),
//             true, amtGroup, lcCustLimitOperateSerialDo.getExchangeRateVersion());
//         //预占金额
//         BigDecimal newPre = cal(Lists.newArrayList(lcCustLimitOperateSerialDo), custLimitAmountBo.getCurrency(), false,
//             amtGroup, exchangeRateCore.getNewestVersion());
//         //预占低风险
//         BigDecimal newPreLowRisk = cal(Lists.newArrayList(lcCustLimitOperateSerialDo), custLimitAmountBo.getCurrency(),
//             true, amtGroup, exchangeRateCore.getNewestVersion());
//         //构建额度余额重算计划
//         LcRecalContractOccupyPlanDo contractOccupyPlanDo = new LcRecalContractOccupyPlanDo();
//         contractOccupyPlanDo.setPreAmountBalance(newPre);
//         contractOccupyPlanDo.setCaPreAmountBalance(oldPre);
//         contractOccupyPlanDo.setCaPreLowRiskAmtBalance(oldPreLowRisk);
//         contractOccupyPlanDo.setCreateTime(now);
//         contractOccupyPlanDo.setExchangeRateVersion(lcCustLimitOperateSerialDo.getExchangeRateVersion());
//         contractOccupyPlanDo.setLimitObjectId(custLimitBo.getLimitObjectId());
//         contractOccupyPlanDo.setPreLowRiskAmtBalance(newPreLowRisk);
//         contractOccupyPlanDo.setNewExchangeRateVersion(exchangeRateCore.getNewestVersion());
//         contractOccupyPlanDo.setUpdateTime(now);
//         contractOccupyPlanDo.setCustLimitId(custLimitBo.getCustLimitId());
//         contractOccupyPlanDo.setRelationId(lcCustLimitOperateSerialDo.getClosSerialNo());
//         contractOccupyPlanDo.setTenantId(AppParamUtil.getTenantId());
//         contractOccupyPlanDo.setCalObject(EnumCalObject.SERIAL.getCode());
//
//         return contractOccupyPlanDo;
//     }
//
//     @Override
//     public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
//         log.info("====================== 接入业务{}分片逻辑 start ================================================",
//             getJobTrade().getDescription());
//         List<JobShared> jobSharedList = new ArrayList<>();
//         //sql 批处理数量 暂定为分片数量，不放大
//         Integer batchFixNum = jobInitDto.getFixNum();
//         //当前分组的最大值，为下次 批处理的最小值
//         LcCustLimitOperateSerialDo maxLimitSerialDo = new LcCustLimitOperateSerialDo();
//         //构造查询条件 查询当前 分批处理的 排序 最大 对象
//         CustLimitOperateSerialBatchQuery query = CustLimitOperateSerialBatchQuery.builder()
//             .offset(batchFixNum - 1)
//             .status(EnumCustLimitOperateStatus.SUCCESS.getCode())
//             .operateType(EnumCustLimitOperateType.PRE_OCCUPY.getCode())
//             .contractRecalFlag(EnumReCalFlag.NOT.getCode())
//             .exchangeRateVersion(exchangeRateCore.getNewestVersion())
//             .limit(1)
//             .build();
//         //分片流水
//         int batchNum = 0;
//         while (maxLimitSerialDo != null) {
//             query.setClosSerialNo(maxLimitSerialDo.getClosSerialNo());
//             maxLimitSerialDo = custLimitOperateSerialBatchDao.selectFirstOne(query);
//             //统计分片 数量
//             batchNum = countBatchNum(batchFixNum, query, maxLimitSerialDo, batchNum, jobInitDto, jobSharedList,
//                 query.getClosSerialNo(), false);
//         }
//         log.info("====================== 接入业务{}分片逻辑 end ================================================",
//             getJobTrade().getDescription());
//         return jobSharedList;
//     }
//
// }
