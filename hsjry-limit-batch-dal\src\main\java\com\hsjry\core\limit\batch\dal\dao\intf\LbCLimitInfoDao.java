package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbCLimitInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbCLimitInfoQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度实例信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-03 10:03:12
 */
public interface LbCLimitInfoDao extends IBaseDao<LbCLimitInfoDo> {
    /**
     * 分页查询额度实例信息
     *
     * @param lbCLimitInfoQuery 条件
     * @return PageInfo<LbCLimitInfoDo>
     */
    PageInfo<LbCLimitInfoDo> selectPage(LbCLimitInfoQuery lbCLimitInfoQuery, PageParam pageParam);

    /**
     * 根据key查询额度实例信息
     *
     * @param custLimitId
     * @return
     */
    LbCLimitInfoDo selectByKey(String custLimitId);

    /**
     * 根据key删除额度实例信息
     *
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custLimitId);

    /**
     * 查询额度实例信息信息
     *
     * @param lbCLimitInfoQuery 条件
     * @return List<LbCLimitInfoDo>
     */
    List<LbCLimitInfoDo> selectByExample(LbCLimitInfoQuery lbCLimitInfoQuery);

    /**
     * 新增额度实例信息信息
     *
     * @param lbCLimitInfo 条件
     * @return int>
     */
    int insertBySelective(LbCLimitInfoDo lbCLimitInfo);

    /**
     * 修改额度实例信息信息
     *
     * @param lbCLimitInfo
     * @return
     */
    int updateBySelective(LbCLimitInfoDo lbCLimitInfo);

    /**
     * 修改额度实例信息信息
     *
     * @param lbCLimitInfo
     * @param lbCLimitInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbCLimitInfoDo lbCLimitInfo, LbCLimitInfoQuery lbCLimitInfoQuery);

    /**
     * 批量插入额度实例信息
     *
     * @param lbCLimitInfoList 批量数据
     * @return int
     */
    @Override
    int insertList(List<LbCLimitInfoDo> lbCLimitInfoList);

    /**
     * 清空额度实例信息所有数据
     *
     * @return int
     */
    int deleteAll();

    /**
     * 查询分片数据
     *
     * @param query 查询条件
     * @return List<LbCLimitInfoDo>
     */
    List<LbCLimitInfoDo> selectShardList(LbCLimitInfoQuery query);

    /**
     * 获取第一个对象，limit m，1
     *
     * @param query 查询条件
     * @return LbCLimitInfoDo
     */
    LbCLimitInfoDo selectFirstOne(LbCLimitInfoQuery query);

    /**
     * 获取当前组的数据量
     *
     * @param query 查询条件
     * @return Integer
     */
    Integer selectCountByCurrentGroup(LbCLimitInfoQuery query);
}
