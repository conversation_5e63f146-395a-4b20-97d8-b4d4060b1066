package com.hsjry.core.limit.batch.biz.job.job.file;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * 资金系统落地表日终业务额度使用情况同步处理任务
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/15 10:00
 */
@Slf4j
@Service("lbSCptlBizLmtUseSttnFileSyncJob")
public class LbSCptlBizLmtUseSttnFileSyncJob extends AbstractBaseBatchJob {
    
    public LbSCptlBizLmtUseSttnFileSyncJob() {
        log.info("LbSCptlBizLmtUseSttnFileSyncJob Bean 正在创建...");
    }
    
    @Autowired
    @Qualifier("lbSCptlBizLmtUseSttnFileSyncBizImpl")
    private BaseOrdinaryBiz baseOrdinaryBiz;

    @Override
    public BaseOrdinaryBiz getBaseOrdinaryBiz() {
        return baseOrdinaryBiz;
    }

    /**
     * 设置基础业务逻辑对象
     * 
     * @param baseOrdinaryBiz 基础业务逻辑对象
     */
    public void setBaseOrdinaryBiz(BaseOrdinaryBiz baseOrdinaryBiz) {
        this.baseOrdinaryBiz = baseOrdinaryBiz;
    }
} 