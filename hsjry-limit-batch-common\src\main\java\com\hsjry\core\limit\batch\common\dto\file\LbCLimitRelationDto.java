package com.hsjry.core.limit.batch.common.dto.file;

import java.io.Serializable;

import lombok.Builder;
import lombok.Data;

/**
 * 额度实例关联dto
 *
 * <AUTHOR>
 * @date 2025-07-09 12:31:19
 */
@Data
@Builder
public class LbCLimitRelationDto implements Serializable {
    private static final long serialVersionUID = 1942924528246259712L;
    /** 租户号 */
    private String tenantId;
    /** 创建时间 */
    private java.util.Date createTime;
    /** 更新时间 */
    private java.util.Date updateTime;
    /** 额度关系编号 */
    private String limitRelationId;
    /** 当前节点额度编号 */
    private String currentNodeLimitId;
    /** 父节点额度编号 */
    private String parentNodeLimitId;
    /** 关系类型;关联关系不校验体系，EnumLimitRelationType:001-默认、002-关联 */
    private String limitRelationType;
    /** 客户编号 */
    private String custNo;
}
