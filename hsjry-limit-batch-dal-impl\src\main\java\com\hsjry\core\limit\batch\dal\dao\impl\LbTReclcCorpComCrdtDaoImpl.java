package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.core.limit.batch.dal.dao.intf.LbTReclcCorpComCrdtDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTReclcCorpComCrdtMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpComCrdtDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpComCrdtExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpComCrdtKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcCorpComCrdtQuery;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中对公客户中一般授信额度数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-08-21 11:31:28
 */
@Repository
public class LbTReclcCorpComCrdtDaoImpl extends AbstractBaseDaoImpl<LbTReclcCorpComCrdtDo, LbTReclcCorpComCrdtMapper>
    implements LbTReclcCorpComCrdtDao {
    /**
     * 分页查询
     *
     * @param lbTReclcCorpComCrdt 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTReclcCorpComCrdtDo> selectPage(LbTReclcCorpComCrdtQuery lbTReclcCorpComCrdt,
        PageParam pageParam) {
        LbTReclcCorpComCrdtExample example = buildExample(lbTReclcCorpComCrdt);
        return PageHelper.<LbTReclcCorpComCrdtDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询额度中心-中间表-额度重算中对公客户中一般授信额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public LbTReclcCorpComCrdtDo selectByKey(String custNo, String custLimitId) {
        LbTReclcCorpComCrdtKeyDo lbTReclcCorpComCrdtKeyDo = new LbTReclcCorpComCrdtKeyDo();
        lbTReclcCorpComCrdtKeyDo.setCustNo(custNo);
        lbTReclcCorpComCrdtKeyDo.setCustLimitId(custLimitId);
        return getMapper().selectByPrimaryKey(lbTReclcCorpComCrdtKeyDo);
    }

    /**
     * 根据key删除额度中心-中间表-额度重算中对公客户中一般授信额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public int deleteByKey(String custNo, String custLimitId) {
        LbTReclcCorpComCrdtKeyDo lbTReclcCorpComCrdtKeyDo = new LbTReclcCorpComCrdtKeyDo();
        lbTReclcCorpComCrdtKeyDo.setCustNo(custNo);
        lbTReclcCorpComCrdtKeyDo.setCustLimitId(custLimitId);
        return getMapper().deleteByPrimaryKey(lbTReclcCorpComCrdtKeyDo);
    }

    /**
     * 查询额度中心-中间表-额度重算中对公客户中一般授信额度信息
     *
     * @param lbTReclcCorpComCrdt 条件
     * @return List<LbTReclcCorpComCrdtDo>
     */
    @Override
    public List<LbTReclcCorpComCrdtDo> selectByExample(LbTReclcCorpComCrdtQuery lbTReclcCorpComCrdt) {
        return getMapper().selectByExample(buildExample(lbTReclcCorpComCrdt));
    }

    /**
     * 新增额度中心-中间表-额度重算中对公客户中一般授信额度信息
     *
     * @param lbTReclcCorpComCrdt 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTReclcCorpComCrdtDo lbTReclcCorpComCrdt) {
        if (lbTReclcCorpComCrdt == null) {
            return -1;
        }
        return getMapper().insertSelective(lbTReclcCorpComCrdt);
    }

    /**
     * 修改额度中心-中间表-额度重算中对公客户中一般授信额度信息
     *
     * @param lbTReclcCorpComCrdt
     * @return
     */
    @Override
    public int updateBySelective(LbTReclcCorpComCrdtDo lbTReclcCorpComCrdt) {
        if (lbTReclcCorpComCrdt == null) {
            return -1;
        }
        return getMapper().updateByPrimaryKeySelective(lbTReclcCorpComCrdt);
    }

    @Override
    public int updateBySelectiveByExample(LbTReclcCorpComCrdtDo lbTReclcCorpComCrdt,
        LbTReclcCorpComCrdtQuery lbTReclcCorpComCrdtQuery) {
        return getMapper().updateByExampleSelective(lbTReclcCorpComCrdt, buildExample(lbTReclcCorpComCrdtQuery));
    }

    /**
     * 构建额度中心-中间表-额度重算中对公客户中一般授信额度Example信息
     *
     * @param lbTReclcCorpComCrdt
     * @return
     */
    public LbTReclcCorpComCrdtExample buildExample(LbTReclcCorpComCrdtQuery lbTReclcCorpComCrdt) {
        LbTReclcCorpComCrdtExample example = new LbTReclcCorpComCrdtExample();
        LbTReclcCorpComCrdtExample.Criteria criteria = example.createCriteria();
        if (lbTReclcCorpComCrdt != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbTReclcCorpComCrdt.getCustNo())) {
                criteria.andCustNoEqualTo(lbTReclcCorpComCrdt.getCustNo());
            }
            if (StringUtil.isNotEmpty(lbTReclcCorpComCrdt.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lbTReclcCorpComCrdt.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lbTReclcCorpComCrdt.getTemplateNodeId())) {
                criteria.andTemplateNodeIdEqualTo(lbTReclcCorpComCrdt.getTemplateNodeId());
            }
            if (StringUtil.isNotEmpty(lbTReclcCorpComCrdt.getLimitStatus())) {
                criteria.andLimitStatusEqualTo(lbTReclcCorpComCrdt.getLimitStatus());
            }
            if (null != lbTReclcCorpComCrdt.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbTReclcCorpComCrdt.getTotalAmount());
            }
            if (null != lbTReclcCorpComCrdt.getPreOccupyAmount()) {
                criteria.andPreOccupyAmountEqualTo(lbTReclcCorpComCrdt.getPreOccupyAmount());
            }
            if (null != lbTReclcCorpComCrdt.getRealOccupyAmount()) {
                criteria.andRealOccupyAmountEqualTo(lbTReclcCorpComCrdt.getRealOccupyAmount());
            }
            if (null != lbTReclcCorpComCrdt.getLowRiskAmount()) {
                criteria.andLowRiskAmountEqualTo(lbTReclcCorpComCrdt.getLowRiskAmount());
            }
            if (null != lbTReclcCorpComCrdt.getPreOccupyLowRiskAmt()) {
                criteria.andPreOccupyLowRiskAmtEqualTo(lbTReclcCorpComCrdt.getPreOccupyLowRiskAmt());
            }
            if (null != lbTReclcCorpComCrdt.getRealOccupyLowRiskAmt()) {
                criteria.andRealOccupyLowRiskAmtEqualTo(lbTReclcCorpComCrdt.getRealOccupyLowRiskAmt());
            }
        }
        buildExampleExt(lbTReclcCorpComCrdt, criteria);
        return example;
    }

    /**
     * 构建额度中心-中间表-额度重算中对公客户中一般授信额度ExampleExt方法
     *
     * @param lbTReclcCorpComCrdt
     * @return
     */
    public void buildExampleExt(LbTReclcCorpComCrdtQuery lbTReclcCorpComCrdt,
        LbTReclcCorpComCrdtExample.Criteria criteria) {

        //自定义实现
    }

    // ==================== 一般授信额度重算相关方法实现 ====================

    /**
     * 1.1.清空一般授信额度中间表
     */
    @Override
    public int truncateCommonCreditLimit() {
        return getMapper().truncateCommonCreditLimit();
    }

    /**
     * 1.2.插入一般授信额度客户编号和额度编号
     */
    @Override
    public int insertCommonCreditLimit() {
        return getMapper().insertCommonCreditLimit();
    }

    /**
     * 1.3.更新一般授信额度中间表金额信息
     */
    @Override
    public int mergeCommonCreditLimitAmount() {
        return getMapper().mergeCommonCreditLimitAmount();
    }

    /**
     * 1.4.更新额度实例金额信息
     */
    @Override
    public int mergeCommonCreditLimitInstance() {
        return getMapper().mergeCommonCreditLimitInstance();
    }

}
