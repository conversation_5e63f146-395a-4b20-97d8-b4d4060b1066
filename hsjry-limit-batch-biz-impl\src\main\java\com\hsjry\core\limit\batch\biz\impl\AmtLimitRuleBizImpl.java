package com.hsjry.core.limit.batch.biz.impl;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.framework.property.PropertyResolver;
import com.hsjry.base.common.model.enums.limit.EnumMsgConfigStatus;
import com.hsjry.base.common.model.enums.limit.EnumNoticeType;
import com.hsjry.core.limit.batch.biz.AmtLimitRuleBiz;
import com.hsjry.core.limit.batch.common.enums.EnumLimitBatchErrorCode;
import com.hsjry.core.limit.center.dal.dao.intf.AmtLimitRuleBatchDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcAmtLimitMsgConfigDao;
import com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitMsgConfigDo;
import com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitRuleDo;
import com.hsjry.core.limit.center.dal.dao.query.LcAmtLimitMsgConfigQuery;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0 2023/4/23 13:57
 */
@Service
@Slf4j
public class AmtLimitRuleBizImpl implements AmtLimitRuleBiz {
    @Autowired
    private AmtLimitRuleBatchDao amtLimitRuleBatchDao;
    @Autowired
    private LcAmtLimitMsgConfigDao lcAmtLimitMsgConfigDao;


    @Override
    public void disableAmtLimitRemind(Date date) {
        String disableAdvanceDay = PropertyResolver.getValue("disable_advance_day");
        if (StringUtil.isEmpty(disableAdvanceDay)) {
            throw new HsjryBizException(EnumLimitBatchErrorCode.DISABLE_ADVANCE_DAY.getCode(),
                EnumLimitBatchErrorCode.DISABLE_ADVANCE_DAY.getDescription());
        }
        String[] split = disableAdvanceDay.split(",");
        // 循环配置天数
        for (String remindDay : split) {
            // 查询即将到期的限额规则
            List<LcAmtLimitRuleDo> lcAmtLimitRuleDoList = amtLimitRuleBatchDao.queryDisableAmtLimitRuleList(date,
                Integer.parseInt(remindDay));
            if(CollectionUtil.isEmpty(lcAmtLimitRuleDoList)){
                continue;
            }
            List<String> ruleIdList = lcAmtLimitRuleDoList.stream()
                .map(LcAmtLimitRuleDo::getRuleId)
                .collect(Collectors.toList());
            for (String ruleId : ruleIdList) {
                List<LcAmtLimitMsgConfigDo> lcAmtLimitMsgConfigDoList = lcAmtLimitMsgConfigDao.selectByExample(
                    LcAmtLimitMsgConfigQuery.builder()
                        .ruleId(ruleId)
                        .noticeType(EnumNoticeType.DISABLE.getCode())
                        .msgConfigStatus(EnumMsgConfigStatus.ENABLE.getCode())
                        .build());
                if(CollectionUtil.isEmpty(lcAmtLimitMsgConfigDoList)){
                    continue;
                }
                log.info("限额规则" + ruleId + "将在" + remindDay + "天后停用");
                // 发送消息
            }
        }
    }
}
