package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 网贷系统-落地表-产品额度信息（记录客户产品额度信息）主键
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
@Table(name = "lb_s_ol_prod_lmt_info")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LbSOlProdLmtInfoKeyDo implements Serializable {

    private static final long serialVersionUID = 1942415996337979399L;
    /** 额度编号 */
    @Id
    @Column(name = "credit_limit_id")
    private String creditLimitId;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
}