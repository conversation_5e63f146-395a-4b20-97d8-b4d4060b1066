---
description: 测试开发指南，包含单元测试和集成测试规范
alwaysApply: true
---
# 测试指南

## 测试模块

测试代码位于 `hsjry-limit-batch-test` 模块中，具体路径为 `hsjry-limit-batch-test/src/test/java/com/hsjry/core/limit/batch/batch/test/`。

## 基础测试类

### 抽象基类
- [AbstractBaseTest.java](mdc:hsjry-limit-batch-test/src/test/java/com/hsjry/core/limit/batch/batch/test/base/AbstractBaseTest.java) - 所有测试的基础抽象类
- [AbstractBaseShardingTest.java](mdc:hsjry-limit-batch-test/src/test/java/com/hsjry/core/limit/batch/batch/test/base/AbstractBaseShardingTest.java) - 分片相关测试的基础抽象类

## 功能测试类

### 限额相关测试
- [AmtLimitDailyReportTest.java](mdc:hsjry-limit-batch-test/src/test/java/com/hsjry/core/limit/batch/batch/test/AmtLimitDailyReportTest.java) - 金额限额日报测试
- [AmtLimitReCalTest.java](mdc:hsjry-limit-batch-test/src/test/java/com/hsjry/core/limit/batch/batch/test/AmtLimitReCalTest.java) - 金额限额重新计算测试
- [AmtLimitValidTest.java](mdc:hsjry-limit-batch-test/src/test/java/com/hsjry/core/limit/batch/batch/test/AmtLimitValidTest.java) - 金额限额验证测试

## 测试配置

测试模块使用Spring Boot测试框架，所有测试类都应该继承自相应的基础测试类，以确保正确的测试环境配置。

## 测试最佳实践

1. **继承基础类**: 所有测试类应该继承 `AbstractBaseTest` 或 `AbstractBaseShardingTest`
2. **分片测试**: 涉及分片逻辑的测试应该使用 `AbstractBaseShardingTest`
3. **命名约定**: 测试类名应以 `Test` 结尾
4. **功能分组**: 按功能模块组织测试类，如限额相关的测试都以 `AmtLimit` 开头

