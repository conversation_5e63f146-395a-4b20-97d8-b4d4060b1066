/*
 * yunrong.cn Inc. Copyright (c) 2014-2019 All Rights Reserved
 */

package com.hsjry.core.limit.batch.biz.job.sharding.structal;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.biz.BaseBusinessPartitionBiz;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.limit.EnumLimitHandlerStatus;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.JobCoreBusinessFactory;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.common.utils.IpUtil;
import com.hsjry.core.limit.batch.common.utils.JobInitDtoRewriteUtil;
import com.hsjry.core.limit.center.dal.dao.intf.LcSliceBatchSerialDao;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.lang.common.exception.JobBizException;
import com.hsjry.lang.common.utils.DateUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version V3.0
 * @since 3.0.1 2020/10/23 15:57
 */
@Slf4j
@Service
@Order(1)
public class BusinessPartitionBizImpl implements BaseBusinessPartitionBiz {

    @Autowired
    private LcSliceBatchSerialDao lcSliceBatchSerialDao;

    /**
     * 执行分片数据批处理操作
     * 根据 批次总流水 批次号 租户好 字段，作为任务幂等流水
     * 处理逻辑为 ：
     * <ul>1、校验分片信息</ul>
     * <ul>2、检查分片数据处理状态 </ul>
     * <ul>
     * <ul>若数据已经处理，return 处理，</ul>
     * <ul>若数据未处理或者状态没有处理成功，继续执行业务逻辑，</ul>
     * </ul>
     * <ul>3、嵌入分片业务处理,若下游业务执行成功，成功的记录由下游业务记录 成功的分片流水记录</ul>
     * <ul>4、记录业务处理情况</ul>
     *
     * @param jobInitDto 初始化job 入参
     * @return 处理情况
     */
    @Override
    public Boolean dealJobSharding(JobInitDto jobInitDto) {

        // 校验任务调度交易码
        checkYrJobTrade(jobInitDto.getJobTrade());
        //重写 批次流水,交易时间 到上下文
        JobInitDtoRewriteUtil.rewriteBusinessDate(jobInitDto);

        //构建分片对象
        JobShared jobShared = extractAndCheckJobShardInfo(jobInitDto.getPartitionInfo());
        LcSliceBatchSerialDo lcSliceBatchSerialDo = lcSliceBatchSerialDao.selectByKey(jobShared.getBatchSerialNo(),
            jobShared.getBatchNum());
        if (lcSliceBatchSerialDo != null && EnumLimitHandlerStatus.SUCCESS.getCode().equals(
            lcSliceBatchSerialDo.getSharedStatus())) {
            log.info("分片数据已处理！");
            return true;
        }

        //构建当前分片流水信息
        LcSliceBatchSerialDo insetCreditSliceBatchSerialDo = this.buildCreditSliceBatchSerialDo(jobInitDto, jobShared);

        //若 分片流水不存在 ，新增 一条处理中的数据
        if (lcSliceBatchSerialDo == null) {
            lcSliceBatchSerialDao.insertBySelective(insetCreditSliceBatchSerialDo);
            lcSliceBatchSerialDo = insetCreditSliceBatchSerialDo;
        } else {
            //更新ip信息，实际处理机器信息 更新，合并文件需要此字段
            lcSliceBatchSerialDo.setExecIp(IpUtil.getIPAddress());
        }
        //分片数据 下游业务处理
        try {
            //复制分片流水信息 传递到业务层
            JobCoreBusiness jobCoreBusiness = getJobCoreBusiness(jobInitDto.getJobTrade());
            //获取分片信息
            ShardingResult shardingResult = jobCoreBusiness.queryShardingResult(lcSliceBatchSerialDo, jobInitDto,
                jobShared);
            //执行分片信息
            jobCoreBusiness.execJobCoreBusiness(shardingResult);
        } catch (Exception e) {
            log.error("分片任务处理失败", e);
            //分片处理失败 数据库记录
            upsertFailAssetSliceBatchSerial(lcSliceBatchSerialDo);
            // 分片数据 下游业务处理 异常处理 ，是否重试
            throw new JobBizException(EnumBatchJobError.JOB_EXEC_BIZ_ERROR.getCode(),
                EnumBatchJobError.JOB_EXEC_BIZ_ERROR.getDescription(), getClass(), e);
        }

        return true;
    }

    /**
     * 构建当前分片流水信息
     *
     * @param jobInitDto 初始化job 任务参数
     * @param jobShared 业务分片参数
     * @return 分片流水信息
     */
    private LcSliceBatchSerialDo buildCreditSliceBatchSerialDo(JobInitDto jobInitDto, JobShared jobShared) {
        LcSliceBatchSerialDo insetCreditSliceBatchSerialDo = new LcSliceBatchSerialDo();
        insetCreditSliceBatchSerialDo.setBatchSerialNo(jobShared.getBatchSerialNo());
        insetCreditSliceBatchSerialDo.setBatchNum(jobShared.getBatchNum());
        insetCreditSliceBatchSerialDo.setTradeCode(jobInitDto.getJobTrade().getCode());
        insetCreditSliceBatchSerialDo.setExecIp(IpUtil.getIPAddress());
        insetCreditSliceBatchSerialDo.setFailureTimes(0);
        insetCreditSliceBatchSerialDo.setSharedDetail(jobInitDto.getPartitionInfo());
        insetCreditSliceBatchSerialDo.setSharedStatus(EnumLimitHandlerStatus.IN_HANDLE.getCode());
        //外部流水号
        insetCreditSliceBatchSerialDo.setExtSerialId(jobInitDto.getParttionSerialId());
        insetCreditSliceBatchSerialDo.setExecDate(Integer.valueOf(DateUtil.getNowDate(DateUtil.DATE_FORMAT_2)));
        return insetCreditSliceBatchSerialDo;
    }

    /**
     * 校验任务调度交易码
     *
     * @param yrJobTrade 任务调度交易码
     */
    private void checkYrJobTrade(IEnumTrade yrJobTrade) {
        if (yrJobTrade == null) {
            log.error("任务调度交易码为空！");
            throw new JobBizException(EnumBatchJobError.JOB_TRADE_CODE_ERROR.getCode(),
                EnumBatchJobError.JOB_TRADE_CODE_ERROR.getDescription(), getClass());
        }
    }

    /**
     * 校验 提取 分片对象
     *
     * @param partitionInfo 分片信息的json 字符串
     * @return 分片对象
     */
    private JobShared extractAndCheckJobShardInfo(String partitionInfo) {
        //构建分片对象
        JobShared jobShared = GsonUtil.json2Obj(partitionInfo, JobShared.class);
        if (jobShared == null) {
            log.error("分片信息错误！");
            throw new JobBizException(EnumBatchJobError.SHARDING_INFO_ERROR.getCode(),
                EnumBatchJobError.SHARDING_INFO_ERROR.getDescription(), getClass());
        }
        /*
         * 分片信息校验
         */
        if (StringUtil.isBlank(jobShared.getBatchSerialNo()) || jobShared.getBatchNum() == null) {
            log.error("分片流水 或 批次号为空！");
            throw new JobBizException(EnumBatchJobError.SHARDING_PRIMARY_ERROR.getCode(),
                EnumBatchJobError.SHARDING_PRIMARY_ERROR.getDescription(), getClass());
        }
        return jobShared;
    }

    /**
     * 分片处理失败 更新或新增分片流水记录
     * 记录分片流水失败次数 和 状态
     *
     * @param assetSliceBatchSerialDo 数据库中的原始分片流水记录 可能为空
     */
    private void upsertFailAssetSliceBatchSerial(LcSliceBatchSerialDo assetSliceBatchSerialDo) {

        if (assetSliceBatchSerialDo == null || assetSliceBatchSerialDo.getSharedStatus().equals(
            EnumLimitHandlerStatus.SUCCESS.getCode())) {
            log.error("分片流水记录状态异常！");
            throw new JobBizException(EnumBatchJobError.SHARDING_SERIAL_STATUS_ERROR.getCode(),
                EnumBatchJobError.SHARDING_SERIAL_STATUS_ERROR.getDescription(), getClass());
        }
        //只有当状态 为不成功的时候 才要更新 失败状态
        if (!EnumLimitHandlerStatus.SUCCESS.getCode().equals(assetSliceBatchSerialDo.getSharedStatus())) {
            assetSliceBatchSerialDo.setFailureTimes(
                (assetSliceBatchSerialDo.getFailureTimes() == null ? 1 : assetSliceBatchSerialDo.getFailureTimes())
                    + 1);
            assetSliceBatchSerialDo.setSharedStatus(EnumLimitHandlerStatus.FAIL.getCode());
            lcSliceBatchSerialDao.updateBySelective(assetSliceBatchSerialDo);
        }
    }

    /**
     * 获取核心业务
     *
     * @param jobTrade 任务交易码
     */
    private JobCoreBusiness getJobCoreBusiness(IEnumTrade jobTrade) {
        if (jobTrade == null) {
            log.error("任务交易码为空！");
            throw new JobBizException(EnumBatchJobError.JOB_TRADE_CODE_ERROR.getCode(),
                EnumBatchJobError.JOB_TRADE_CODE_ERROR.getDescription(), getClass());
        }

        JobCoreBusiness jobCoreBusiness = JobCoreBusinessFactory.getJobCoreBusiness(jobTrade.getCode());

        if (jobCoreBusiness == null) {
            log.error("未找到任务交易码【{}】对应的批量调度任务下游 核心业务实现类！", jobTrade.getCode());
            throw new JobBizException(EnumBatchJobError.SHARDING_INFO_ERROR.getCode(),
                EnumBatchJobError.SHARDING_INFO_ERROR.getDescription(), getClass());
        }
        return jobCoreBusiness;
    }

    /**
     * 分片任务执行前，前置处理
     *
     * @param jobInitDto
     */
    @Override
    public void preHandle(JobInitDto jobInitDto) {
        getJobCoreBusiness(jobInitDto.getJobTrade()).preHandle(jobInitDto);
    }

    /**
     * 分片任务执行后，后面处理
     *
     * @param jobInitDto
     */
    @Override
    public void afterHandle(JobInitDto jobInitDto) {
        getJobCoreBusiness(jobInitDto.getJobTrade()).afterHandle(jobInitDto);
    }

    @Override
    public Class getEnumTradeClass() {
        return EnumJobTrade.class;
    }
}
