package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpTotlDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcCorpTotlQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中对公客户中总额度数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-08-21 11:31:28
 */
public interface LbTReclcCorpTotlDao extends IBaseDao<LbTReclcCorpTotlDo> {
    /**
     * 分页查询额度中心-中间表-额度重算中对公客户中总额度
     *
     * @param lbTReclcCorpTotlQuery 条件
     * @return PageInfo<LbTReclcCorpTotlDo>
     */
    PageInfo<LbTReclcCorpTotlDo> selectPage(LbTReclcCorpTotlQuery lbTReclcCorpTotlQuery, PageParam pageParam);

    /**
     * 根据key查询额度中心-中间表-额度重算中对公客户中总额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    LbTReclcCorpTotlDo selectByKey(String custNo, String custLimitId);

    /**
     * 根据key删除额度中心-中间表-额度重算中对公客户中总额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custNo, String custLimitId);

    /**
     * 查询额度中心-中间表-额度重算中对公客户中总额度信息
     *
     * @param lbTReclcCorpTotlQuery 条件
     * @return List<LbTReclcCorpTotlDo>
     */
    List<LbTReclcCorpTotlDo> selectByExample(LbTReclcCorpTotlQuery lbTReclcCorpTotlQuery);

    /**
     * 新增额度中心-中间表-额度重算中对公客户中总额度信息
     *
     * @param lbTReclcCorpTotl 条件
     * @return int>
     */
    int insertBySelective(LbTReclcCorpTotlDo lbTReclcCorpTotl);

    /**
     * 修改额度中心-中间表-额度重算中对公客户中总额度信息
     *
     * @param lbTReclcCorpTotl
     * @return
     */
    int updateBySelective(LbTReclcCorpTotlDo lbTReclcCorpTotl);

    /**
     * 修改额度中心-中间表-额度重算中对公客户中总额度信息
     *
     * @param lbTReclcCorpTotl
     * @param lbTReclcCorpTotlQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTReclcCorpTotlDo lbTReclcCorpTotl, LbTReclcCorpTotlQuery lbTReclcCorpTotlQuery);

    // ==================== 客户总额度重算相关方法 ====================

    /**
     * 7.1.清空客户总额度中间表
     */
    int truncateTotalLimit();

    /**
     * 7.2.插入客户总额度客户编号和额度编号
     */
    int insertTotalLimit();

    /**
     * 7.3.更新客户总额度中间表金额信息
     */
    int mergeTotalLimitAmount();

    /**
     * 7.4.更新额度实例金额信息
     */
    int mergeTotalLimitInstance();
}
