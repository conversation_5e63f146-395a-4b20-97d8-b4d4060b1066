package com.hsjry.core.limit.batch.biz.job.sharding.biz.mid;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.limit.AbstractShardingPrepareBiz;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTIbnkCustInfoDao;
import com.hsjry.core.limit.center.dal.dao.intf.CustLimitObjectInfoBatchDao;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitObjectInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.CustLimitObjectInfoQuery;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 额度中心-中间表-同业客户信息处理
 * 执行truncate + insert SQL操作，将LC_CUST_LIMIT_OBJECT_INFO中的同业客户数据导入到LB_T_IBNK_CUST_INFO
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Slf4j
@Service("lbTIbnkCustInfoProcessImpl")
@RequiredArgsConstructor
public class LbTIbnkCustInfoProcessImpl extends AbstractShardingPrepareBiz<CustLimitObjectInfoQuery>
    implements JobCoreBusiness<LcCustLimitObjectInfoDo> {

    private final LbTIbnkCustInfoDao lbTIbnkCustInfoDao;
    private final CustLimitObjectInfoBatchDao custLimitObjectInfoBatchDao;

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.LB_T_IBNK_CUST_INFO_PROCESS;
    }

    @Override
    public Integer selectCountByCurrentGroupFromDb(CustLimitObjectInfoQuery query) {
        return custLimitObjectInfoBatchDao.selectCountByCurrentGroup(query);
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);
        log.info(prefixLog + "====================== 分片逻辑 start ================================================");

        List<JobShared> jobSharedList = new ArrayList<>();
        // SQL批处理数量，暂定为分片数量
        Integer fixNum = jobInitDto.getFixNum();

        // 获取总数据量
        CustLimitObjectInfoQuery countQuery = CustLimitObjectInfoQuery.builder()
            .tenantId(AppParamUtil.getTenantId())
            .build();
        Integer totalCount = custLimitObjectInfoBatchDao.selectCountByCurrentGroup(countQuery);

        if (totalCount == null || totalCount == 0) {
            log.info(prefixLog + "没有数据需要处理，总数据量: {}", totalCount);
            return jobSharedList;
        }

        log.info(prefixLog + "总数据量: {}, 分片大小: {}", totalCount, fixNum);

        // 计算分片数量
        Integer shardCount = (totalCount + fixNum - 1) / fixNum; // 向上取整

        // 生成分片
        for (int i = 0; i < shardCount; i++) {
            int batchNum = i + 1;
            int offset = i * fixNum;
            int limit = Math.min(fixNum, totalCount - offset);

            // 创建分片查询条件
            CustLimitObjectInfoQuery shardQuery = CustLimitObjectInfoQuery.builder()
                .tenantId(AppParamUtil.getTenantId())
                .offset(offset)
                .limit(limit)
                .build();

            // 创建JobShared对象
            JobShared jobShared = new JobShared();
            jobShared.setBatchSerialNo(batchSerialNo);
            jobShared.setBusinessDate(businessDate);
            jobShared.setBatchNum(batchNum);
            jobShared.setOffset(offset);
            jobShared.setLimit(limit);
            jobShared.setFixNum(fixNum);
            jobShared.setInPara(jobInitDto.getInPara());
            jobShared.setExtParam(GsonUtil.obj2Json(shardQuery));

            jobSharedList.add(jobShared);

            log.info(prefixLog + "生成分片[{}]: offset={}, limit={}", batchNum, offset, limit);
        }

        log.info(prefixLog + "====================== 分片逻辑 end ================================================");
        log.info(prefixLog + "同业客户信息处理分片任务生成完成,共{}个分片", jobSharedList.size());
        return jobSharedList;
    }

    @Override
    public ShardingResult<LcCustLimitObjectInfoDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info(prefixLog + "开始查询分片数据,分片号:[{}]", batchNum);

        ShardingResult<LcCustLimitObjectInfoDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            log.warn(prefixLog + "分片[{}]的extParam为空，跳过查询", batchNum);
            return shardingResult;
        }

        try {
            // 从extParam中解析查询条件
            CustLimitObjectInfoQuery query = GsonUtil.json2Obj(jobShared.getExtParam(), CustLimitObjectInfoQuery.class);
            if (query == null) {
                log.error(prefixLog + "分片[{}]的extParam解析失败: {}", batchNum, jobShared.getExtParam());
                return shardingResult;
            }

            log.info(prefixLog + "=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");

            // 性能监控
            long startTime = System.currentTimeMillis();

            // 查询分片数据
            List<LcCustLimitObjectInfoDo> dataList = custLimitObjectInfoBatchDao.selectShardList(query);
            shardingResult.setShardingResultList(dataList);

            long endTime = System.currentTimeMillis();
            log.info(prefixLog + "分片数据查询完成,分片号:[{}],数据量:[{}],耗时:[{}]ms",
                batchNum, CollectionUtil.isEmpty(dataList) ? 0 : dataList.size(), (endTime - startTime));

            // 性能告警
            if (dataList != null && dataList.size() > jobInitDto.getFixNum() * 2) {
                log.warn(prefixLog + "分片数据量过大,分片号:[{}],数据量:[{}],可能影响性能",
                    batchNum, dataList.size());
            }

        } catch (Exception e) {
            log.error(prefixLog + "分片[{}]查询数据时发生异常", batchNum, e);
            throw new RuntimeException("分片数据查询异常: " + e.getMessage(), e);
        }

        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LcCustLimitObjectInfoDo> shardingResult) {
        List<LcCustLimitObjectInfoDo> shardingDataList = shardingResult.getShardingResultList();
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        Integer dataSize = shardingResult.getShardingResultList().size();

        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);
        log.info(prefixLog + "=========分片执行开始:[{}]数量为[{}]===========", batchNum, dataSize);

        // 判空处理，若分片数据为空直接返回
        if (CollectionUtil.isEmpty(shardingDataList)) {
            log.info(prefixLog + "=========分片执行结束:[{}]数量为空===========", batchNum);
            return;
        }

        try {
            LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();
            // 只在第一个分片中清空目标表
            if (sliceBatchSerialDo.getBatchNum() == 1) {
                // 执行truncate操作
                log.info(prefixLog + "开始执行truncate table LB_T_IBNK_CUST_INFO");
                int truncateResult = lbTIbnkCustInfoDao.truncateTable();
                log.info(prefixLog + "truncate操作完成,影响行数:{}", truncateResult);
            }
            //查询[网贷系统-落地表-合同信息]中所有的[USER_ID]
            List<String> userIdList = shardingDataList.stream().filter(Objects::nonNull)//
                .map(LcCustLimitObjectInfoDo::getUserId)//
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
            // 执行insert from source操作
            log.info(prefixLog + "开始从LC_CUST_LIMIT_OBJECT_INFO导入同业客户数据");
            int insertResult = lbTIbnkCustInfoDao.insertFromSource(userIdList);
            log.info(prefixLog + "数据导入完成,成功插入{}条记录", insertResult);
            // 更新分片流水成功
            normalUpdateSliceSerial(insertResult, sliceBatchSerialDo);

        } catch (Exception e) {
            log.error(prefixLog + "分片执行异常:[{}]", batchNum, e);
            throw new HsjryBizException(EnumBatchJobError.SYSTEM_ERR.getCode(),
                EnumBatchJobError.SYSTEM_ERR.getDescription());
        }

        log.info(prefixLog + "=========分片执行结束:[{}]===========", batchNum);
    }
} 