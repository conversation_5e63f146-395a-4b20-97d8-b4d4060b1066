/*
 * yunrong.cn Inc. Copyright (c) 2014-2019 All Rights Reserved
 */

package com.hsjry.core.limit.batch.common.utils;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;

import lombok.extern.slf4j.Slf4j;

/**
 * Ip 工具
 *
 * <AUTHOR>
 * @version $$Id: IpUtil.java, v 0.1 2019-03-05 17:09 陈中强_yrchenzq25214 Exp $$
 */
@Slf4j
public class IpUtil {

    /**
     * 获取Ip
     *
     * @return 本地IP
     */
    public static String getIPAddress() {

        try {
            return getRealIpAddress();
        } catch (Exception e) {
            log.error("get ip address error", e);
        }
        return null;
    }

    private static String getRealIpAddress() throws SocketException {
        String ip = "";
        for (Enumeration<NetworkInterface> en = NetworkInterface.getNetworkInterfaces(); en.hasMoreElements(); ) {
            NetworkInterface networkInterface = en.nextElement();
            String name = networkInterface.getName();
            if (!name.contains("docker") && !name.contains("lo")) {
                for (Enumeration<InetAddress> addressEnumeration
                     = networkInterface.getInetAddresses(); addressEnumeration.hasMoreElements(); ) {
                    InetAddress inetAddress = addressEnumeration.nextElement();
                    if (!inetAddress.isLoopbackAddress()) {
                        String ipAddress = inetAddress.getHostAddress();
                        if (!ipAddress.contains("::") && !ipAddress.contains("0:0:") && !ipAddress.contains("fe80")
                            && !ipAddress.equals("127.0.0.1")) {
                            ip = ipAddress;
                        }
                    }
                }
            }
        }
        return ip;
    }

}
