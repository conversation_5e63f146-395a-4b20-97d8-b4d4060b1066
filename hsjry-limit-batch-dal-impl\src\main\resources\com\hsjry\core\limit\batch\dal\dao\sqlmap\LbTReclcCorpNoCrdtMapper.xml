<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.batch.dal.dao.mapper.LbTReclcCorpNoCrdtMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpNoCrdtDo">
        <result property="custNo" column="cust_no" jdbcType="VARCHAR"/> <!-- 客户编号 -->
        <result property="custLimitId" column="cust_limit_id" jdbcType="VARCHAR"/> <!-- 额度编号 -->
        <result property="templateNodeId" column="template_node_id" jdbcType="VARCHAR"/> <!-- 模板节点编号 -->
        <result property="limitStatus" column="limit_status" jdbcType="VARCHAR"/> <!-- 额度状态 -->
        <result property="totalAmount" column="total_amount" jdbcType="DECIMAL"/> <!-- 总额度 -->
        <result property="preOccupyAmount" column="pre_occupy_amount" jdbcType="DECIMAL"/> <!-- 预占额度 -->
        <result property="realOccupyAmount" column="real_occupy_amount" jdbcType="DECIMAL"/> <!-- 实占额度 -->
        <result property="lowRiskAmount" column="low_risk_amount" jdbcType="DECIMAL"/> <!-- 总低风险额度 -->
        <result property="preOccupyLowRiskAmt" column="pre_occupy_low_risk_amt" jdbcType="DECIMAL"/> <!-- 预占低风险 -->
        <result property="realOccupyLowRiskAmt" column="real_occupy_low_risk_amt" jdbcType="DECIMAL"/> <!-- 实占低风险 -->
    </resultMap>
    <sql id="Base_Column_List">
        cust_no
        , cust_limit_id
                , template_node_id
                , limit_status
                , total_amount
                , pre_occupy_amount
                , real_occupy_amount
                , low_risk_amount
                , pre_occupy_low_risk_amt
                , real_occupy_low_risk_amt
    </sql>
</mapper>