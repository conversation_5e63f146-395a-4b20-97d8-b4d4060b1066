/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.sharding.biz.amtlimit;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Table;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.limit.EnumAmtLimitOperateStatus;
import com.hsjry.base.common.model.enums.limit.EnumLimitEntityStatus;
import com.hsjry.base.common.model.enums.limit.EnumReCalAmtLimitMap;
import com.hsjry.base.common.model.enums.limit.EnumReCalFiled;
import com.hsjry.base.common.model.enums.limit.EnumReCalFlag;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.limit.AbstractShardingPrepareBiz;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.center.dal.dao.intf.EntityInfoBatchDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcAmtLimitRecordDetailDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcAmtLimitRecordSerialDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcEntityInfoDao;
import com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitRecordSerialDo;
import com.hsjry.core.limit.center.dal.dao.model.LcEntityInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.EntityInfoBatchQuery;
import com.hsjry.core.limit.center.dal.dao.query.LcAmtLimitRecordSerialQuery;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.extern.slf4j.Slf4j;
/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/22 16:32
 */
@Service
@Slf4j
public class AmtLimitReCalImpl extends AbstractShardingPrepareBiz<EntityInfoBatchQuery>
    implements JobCoreBusiness<LcEntityInfoDo> {
    @Autowired
    private EntityInfoBatchDao entityInfoBatchDao;
    // @Autowired
    // private IExchangeRateCore exchangeRateCore;
    @Autowired
    private LcAmtLimitRecordSerialDao lcAmtLimitRecordSerialDao;
    @Autowired
    private LcEntityInfoDao lcEntityInfoDao;

    // @Autowired
    // private IAmtLimitRuleCore iAmtLimitRuleCore;
    // @Autowired
    // private IAmtDetailCore iAmtDetailCore;
    @Autowired
    private LcAmtLimitRecordDetailDao lcAmtLimitRecordDetailDao;
    @Autowired
    @Qualifier("limitTransactionNewTemplate")
    private TransactionTemplate transactionTemplate;

    @Override
    public Integer selectCountByCurrentGroupFromDb(EntityInfoBatchQuery query) {
        return entityInfoBatchDao.selectCountByCurrentGroup(query);
    }

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.AMT_LIMIT_RE_CAL;
    }

    @Override
    public ShardingResult<LcEntityInfoDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        ShardingResult<LcEntityInfoDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }
        //原始查询条件
        EntityInfoBatchQuery entityInfoBatchQuery = GsonUtil.json2Obj(jobShared.getExtParam(),
            EntityInfoBatchQuery.class);
        EntityInfoBatchQuery query = EntityInfoBatchQuery.builder()
            .offset(jobShared.getOffset())
            .limit(jobShared.getLimit())
            // .exchangeRateVersion(exchangeRateCore.getNewestVersion())
            .status(EnumLimitEntityStatus.UN_SETTLE.getCode())
            .amtLimitExRateRecalFlag(EnumReCalFlag.NOT.getCode())
            .entityId(entityInfoBatchQuery.getEntityId())
            .build();
        log.info("=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");
        List<LcEntityInfoDo> list = entityInfoBatchDao.selectShardList(query);
        shardingResult.setShardingResultList(list);
        return shardingResult;
    }



    @Override
    public void execJobCoreBusiness(ShardingResult<LcEntityInfoDo> shardingResult) {
        log.info("=========分片执行开始:[{}]===========", shardingResult.getJobShared()
            .getBatchNum());
        List<LcEntityInfoDo> lcEntityInfoDoList = shardingResult.getShardingResultList();
        if (CollectionUtils.isEmpty(lcEntityInfoDoList)) {
            log.info("=========分片执行结束:" + shardingResult.getJobShared()
                .getBatchNum() + "数量为空==========");
            return;
        }
        //获取所有实体编号
        List<String> entityIdList = lcEntityInfoDoList.stream()
            .map(LcEntityInfoDo::getEntityId)
            .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(entityIdList)) {
            return;
        }
        List<LcAmtLimitRecordSerialDo> serialDoList = lcAmtLimitRecordSerialDao.selectByExample(
            LcAmtLimitRecordSerialQuery.builder()
                .entityIdList(entityIdList)
                .status(EnumAmtLimitOperateStatus.SUCCESS.getCode())
                .operateTypeList(EnumReCalAmtLimitMap.allCodeList())
                .build());
        if (CollectionUtil.isEmpty(serialDoList)) {
            return;
        }
        List<String> amountIdList = serialDoList.stream()
            .map(LcAmtLimitRecordSerialDo::getOperateAmountId)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        //金额明细分组 key amountId
        // Map<String, List<AmtDetailBo>> amtGroup = iAmtDetailCore.queryGroup(amountIdList);
        // Table<String, String, List<LcAmtLimitRecordSerialDo>> table = group(serialDoList);
        // for (LcEntityInfoDo lcEntityInfoDo : lcEntityInfoDoList) {
        //     Map<String, List<LcAmtLimitRecordSerialDo>> map = table.row(lcEntityInfoDo.getEntityId());
        //     if (CollectionUtil.isEmpty(map)) {
        //         continue;
        //     }
        //     handle(map, lcEntityInfoDo, amtGroup);
        // }
        //更新分片流水成功
        normalUpdateSliceSerial(lcEntityInfoDoList.size(), shardingResult.getLcSliceBatchSerialDo());
        log.info("=========分片执行结束:[{}]数量为[{}]===========", shardingResult.getJobShared()
            .getBatchNum(), lcEntityInfoDoList.size());
    }

    /**
     * 金额计算
     *
     * @param serialDoList
     * @param currency
     * @param amtGroup
     * @return
     */
    // protected BigDecimal cal(List<LcAmtLimitRecordSerialDo> serialDoList, String currency,
    //     Map<String, List<AmtDetailBo>> amtGroup, Integer version) {
    //     BigDecimal calResult = BigDecimal.ZERO;
    //     if (CollectionUtil.isEmpty(serialDoList)) {
    //         return calResult;
    //     }
    //     for (LcAmtLimitRecordSerialDo operateSerialDo : serialDoList) {
    //         calResult = calResult.add(
    //             iAmtDetailCore.transferAmount(operateSerialDo.getOperateAmount(), operateSerialDo.getCurrency(),
    //                 operateSerialDo.getOperateAmountId(), currency, amtGroup.get(operateSerialDo.getOperateAmountId()),
    //                 version));
    //     }
    //     return calResult;
    // }

    /**
     * 流水分组
     *
     * @param serialDoList
     * @return
     */
    private Table<String, String, List<LcAmtLimitRecordSerialDo>> group(List<LcAmtLimitRecordSerialDo> serialDoList) {
        //entityId、ruleId、List<LcAmtLimitRecordSerialDo>
        Table<String, String, List<LcAmtLimitRecordSerialDo>> table = HashBasedTable.create();
        if (CollectionUtil.isEmpty(serialDoList)) {
            return table;
        }
        for (LcAmtLimitRecordSerialDo operateSerialDo : serialDoList) {
            List<LcAmtLimitRecordSerialDo> doList = table.get(operateSerialDo.getEntityId(),
                operateSerialDo.getRuleId());
            if (null == doList) {
                doList = Lists.newArrayList();
            }
            doList.add(operateSerialDo);
            table.put(operateSerialDo.getEntityId(), operateSerialDo.getRuleId(), doList);
        }
        return table;
    }

    /**
     * 额度操作分组
     *
     * @param serialDoList
     * @return
     */
    protected Map<EnumReCalFiled, List<LcAmtLimitRecordSerialDo>> groupCalFiledListMap(
        List<LcAmtLimitRecordSerialDo> serialDoList) {
        Map<EnumReCalFiled, List<LcAmtLimitRecordSerialDo>> map = Maps.newHashMap();
        if (CollectionUtil.isEmpty(serialDoList)) {
            return map;
        }
        for (LcAmtLimitRecordSerialDo operateSerialDo : serialDoList) {
            EnumReCalAmtLimitMap reCalFiledMap = EnumReCalAmtLimitMap.find(operateSerialDo.getOperateType());
            assert reCalFiledMap != null;
            List<LcAmtLimitRecordSerialDo> doList = map.get(reCalFiledMap.getReCalFiled());
            if (null == doList) {
                doList = Lists.newArrayList();
            }
            doList.add(operateSerialDo);
            map.put(reCalFiledMap.getReCalFiled(), doList);
        }
        return map;
    }

    /**
     * 处理同一实体下的限额重算
     *
     * @param serialMap
     * @param lcEntityInfoDo
     */
    // private void handle(Map<String, List<LcAmtLimitRecordSerialDo>> serialMap, LcEntityInfoDo lcEntityInfoDo,
    //     Map<String, List<AmtDetailBo>> amtGroup) {
    //     Date now = BusinessDateUtil.getDate();
    //     transactionTemplate.execute(transactionStatus -> {
    //         for (Map.Entry<String, List<LcAmtLimitRecordSerialDo>> entry : serialMap.entrySet()) {
    //             //获取限额规则
    //             AmtLimitRuleBo limitRuleBo = iAmtLimitRuleCore.getRule(entry.getKey());
    //             String recordId = entry.getValue()
    //                 .get(0)
    //                 .getRecordId();
    //             Integer shardSeq = entry.getValue()
    //                 .get(0)
    //                 .getShardSeq();
    //             Map<EnumReCalFiled, List<LcAmtLimitRecordSerialDo>> reCalFiledListMap = groupCalFiledListMap(
    //                 entry.getValue());
    //             BigDecimal realOldOperateAmount = cal(reCalFiledListMap.get(EnumReCalFiled.REAL),
    //                 limitRuleBo.getCurrency(), amtGroup, lcEntityInfoDo.getExchangeRateVersion());
    //             BigDecimal realNewOperateAmount = cal(reCalFiledListMap.get(EnumReCalFiled.REAL),
    //                 limitRuleBo.getCurrency(), amtGroup, getNewVersion(lcEntityInfoDo));
    //             BigDecimal preOldOperateAmount = cal(reCalFiledListMap.get(EnumReCalFiled.PRE),
    //                 limitRuleBo.getCurrency(), amtGroup, lcEntityInfoDo.getExchangeRateVersion());
    //             BigDecimal preNewOperateAmount = cal(reCalFiledListMap.get(EnumReCalFiled.PRE),
    //                 limitRuleBo.getCurrency(), amtGroup, getNewVersion(lcEntityInfoDo));
    //             //金额更新
    //             lcAmtLimitRecordDetailDao.occupyUpdate(recordId, shardSeq, realOldOperateAmount, realNewOperateAmount,
    //                 preOldOperateAmount, preNewOperateAmount, now);
    //         }
    //         LcEntityInfoDo updateDo = new LcEntityInfoDo();
    //         lcEntityInfoDo.setEntityId(lcEntityInfoDo.getEntityId());
    //         lcEntityInfoDo.setTenantId(AppParamUtil.getTenantId());
    //         lcEntityInfoDo.setAmtLimitExRateRecalFlag(EnumReCalFlag.DONE.getCode());
    //         if (null == lcEntityInfoDo.getNextExchangeRateVersion()) {
    //             lcEntityInfoDo.setNextExchangeRateVersion(exchangeRateCore.getNewestVersion());
    //         }
    //         lcEntityInfoDo.setUpdateTime(now);
    //         lcEntityInfoDao.updateBySelective(updateDo);
    //         return true;
    //     });
    // }

    /**
     * 获取汇率新版本
     *
     * @param lcEntityInfoDo
     * @return
     */
    private Integer getNewVersion(LcEntityInfoDo lcEntityInfoDo) {
        if (null == lcEntityInfoDo.getNextExchangeRateVersion()) {
            return null;
            // return exchangeRateCore.getNewestVersion();
        } else {
            return lcEntityInfoDo.getNextExchangeRateVersion();
        }
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        log.info("====================== 接入业务{}分片逻辑 start ================================================",
            getJobTrade().getDescription());
        List<JobShared> jobSharedList = new ArrayList<>();
        //sql 批处理数量 暂定为分片数量，不放大
        Integer batchFixNum = jobInitDto.getFixNum();
        //当前分组的最大值，为下次 批处理的最小值
        LcEntityInfoDo maxEntityInfoDo = new LcEntityInfoDo();
        //构造查询条件 查询当前 分批处理的 排序 最大 对象
        EntityInfoBatchQuery query = EntityInfoBatchQuery.builder()
            // .exchangeRateVersion(exchangeRateCore.getNewestVersion())
            .status(EnumLimitEntityStatus.UN_SETTLE.getCode())
            .amtLimitExRateRecalFlag(EnumReCalFlag.NOT.getCode())
            .offset(batchFixNum - 1)
            .limit(1)
            .build();
        //分片流水
        int batchNum = 0;
        while (maxEntityInfoDo != null) {
            query.setEntityId(maxEntityInfoDo.getEntityId());
            maxEntityInfoDo = entityInfoBatchDao.selectFirstOne(query);
            //统计分片 数量
            batchNum = countBatchNum(batchFixNum, query, maxEntityInfoDo, batchNum, jobInitDto, jobSharedList,
                query.getEntityId(), false);
        }
        log.info("======================= 接入业务{}分片逻辑 end ================================================",
            getJobTrade().getDescription());
        return jobSharedList;
    }
}
