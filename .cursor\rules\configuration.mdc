---
description: 应用配置和部署指南，包含启动类、配置文件和部署架构
alwaysApply: true
---
# 配置和部署指南

## 主应用程序

### 启动类
[LimitBatchDeployApplication.java](mdc:hsjry-limit-batch-deploy/src/main/java/com/hsjry/core/limit/batch/batch/deploy/LimitBatchDeployApplication.java) - Spring Boot主启动类
- 使用 `@EnableRpc` 开启RPC功能，扫描 `com.hsjry` 包
- 使用 `@SpringBootApplication` 扫描 `com.hsjry.core.limit.**` 和 `com.hsjry.base.common.**` 包
- 使用 `@EnableApolloConfig` 开启Apollo配置中心

### 配置类
- [LimitBatchDeployConfiguration.java](mdc:hsjry-limit-batch-deploy/src/main/java/com/hsjry/core/limit/batch/batch/deploy/LimitBatchDeployConfiguration.java) - 主要配置类
- [JmsConfig.java](mdc:hsjry-limit-batch-deploy/src/main/java/com/hsjry/core/limit/batch/batch/deploy/JmsConfig.java) - JMS消息队列配置

## 配置文件

### Bootstrap配置
[bootstrap.yml](mdc:hsjry-limit-batch-deploy/src/main/resources/bootstrap.yml) - Bootstrap配置文件，用于早期配置加载

### 日志配置
[logback-spring.xml](mdc:hsjry-limit-batch-deploy/src/main/resources/logback-spring.xml) - Logback日志配置

### Spring配置
[spring.factories](mdc:hsjry-limit-batch-deploy/src/main/resources/META-INF/spring.factories) - Spring自动配置文件

### 消息队列配置
[sccba_mq_consumer.xml](mdc:hsjry-limit-batch-deploy/src/main/resources/sccba_mq_consumer.xml) - SCCBA消息队列消费者配置

### 启动横幅
[banner.txt](mdc:hsjry-limit-batch-deploy/src/main/resources/banner.txt) - 应用启动时显示的横幅

## Starter模块

### Starter配置
`hsjry-limit-batch-starter` 模块提供了独立的启动器配置：
- [JmsConfig.java](mdc:hsjry-limit-batch-starter/src/main/java/com/hsjry/core/limit/batch/batch/deploy/JmsConfig.java) - JMS配置
- [spring.factories](mdc:hsjry-limit-batch-starter/src/main/resources/META-INF/spring.factories) - 自动配置文件

## 依赖管理

### 主POM
[pom.xml](mdc:pom.xml) - 项目根POM文件，定义了所有模块和版本管理
- 继承自 `hsjry-loan-starter-parent` 4.1.0-SNAPSHOT
- 使用 `revision` 属性管理版本号
- 包含所有子模块的定义

## 部署架构

项目采用分层部署架构：
1. **部署层** (`hsjry-limit-batch-deploy`) - 包含启动类和配置
2. **控制层** (`hsjry-limit-batch-controller`) - HTTP接口和RPC服务
3. **业务层** (`hsjry-limit-batch-biz-impl`) - 业务逻辑实现
4. **数据层** (`hsjry-limit-batch-dal-impl`) - 数据访问实现
5. **通用层** (`hsjry-limit-batch-common`) - 共享组件

## 外部依赖

- **Apollo配置中心** - 用于配置管理
- **RPC框架** - 用于服务间通信
- **JMS消息队列** - 用于异步消息处理
- **MyBatis** - 用于数据库访问
- **Spring Boot** - 应用框架

