<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hsjry.core.limit.batch.dal.dao.mapper.LbTCcsAcctMtMapper">
    <resultMap id="BaseResultMap" type="com.hsjry.core.limit.batch.dal.dao.model.LbTCcsAcctMtDo">
        <result property="balOrint" column="bal_orint" jdbcType="DECIMAL"/> <!-- 利息余额(未出账单组成) -->
        <result property="custNo" column="cust_no" jdbcType="VARCHAR"/> <!-- 客户编号 -->
        <result property="avlBal" column="avl_bal" jdbcType="DECIMAL"/> <!-- 可用余额 -->
        <result property="stmBalmp" column="stm_balmp" jdbcType="DECIMAL"/> <!-- 分期付款已出帐单余额 -->
        <result property="balMp" column="bal_mp" jdbcType="DECIMAL"/> <!-- 分期付款未出帐单余额 -->
        <result property="stmNoint" column="stm_noint" jdbcType="DECIMAL"/> <!-- 帐单免息余额 -->
        <result property="stmBalori" column="stm_balori" jdbcType="DECIMAL"/> <!-- 帐单利息余额 -->
        <result property="stmBalint" column="stm_balint" jdbcType="DECIMAL"/> <!-- 帐单日记息余额 -->
        <result property="stmBalfre" column="stm_balfre" jdbcType="DECIMAL"/> <!-- 帐单消费余额 -->
        <result property="mpRemPpl" column="mp_rem_ppl" jdbcType="DECIMAL"/> <!-- 分期付款目前剩余本金 -->
        <result property="xaccount" column="xaccount" jdbcType="BIGINT"/> <!-- 账号 -->
        <result property="balNoint" column="bal_noint" jdbcType="DECIMAL"/> <!-- 不记息余额(未出账单组成) -->
        <result property="balInt" column="bal_int" jdbcType="DECIMAL"/> <!-- 日记息余额(未出账单组成) -->
        <result property="balFree" column="bal_free" jdbcType="DECIMAL"/> <!-- 消费余额(未出账单组成) -->
        <result property="balCmpint" column="bal_cmpint" jdbcType="DECIMAL"/> <!-- 复利余额 -->
        <result property="credLimit" column="cred_limit" jdbcType="BIGINT"/> <!-- 帐户信用额度 -->
        <result property="accName1" column="acc_name1" jdbcType="VARCHAR"/> <!-- 帐户名称 -->
        <result property="custrNbr" column="custr_nbr" jdbcType="VARCHAR"/> <!-- 帐户拥有者证件号码 -->
        <result property="bank" column="bank" jdbcType="INTEGER"/> <!-- 银行 -->
    </resultMap>
    <sql id="Base_Column_List">
        bal_orint
        , cust_no
        , avl_bal
        , stm_balmp
        , bal_mp
        , stm_noint
        , stm_balori
        , stm_balint
        , stm_balfre
        , mp_rem_ppl
        , xaccount
        , bal_noint
        , bal_int
        , bal_free
        , bal_cmpint
        , cred_limit
        , acc_name1
        , custr_nbr
        , bank
    </sql>







</mapper>