package com.hsjry.core.limit.batch.biz.job.job.mid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * 信用卡额度信息处理Job
 * 负责调度信用卡额度信息处理任务
 *
 * <AUTHOR>
 * @date 2025-07-10 12:31:05
 */
@Slf4j
@Service("lbTCcsAcctLmtProcessJob")
public class LbTCcsAcctLmtProcessJob extends AbstractBaseBatchJob {
    public LbTCcsAcctLmtProcessJob() {
        log.info("LbTCcsAcctLmtProcessJob Bean初始化完成");
    }

    @Autowired
    @Qualifier("lbTCcsAcctLmtProcessBizImpl")
    private BaseOrdinaryBiz baseOrdinaryBiz;

    @Override
    public BaseOrdinaryBiz getBaseOrdinaryBiz() {
        return baseOrdinaryBiz;
    }
} 