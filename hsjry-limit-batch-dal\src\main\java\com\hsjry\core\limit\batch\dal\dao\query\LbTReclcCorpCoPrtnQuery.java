package com.hsjry.core.limit.batch.dal.dao.query;

import java.io.Serializable;

import lombok.Builder;
import lombok.Data;

/**
 * 额度中心-中间表-额度重算中对公客户中合作方额度查询条件
 *
 * <AUTHOR>
 * @date 2025-08-21 11:31:28
 */
@Data
@Builder
public class LbTReclcCorpCoPrtnQuery implements Serializable {

    /** serialVersionUID */
    private static final long serialVersionUID = 1958492141684719622L;

    /** 客户编号 */
    private String custNo;
    /** 额度编号 */
    private String custLimitId;
    /** 模板节点编号 */
    private String templateNodeId;
    /** 额度状态 */
    private String limitStatus;
    /** 总额度 */
    private java.math.BigDecimal totalAmount;
    /** 预占额度 */
    private java.math.BigDecimal preOccupyAmount;
    /** 实占额度 */
    private java.math.BigDecimal realOccupyAmount;
    /** 总低风险额度 */
    private java.math.BigDecimal lowRiskAmount;
    /** 预占低风险 */
    private java.math.BigDecimal preOccupyLowRiskAmt;
    /** 实占低风险 */
    private java.math.BigDecimal realOccupyLowRiskAmt;
}
