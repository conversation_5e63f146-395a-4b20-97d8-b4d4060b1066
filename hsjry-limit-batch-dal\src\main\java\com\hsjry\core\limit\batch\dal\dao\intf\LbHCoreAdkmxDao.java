package com.hsjry.core.limit.batch.dal.dao.intf;

import com.hsjry.core.limit.batch.dal.dao.model.LbHCoreAdkmxDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbHCoreAdkmxQuery;

import java.util.List;

import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 核心系统贷款账户交易明细表-历史表（存储贷款账户交易明细历史数据）数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbHCoreAdkmxDao extends IBaseDao<LbHCoreAdkmxDo> {
    /**
     * 分页查询核心系统贷款账户交易明细表-历史表（存储贷款账户交易明细历史数据）
     *
     * @param lbHCoreAdkmxQuery 条件
     * @return PageInfo<LbHCoreAdkmxDo>
     */
    PageInfo<LbHCoreAdkmxDo> selectPage(LbHCoreAdkmxQuery lbHCoreAdkmxQuery, PageParam pageParam);

    /**
     * 根据key查询核心系统贷款账户交易明细表-历史表（存储贷款账户交易明细历史数据）
     *
     * @param faredm
     * @param dkjeju
     * @param mxxhao
     * @param dataDate
     * @return
     */
    LbHCoreAdkmxDo selectByKey(String faredm, String dkjeju, java.math.BigDecimal mxxhao, String dataDate);

    /**
     * 根据key删除核心系统贷款账户交易明细表-历史表（存储贷款账户交易明细历史数据）
     *
     * @param faredm
     * @param dkjeju
     * @param mxxhao
     * @param dataDate
     * @return
     */
    int deleteByKey(String faredm, String dkjeju, java.math.BigDecimal mxxhao, String dataDate);

    /**
     * 查询核心系统贷款账户交易明细表-历史表（存储贷款账户交易明细历史数据）信息
     *
     * @param lbHCoreAdkmxQuery 条件
     * @return List<LbHCoreAdkmxDo>
     */
    List<LbHCoreAdkmxDo> selectByExample(LbHCoreAdkmxQuery lbHCoreAdkmxQuery);

    /**
     * 新增核心系统贷款账户交易明细表-历史表（存储贷款账户交易明细历史数据）信息
     *
     * @param lbHCoreAdkmx 条件
     * @return int>
     */
    int insertBySelective(LbHCoreAdkmxDo lbHCoreAdkmx);

    /**
     * 修改核心系统贷款账户交易明细表-历史表（存储贷款账户交易明细历史数据）信息
     *
     * @param lbHCoreAdkmx
     * @return
     */
    int updateBySelective(LbHCoreAdkmxDo lbHCoreAdkmx);

    /**
     * 修改核心系统贷款账户交易明细表-历史表（存储贷款账户交易明细历史数据）信息
     *
     * @param lbHCoreAdkmx
     * @param lbHCoreAdkmxQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbHCoreAdkmxDo lbHCoreAdkmx, LbHCoreAdkmxQuery lbHCoreAdkmxQuery);

    int deleteByDataDate(String dataDate);
}
