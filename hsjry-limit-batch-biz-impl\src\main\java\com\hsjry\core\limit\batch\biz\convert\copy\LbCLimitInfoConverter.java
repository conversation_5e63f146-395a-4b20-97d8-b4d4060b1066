package com.hsjry.core.limit.batch.biz.convert.copy;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.hsjry.core.limit.batch.common.dto.file.LbCLimitInfoDto;
import com.hsjry.core.limit.batch.dal.dao.model.LbCLimitInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoDo;
import com.hsjry.lang.common.utils.DateUtil;
import com.hsjry.lang.common.utils.StringUtil;

/**
 * 额度实例信息转换器
 *
 * <AUTHOR>
 * @version V3.0.5
 * @since 2025/7/510:32
 */
public class LbCLimitInfoConverter {

    /**
     * DTO转DO
     *
     * @param dto DTO对象
     * @return DO对象
     */
    public static LbCLimitInfoDo dtoToDo(LbCLimitInfoDto dto) {
        return LbCLimitInfoCnvs.INSTANCE.dtoToDo(dto);
    }

    /**
     * DO转DTO
     *
     * @param model DO对象
     * @return DTO对象
     */
    public static LbCLimitInfoDto doToDto(LbCLimitInfoDo model) {
        return LbCLimitInfoCnvs.INSTANCE.do2Dto(model);
    }

    /**
     * DTO列表转DO列表
     *
     * @param dtoList DTO列表
     * @return DO列表
     */
    public List<LbCLimitInfoDo> dtoListToDoList(List<LbCLimitInfoDto> dtoList) {
        if (null == dtoList) {
            return Collections.emptyList();
        }
        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (dtoList.size() / 0.75f) + 1, 16);
        return dtoList.parallelStream().map(LbCLimitInfoConverter::dtoToDo)//
            .collect(Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
    }

    /**
     * DO列表转DTO列表
     *
     * @param doList DO列表
     * @return DTO列表
     */
    public List<LbCLimitInfoDto> doListToDtoList(List<LbCLimitInfoDo> doList) {
        if (null == doList) {
            return Collections.emptyList();
        }
        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (doList.size() / 0.75f) + 1, 16);
        return doList.parallelStream().map(LbCLimitInfoConverter::doToDto)//
            .collect(Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
    }

    /**
     * 从字符串数组创建DTO (根据CSV文件头字段顺序)
     *
     * @param fields 字段数组
     * @return DTO对象
     */
    public LbCLimitInfoDto fieldsToDto(String[] fields) {
        if (fields == null || fields.length == 0) {
            return null;
        }

        LbCLimitInfoDto dto = new LbCLimitInfoDto();

        // 根据CSV文件头字段顺序映射
        dto.setCustLimitId(trimToNull(fields[0]));
        dto.setLimitStatus(trimToNull(fields[1]));
        dto.setOperatorId(trimToNull(fields[2]));
        dto.setOwnOrganId(trimToNull(fields[3]));
        dto.setEffectiveStartTime(parseDate(fields[4]));
        dto.setEffectiveEndTime(parseDate(fields[5]));
        dto.setLimitTerm(parseInteger(fields[6]));
        dto.setLimitTermUnit(trimToNull(fields[7]));
        dto.setLimitGraceTerm(parseInteger(fields[8]));
        dto.setLimitGraceTermUnit(trimToNull(fields[9]));
        dto.setLimitLastTime(parseDate(fields[10]));
        dto.setLimitApprovalDate(parseDate(fields[11]));
        dto.setLimitEnableEndTime(parseDate(fields[12]));
        dto.setLimitEnableTerm(parseInteger(fields[13]));
        dto.setLimitEnableTermUnit(trimToNull(fields[14]));
        dto.setOutCustLimitId(trimToNull(fields[15]));
        dto.setCustLimitTypeId(trimToNull(fields[16]));
        dto.setTemplateNodeId(trimToNull(fields[17]));
        dto.setLimitTemplateId(trimToNull(fields[18]));
        dto.setLimitObjectId(trimToNull(fields[19]));
        dto.setLimitCoreObjectId(trimToNull(fields[20]));
        dto.setLimitObjectType(trimToNull(fields[21]));
        dto.setUseOccupyTimes(parseInteger(fields[22]));
        dto.setOccupyTimesLimit(parseInteger(fields[23]));
        dto.setProductId(trimToNull(fields[24]));
        dto.setLimitLevel(trimToNull(fields[25]));
        dto.setLimitClassification(trimToNull(fields[26]));
        dto.setLimitUsageType(trimToNull(fields[27]));
        dto.setLimitGrantType(trimToNull(fields[28]));
        dto.setLimitOccupationType(trimToNull(fields[29]));
        dto.setUpFlag(trimToNull(fields[30]));
        dto.setContractLimitFlag(trimToNull(fields[31]));
        dto.setSoleFlag(trimToNull(fields[32]));
        dto.setExchangeRateVersion(parseInteger(fields[33]));
        dto.setContractRecalFlag(trimToNull(fields[34]));
        dto.setSeq(parseInteger(fields[35]));
        dto.setExcessOccupationType(trimToNull(fields[36]));
        dto.setInstId(trimToNull(fields[37]));
        dto.setRelationId(trimToNull(fields[38]));
        dto.setBizLine(trimToNull(fields[39]));
        dto.setSupportSharedFlag(trimToNull(fields[40]));
        dto.setSupportShareFlag(trimToNull(fields[41]));
        dto.setVirtualContractFlag(trimToNull(fields[42]));
        dto.setHappenType(safeGet(fields, 43));
        dto.setRegroupTag(safeGet(fields, 44));
        dto.setMainGuarType(safeGet(fields, 45));
        dto.setFtmDwnAvlDt(parseDate(safeGet(fields, 46)));
        dto.setBlngLglpsnCoreInsNo(safeGet(fields, 47));
        dto.setBlngLglpsnCoreInsNm(safeGet(fields, 48));

        return dto;
    }

    /**
     * 安全获取数组元素
     */
    private String safeGet(String[] fields, int index) {
        if (fields.length > index) {
            return trimToNull(fields[index]);
        }
        return null;
    }

    /**
     * 字符串去空格并转换为null
     */
    private String trimToNull(String str) {
        return StringUtil.trimToNull(str);
    }

    /**
     * 解析日期字符串
     */
    private Date parseDate(String dateStr) {
        if (StringUtil.isBlank(dateStr)) {
            return null;
        }
        try {
            // 尝试多种日期格式
            if (dateStr.length() == 8) {
                return DateUtil.parseDate(dateStr, "yyyyMMdd");
            } else if (dateStr.length() == 14) {
                return DateUtil.parseDate(dateStr, "yyyyMMddHHmmss");
            } else if (dateStr.contains("-")) {
                return DateUtil.parseDate(dateStr, "yyyy-MM-dd");
            } else if (dateStr.contains("/")) {
                return DateUtil.parseDate(dateStr, "yyyy/MM/dd");
            }
        } catch (Exception e) {
            // 日期解析失败，返回null
        }
        return null;
    }

    /**
     * Integer
     */
    private Integer parseInteger(String numStr) {
        if (StringUtil.isBlank(numStr)) {
            return null;
        }
        try {
            return Integer.valueOf(numStr.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * LcCustLimitInfoDo转LbCLimitInfoDo
     *
     * @param model 源DO对象
     * @return 目标DO对象
     */
    public static LbCLimitInfoDo do2Copy(LcCustLimitInfoDo model) {
        return LbCLimitInfoCnvs.INSTANCE.do2Copy(model);
    }

    /**
     * LcCustLimitInfoDo列表转LbCLimitInfoDo列表
     *
     * @param doList 源DO列表
     * @return 目标DO列表
     */
    public static List<LbCLimitInfoDo> doList2CopyList(List<LcCustLimitInfoDo> doList) {
        if (null == doList) {
            return Collections.emptyList();
        }
        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (doList.size() / 0.75f) + 1, 16);
        return doList.parallelStream().map(LbCLimitInfoConverter::do2Copy)//
            .collect(Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
    }

}
