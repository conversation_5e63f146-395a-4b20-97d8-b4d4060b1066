/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.entity;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 核心贴现票据信息文件数据实体
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/11 11:41
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LbSCoreBtxpjData {

    /** 法人代码 */
    private String faredm;

    /** 贴现借据号 */
    private String txnjjh;

    /** 票据包号 */
    private String piojzh;

    /** 贴现协议编号 */
    private String txxybh;

    /** 票据种类 */
    private String piojzl;

    /** 是否电子票据 */
    private String sfdzpj;

    /** 票据号码 */
    private String piaojh;

    /** 票面金额 */
    private BigDecimal piomje;

    /** 票据签发日 */
    private String pjqfrq;

    /** 票据到期日 */
    private String pjdqrq;

    /** 调整天数 */
    private Integer tiozts;

    /** 出票人全称 */
    private String chprqc;

    /** 收款人全称 */
    private String skrnmc;

    /** 承兑行行号 */
    private String chdhhh;

    /** 承兑行行名 */
    private String fukhmc;

    /** 承兑行种类 */
    private String chdhzl;

    /** 柜员流水号 */
    private String guiyls;

    /** 交易日期 */
    private String jioyrq;

    /** 交易柜员 */
    private String jioygy;

    /** 维护日期 */
    private String weihrq;

    /** 维护柜员 */
    private String weihgy;

    /** 维护机构 */
    private String weihjg;

    /** 维护时间 */
    private Integer weihsj;

    /** 时间戳 */
    private Long shjnch;

    /** 记录状态 */
    private String jiluzt;
}
