package com.hsjry.core.limit.batch.dal.dao.intf;

import com.hsjry.core.limit.batch.dal.dao.model.LbSCoreAdkzhDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSCoreAdkzhQuery;

import java.util.List;

import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 核心系统-历史表-贷款账户主数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbSCoreAdkzhDao extends IBaseDao<LbSCoreAdkzhDo> {
    /**
     * 分页查询核心系统-历史表-贷款账户主
     *
     * @param lbSCoreAdkzhQuery 条件
     * @return PageInfo<LbSCoreAdkzhDo>
     */
    PageInfo<LbSCoreAdkzhDo> selectPage(LbSCoreAdkzhQuery lbSCoreAdkzhQuery, PageParam pageParam);

    /**
     * 根据key查询核心系统-历史表-贷款账户主
     *
     * @param faredm
     * @param daikzh
     * @return
     */
    LbSCoreAdkzhDo selectByKey(String faredm, String daikzh);

    /**
     * 根据key删除核心系统-历史表-贷款账户主
     *
     * @param faredm
     * @param daikzh
     * @return
     */
    int deleteByKey(String faredm, String daikzh);

    /**
     * 查询核心系统-历史表-贷款账户主信息
     *
     * @param lbSCoreAdkzhQuery 条件
     * @return List<LbSCoreAdkzhDo>
     */
    List<LbSCoreAdkzhDo> selectByExample(LbSCoreAdkzhQuery lbSCoreAdkzhQuery);

    /**
     * 新增核心系统-历史表-贷款账户主信息
     *
     * @param lbSCoreAdkzh 条件
     * @return int>
     */
    int insertBySelective(LbSCoreAdkzhDo lbSCoreAdkzh);

    /**
     * 修改核心系统-历史表-贷款账户主信息
     *
     * @param lbSCoreAdkzh
     * @return
     */
    int updateBySelective(LbSCoreAdkzhDo lbSCoreAdkzh);

    /**
     * 修改核心系统-历史表-贷款账户主信息
     *
     * @param lbSCoreAdkzh
     * @param lbSCoreAdkzhQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbSCoreAdkzhDo lbSCoreAdkzh, LbSCoreAdkzhQuery lbSCoreAdkzhQuery);

    /**
     * 批量插入核心系统-落地表-贷款账户主信息
     *
     * @param lbSCoreAdkzhList 批量数据
     * @return int
     */
    int insertList(List<LbSCoreAdkzhDo> lbSCoreAdkzhList);

    /**
     * 清空核心系统-落地表-贷款账户主所有数据
     *
     * @return int
     */
    int deleteAll();
}
