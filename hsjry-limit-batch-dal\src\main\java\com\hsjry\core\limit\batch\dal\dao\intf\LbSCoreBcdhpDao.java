package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbSCoreBcdhpDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSCoreBcdhpQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 银行承兑汇票核心业务数据表-落地表数据库操作接口
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/22 10:00
 */
public interface LbSCoreBcdhpDao extends IBaseDao<LbSCoreBcdhpDo> {
    /**
     * 分页查询银行承兑汇票核心业务数据表-落地表
     *
     * @param lbSCoreBcdhpQuery 条件
     * @return PageInfo<LbSCoreBcdhpDo>
     */
    PageInfo<LbSCoreBcdhpDo> selectPage(LbSCoreBcdhpQuery lbSCoreBcdhpQuery, PageParam pageParam);

    /**
     * 根据key删除银行承兑汇票核心业务数据表-落地表
     *
     * @param faredm 法人代码
     * @param cdxybh 银承协议编号
     * @param bccppc 本次出票批次
     * @param chupbh 本次出票编号
     * @return
     */
    int deleteByKey(String faredm, String cdxybh, java.math.BigDecimal bccppc, String chupbh);

    LbSCoreBcdhpDo selectByKey(String faredm, String cdxybh, java.math.BigDecimal bccppc, String chupbh);

    /**
     * 查询银行承兑汇票核心业务数据表-落地表信息
     *
     * @param lbSCoreBcdhpQuery 条件
     * @return List<LbSCoreBcdhpDo>
     */
    List<LbSCoreBcdhpDo> selectByExample(LbSCoreBcdhpQuery lbSCoreBcdhpQuery);

    /**
     * 新增银行承兑汇票核心业务数据表-落地表信息
     *
     * @param lbSCoreBcdhp 条件
     * @return int>
     */
    int insertBySelective(LbSCoreBcdhpDo lbSCoreBcdhp);

    /**
     * 修改银行承兑汇票核心业务数据表-落地表信息
     *
     * @param lbSCoreBcdhp
     * @return
     */
    int updateBySelective(LbSCoreBcdhpDo lbSCoreBcdhp);

    /**
     * 修改银行承兑汇票核心业务数据表-落地表信息
     *
     * @param lbSCoreBcdhp
     * @param lbSCoreBcdhpQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbSCoreBcdhpDo lbSCoreBcdhp, LbSCoreBcdhpQuery lbSCoreBcdhpQuery);

    /**
     * 批量插入银行承兑汇票核心业务数据表-落地表信息
     *
     * @param lbSCoreBcdhpList 批量数据
     * @return int
     */
    @Override
    int insertList(List<LbSCoreBcdhpDo> lbSCoreBcdhpList);

    /**
     * 清空银行承兑汇票核心业务数据表-落地表所有数据
     *
     * @return int
     */
    int deleteAll();

}
