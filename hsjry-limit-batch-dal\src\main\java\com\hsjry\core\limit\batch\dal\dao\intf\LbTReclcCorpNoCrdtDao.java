package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpNoCrdtDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcCorpNoCrdtQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中对公客户中非授信额度数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-08-21 11:31:28
 */
public interface LbTReclcCorpNoCrdtDao extends IBaseDao<LbTReclcCorpNoCrdtDo> {
    /**
     * 分页查询额度中心-中间表-额度重算中对公客户中非授信额度
     *
     * @param lbTReclcCorpNoCrdtQuery 条件
     * @return PageInfo<LbTReclcCorpNoCrdtDo>
     */
    PageInfo<LbTReclcCorpNoCrdtDo> selectPage(LbTReclcCorpNoCrdtQuery lbTReclcCorpNoCrdtQuery, PageParam pageParam);

    /**
     * 根据key查询额度中心-中间表-额度重算中对公客户中非授信额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    LbTReclcCorpNoCrdtDo selectByKey(String custNo, String custLimitId);

    /**
     * 根据key删除额度中心-中间表-额度重算中对公客户中非授信额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custNo, String custLimitId);

    /**
     * 查询额度中心-中间表-额度重算中对公客户中非授信额度信息
     *
     * @param lbTReclcCorpNoCrdtQuery 条件
     * @return List<LbTReclcCorpNoCrdtDo>
     */
    List<LbTReclcCorpNoCrdtDo> selectByExample(LbTReclcCorpNoCrdtQuery lbTReclcCorpNoCrdtQuery);

    /**
     * 新增额度中心-中间表-额度重算中对公客户中非授信额度信息
     *
     * @param lbTReclcCorpNoCrdt 条件
     * @return int>
     */
    int insertBySelective(LbTReclcCorpNoCrdtDo lbTReclcCorpNoCrdt);

    /**
     * 修改额度中心-中间表-额度重算中对公客户中非授信额度信息
     *
     * @param lbTReclcCorpNoCrdt
     * @return
     */
    int updateBySelective(LbTReclcCorpNoCrdtDo lbTReclcCorpNoCrdt);

    /**
     * 修改额度中心-中间表-额度重算中对公客户中非授信额度信息
     *
     * @param lbTReclcCorpNoCrdt
     * @param lbTReclcCorpNoCrdtQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTReclcCorpNoCrdtDo lbTReclcCorpNoCrdt,
        LbTReclcCorpNoCrdtQuery lbTReclcCorpNoCrdtQuery);

    // ==================== 非授信额度重算相关方法 ====================

    /**
     * 6.1.清空非授信额度中间表
     */
    int truncateNoCreditLimit();

    /**
     * 6.2.插入非授信额度客户编号和额度编号
     */
    int insertNoCreditLimit();

    /**
     * 6.3.更新非授信额度中间表金额信息
     */
    int mergeNoCreditLimitAmount();

    /**
     * 6.4.更新额度实例金额信息
     */
    int mergeNoCreditLimitInstance();
}
