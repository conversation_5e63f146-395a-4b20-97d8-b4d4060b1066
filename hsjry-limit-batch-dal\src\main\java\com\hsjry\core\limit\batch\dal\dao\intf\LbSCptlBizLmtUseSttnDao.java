package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbSCptlBizLmtUseSttnDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSCptlBizLmtUseSttnQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 资金日终业务额度使用情况同步表（记录客户额度使用情况）数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbSCptlBizLmtUseSttnDao extends IBaseDao<LbSCptlBizLmtUseSttnDo> {
    /**
     * 分页查询资金日终业务额度使用情况同步表（记录客户额度使用情况）
     *
     * @param lbSCptlBizLmtUseSttnQuery 条件
     * @return PageInfo<LbSCptlBizLmtUseSttnDo>
     */
    PageInfo<LbSCptlBizLmtUseSttnDo> selectPage(LbSCptlBizLmtUseSttnQuery lbSCptlBizLmtUseSttnQuery,
        PageParam pageParam);

    /**
     * 根据key查询资金日终业务额度使用情况同步表（记录客户额度使用情况）
     *
     * @param userId
     * @return
     */
    LbSCptlBizLmtUseSttnDo selectByKey(String userId);

    /**
     * 根据key删除资金日终业务额度使用情况同步表（记录客户额度使用情况）
     *
     * @param userId
     * @return
     */
    int deleteByKey(String userId);

    /**
     * 查询资金日终业务额度使用情况同步表（记录客户额度使用情况）信息
     *
     * @param lbSCptlBizLmtUseSttnQuery 条件
     * @return List<LbSCptlBizLmtUseSttnDo>
     */
    List<LbSCptlBizLmtUseSttnDo> selectByExample(LbSCptlBizLmtUseSttnQuery lbSCptlBizLmtUseSttnQuery);

    /**
     * 新增资金日终业务额度使用情况同步表（记录客户额度使用情况）信息
     *
     * @param lbSCptlBizLmtUseSttn 条件
     * @return int>
     */
    int insertBySelective(LbSCptlBizLmtUseSttnDo lbSCptlBizLmtUseSttn);

    /**
     * 修改资金日终业务额度使用情况同步表（记录客户额度使用情况）信息
     *
     * @param lbSCptlBizLmtUseSttn
     * @return
     */
    int updateBySelective(LbSCptlBizLmtUseSttnDo lbSCptlBizLmtUseSttn);

    /**
     * 修改资金日终业务额度使用情况同步表（记录客户额度使用情况）信息
     *
     * @param lbSCptlBizLmtUseSttn
     * @param lbSCptlBizLmtUseSttnQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbSCptlBizLmtUseSttnDo lbSCptlBizLmtUseSttn,
        LbSCptlBizLmtUseSttnQuery lbSCptlBizLmtUseSttnQuery);

    /**
     * 删除所有记录
     * 用于文件同步前清空目标表
     *
     * @return 删除的记录数
     */
    int deleteAll();

    /**
     * 批量插入资金日终业务额度使用情况同步表记录
     * 用于文件同步批量数据插入
     *
     * @param list 要插入的记录列表
     * @return 插入的记录数
     */
    int batchInsert(List<LbSCptlBizLmtUseSttnDo> list);

    /**
     * 获取第一个对象，用于分片查询
     * 根据userId主键排序，获取指定偏移量的第一条记录
     *
     * @param query 查询条件
     * @return 第一条记录，如果没有则返回null
     */
    LbSCptlBizLmtUseSttnDo selectFirstOne(LbSCptlBizLmtUseSttnQuery query);

    /**
     * 查询当前分片主键范围内的数据总数
     * 根据userId主键范围统计当前分片的数据量
     *
     * @param query 查询条件，需包含userId范围
     * @return 当前分片的数据量
     */
    Integer selectCountByCurrentGroup(LbSCptlBizLmtUseSttnQuery query);

    /**
     * 查询分片数据列表
     * 支持offset/limit分页查询
     *
     * @param query 查询条件，包含offset和limit
     * @return 分片数据列表
     */
    List<LbSCptlBizLmtUseSttnDo> selectShardList(LbSCptlBizLmtUseSttnQuery query);
}
