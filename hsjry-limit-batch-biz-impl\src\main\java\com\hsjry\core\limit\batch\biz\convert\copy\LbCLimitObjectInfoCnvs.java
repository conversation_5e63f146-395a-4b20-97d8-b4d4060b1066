package com.hsjry.core.limit.batch.biz.convert.copy;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.hsjry.core.limit.batch.common.dto.file.LbCLimitObjectInfoDto;
import com.hsjry.core.limit.batch.dal.dao.model.LbCLimitObjectInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitObjectInfoDo;

/**
 * 额度实例所属对象信息转换器
 *
 * <AUTHOR>
 * @date 2025/7/10
 */
@Mapper(componentModel = "spring")
public interface LbCLimitObjectInfoCnvs {

    LbCLimitObjectInfoCnvs INSTANCE = Mappers.getMapper(LbCLimitObjectInfoCnvs.class);

    LbCLimitObjectInfoDo do2Copy(LcCustLimitObjectInfoDo model);

    LbCLimitObjectInfoDo dtoToDo(LbCLimitObjectInfoDto dto);

    LbCLimitObjectInfoDto do2Dto(LbCLimitObjectInfoDo model);
}
