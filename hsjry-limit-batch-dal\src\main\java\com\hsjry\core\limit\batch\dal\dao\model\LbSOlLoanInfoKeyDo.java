package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 网贷系统-落地表-借据信息（记录客户借款信息）主键
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
@Table(name = "lb_s_ol_loan_info")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LbSOlLoanInfoKeyDo implements Serializable {

    private static final long serialVersionUID = 1942415996337979398L;
    /** 支用申请编号 */
    @Id
    @Column(name = "loan_apply_id")
    private String loanApplyId;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
}