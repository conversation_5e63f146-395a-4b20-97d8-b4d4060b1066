/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.core.bo;

import java.util.Date;

import org.springframework.beans.factory.annotation.Value;

import com.hsjry.base.common.job.biz.file.FilePropertiesSkip;
import com.hsjry.base.common.job.dto.BaseFile;
import com.hsjry.base.common.job.dto.IEnumFileType;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.common.enums.EnumLimitFileType;
import com.hsjry.core.limit.batch.core.IDefinePathCore;
import com.hsjry.lang.common.utils.SpringContextUtil;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/4/12 15:51
 */
@Data
@Builder
public class InboundSerialFileBo implements BaseFile {

    /** 前置流水 */
    private String inboundSerial;
    /** 业务流水 */
    private String serialNo;
    /** 交易码 */
    private String tradeCode;
    /** 交易状态 */
    private String tradeStatus;
    /** 租户号 */
    private String tenantId;

    @Override
    public String getHead() {
        return "INBOUND_SERIAL,SERIAL_NO,TRADE_CODE,TRADE_STATUS,TENANT_ID" + System.lineSeparator();
    }

    @Override
    public IEnumFileType getFileType() {
        return EnumLimitFileType.LIMIT_RECONCILE_FILE;
    }

    @Override
    public String getLocalPath(Date accountDate, String tenantId) {
        return SpringContextUtil.getBean(IDefinePathCore.class)
            .getLocalPath(EnumJobTrade.INBOUND_FILE, tenantId, accountDate);
    }

    @Override
    public String getRemotePath(Date accountDate, String tenantId) {
        return SpringContextUtil.getBean(IDefinePathCore.class)
            .getRemotePath(EnumJobTrade.INBOUND_FILE, tenantId, accountDate);
    }
}
