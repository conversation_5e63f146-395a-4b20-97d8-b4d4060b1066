/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.entity;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 核心产品文件数据实体
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/11/7 10:28
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LbSCorePprodData {

    /** 法人代码 */
    private String faredm;

    /** 产品编号 */
    private String chapbh;

    /** 产品描述 */
    private String chapmx;

    /** 模块 */
    private String module;

    /** 维护日期 */
    private String weihrq;

    /** 维护时间 */
    private BigDecimal weihsj;

    /** 维护工号 */
    private String weihgy;

    /** 维护机构 */
    private String weihjg;

    /** 行ID */
    private String rowidd;

    /** 时间戳 */
    private BigDecimal shjnch;

    /** 记录状态 */
    private String jiluzt;
} 