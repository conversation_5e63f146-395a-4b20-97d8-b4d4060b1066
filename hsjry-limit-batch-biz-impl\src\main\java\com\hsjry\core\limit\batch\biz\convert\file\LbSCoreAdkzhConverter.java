package com.hsjry.core.limit.batch.biz.convert.file;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.hsjry.core.limit.batch.biz.entity.LbSCoreAdkzhData;
import com.hsjry.core.limit.batch.dal.dao.model.LbSCoreAdkzhDo;

import lombok.extern.slf4j.Slf4j;

/**
 * 核心系统-落地表-贷款账户主表转换器
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/21 11:00
 */
@Slf4j
@Component
public class LbSCoreAdkzhConverter {

    /** 默认初始容量 */
    private static final int DEFAULT_INITIAL_CAPACITY = 16;
    /** 负载因子 */
    private static final float LOAD_FACTOR = 0.75f;

    /**
     * Data转DO
     *
     * @param data 源数据对象
     * @return 目标DO对象
     */
    public static LbSCoreAdkzhDo data2Do(LbSCoreAdkzhData data) {
        return LbSCoreAdkzhCnvs.INSTANCE.data2Do(data);
    }

    /**
     * DO转Data
     *
     * @param dataObject DO对象
     * @return 目标Data对象
     */
    public static LbSCoreAdkzhData do2Data(LbSCoreAdkzhDo dataObject) {
        return LbSCoreAdkzhCnvs.INSTANCE.do2Data(dataObject);
    }

    /**
     * Data列表转DO列表
     *
     * @param dataList 源数据列表
     * @return 目标DO列表
     */
    public List<LbSCoreAdkzhDo> dataListToDoList(List<LbSCoreAdkzhData> dataList) {
        if (Objects.isNull(dataList)) {
            return Collections.emptyList();
        }
        
        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (dataList.size() / LOAD_FACTOR) + 1, DEFAULT_INITIAL_CAPACITY);
        return dataList.parallelStream().filter(Objects::nonNull).map(LbSCoreAdkzhConverter::data2Do).collect(
            Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
    }
} 