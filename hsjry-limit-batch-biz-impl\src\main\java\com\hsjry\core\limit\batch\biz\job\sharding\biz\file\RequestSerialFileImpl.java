/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.sharding.biz.file;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;
import com.hsjry.base.common.job.biz.file.FileWriteSharedBiz;
import com.hsjry.base.common.job.biz.file.IFileBaseBuilder;
import com.hsjry.base.common.job.dto.BaseFile;
import com.hsjry.base.common.job.dto.FileParam;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.job.util.JobUtil;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.limit.AbstractShardingPrepareBiz;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.core.bo.InboundSerialFileBo;
import com.hsjry.core.limit.center.dal.dao.intf.InboundSerialBatchDao;
import com.hsjry.core.limit.center.dal.dao.model.LcInboundSerialDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.InboundSerialBatchQuery;
import com.hsjry.lang.common.utils.DateUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.extern.slf4j.Slf4j;
/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/4/7 9:51
 */
@Service
@Slf4j
public class RequestSerialFileImpl extends AbstractShardingPrepareBiz<InboundSerialBatchQuery>
    implements JobCoreBusiness<LcInboundSerialDo>, IFileBaseBuilder {
    @Autowired
    private InboundSerialBatchDao inboundSerialBatchDao;
    @Autowired
    private FileWriteSharedBiz fileWriteSharedBiz;

    @Override
    public Integer selectCountByCurrentGroupFromDb(InboundSerialBatchQuery query) {
        return inboundSerialBatchDao.selectCountByCurrentGroup(query);
    }

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.INBOUND_FILE;
    }

    @Override
    public ShardingResult<LcInboundSerialDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        ShardingResult<LcInboundSerialDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }
        //原始查询条件
        InboundSerialBatchQuery inboundSerialBatchQuery = GsonUtil.json2Obj(jobShared.getExtParam(),
            InboundSerialBatchQuery.class);
        InboundSerialBatchQuery query = InboundSerialBatchQuery.builder()
            .offset(jobShared.getOffset())
            .limit(jobShared.getLimit())
            .bizDatetimeBegin(inboundSerialBatchQuery.getBizDatetimeBegin())
            .bizDatetimeEnd(inboundSerialBatchQuery.getBizDatetimeEnd())
            .cisSerialNo(inboundSerialBatchQuery.getCisSerialNo())
            .build();
        log.info("=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");
        List<LcInboundSerialDo> list = inboundSerialBatchDao.selectShardList(query);
        shardingResult.setShardingResultList(list);
        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LcInboundSerialDo> shardingResult) {
        log.info("=========分片执行开始:[{}]===========", shardingResult.getJobShared()
            .getBatchNum());
        List<LcInboundSerialDo> lcInboundSerialDoList = shardingResult.getShardingResultList();
        if (CollectionUtils.isEmpty(lcInboundSerialDoList)) {
            log.info("=========分片执行结束:" + shardingResult.getJobShared()
                .getBatchNum() + "数量为空===========");
            //更新分片流水成功
            normalUpdateSliceSerial(0, shardingResult.getLcSliceBatchSerialDo());
            fileWriteSharedBiz.writeShardFile(FileParam.<LcInboundSerialDo>builder()
                .dataList(Lists.newArrayList())
                .jobShared(shardingResult.getJobShared())
                .tradeCode(getJobTrade())
                .fileType(getBaseFile().getFileType())
                .build(), this);
            return;
        }
        fileWriteSharedBiz.writeShardFile(FileParam.<LcInboundSerialDo>builder()
            .dataList(lcInboundSerialDoList)
            .jobShared(shardingResult.getJobShared())
            .tradeCode(getJobTrade())
            .fileType(getBaseFile().getFileType())
            .build(), this);
        //更新分片流水成功
        normalUpdateSliceSerial(lcInboundSerialDoList.size(), shardingResult.getLcSliceBatchSerialDo());
        log.info("=========分片执行结束:[{}]数量为[{}]===========", shardingResult.getJobShared()
            .getBatchNum(), lcInboundSerialDoList.size());
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        log.info("====================== 接入业务{}分片逻辑 start ================================================",
            getJobTrade().getDescription());
        List<JobShared> jobSharedList = new ArrayList<>();
        //sql 批处理数量 暂定为分片数量，不放大
        Integer batchFixNum = jobInitDto.getFixNum();
        //当前分组的最大值，为下次 批处理的最小值
        LcInboundSerialDo maxInboundSerialDo = new LcInboundSerialDo();
        //业务时间
        Date businessDate = JobUtil.getExecuteDate(jobInitDto);
        Date bizDatetimeBegin = DateUtil.getDayBegin(businessDate);
        Date bizDatetimeEnd = DateUtil.getDayEnd(businessDate);
        //构造查询条件 查询当前 分批处理的 排序 最大 对象
        InboundSerialBatchQuery query = InboundSerialBatchQuery.builder()
            .bizDatetimeEnd(bizDatetimeEnd)
            .bizDatetimeBegin(bizDatetimeBegin)
            .offset(batchFixNum - 1)
            .limit(1)
            .build();
        //分片流水
        int batchNum = 0;
        while (maxInboundSerialDo != null) {
            query.setCisSerialNo(maxInboundSerialDo.getCisSerialNo());
            maxInboundSerialDo = inboundSerialBatchDao.selectFirstOne(query);
            //统计分片 数量
            batchNum = countBatchNum(batchFixNum, query, maxInboundSerialDo, batchNum, jobInitDto, jobSharedList,
                query.getCisSerialNo(), false);
        }
        log.info("======================== 接入业务{}分片逻辑 end 分片数量为{} ================================================",
            getJobTrade().getDescription(), jobSharedList.size());
        return jobSharedList;
    }

    @Override
    public BaseFile getBaseFile() {
        return InboundSerialFileBo.builder()
            .build();
    }
}
