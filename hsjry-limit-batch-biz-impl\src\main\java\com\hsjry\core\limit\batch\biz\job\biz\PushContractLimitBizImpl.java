// package com.hsjry.core.limit.batch.biz.job.biz;
//
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Service;
//
// import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;
// import com.hsjry.base.common.job.dto.IEnumTrade;
// import com.hsjry.base.common.job.dto.JobInitDto;
// import com.hsjry.core.limit.batch.biz.PushLimitFileBiz;
// import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
//
// import lombok.extern.slf4j.Slf4j;
//
// /**
//  * 合同额度信息文件推送任务
//  * 不带分片
//  * <AUTHOR>
//  * @version V4.0
//  * @since 4.0 2024/2/6 14:02
//  */
// @Service
// @Slf4j
// public class PushContractLimitBizImpl implements BaseOrdinaryBiz {
//
//     // @Autowired
//     // private PushLimitFileBiz pushLimitFileBiz;
//
//     @Override
//     public IEnumTrade getJobTrade() {
//         return EnumJobTrade.PUSH_CONTRACT_LIMIT;
//     }
//
//     @Override
//     public void execBaseJob(JobInitDto jobInitDto) {
//         log.info("========合同额度信息文件推送任务开始========" + jobInitDto.getBusinessDate());
//         pushLimitFileBiz.pushContractLimit(jobInitDto.getBusinessDate());
//         log.info("========合同额度信息文件推送任务结束========" + jobInitDto.getBusinessDate());
//     }
// }
