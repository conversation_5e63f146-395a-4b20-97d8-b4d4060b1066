package com.hsjry.core.limit.batch.dal.dao.intf;

import com.hsjry.core.limit.batch.dal.dao.model.LbHElcblTfrdcnLmtOcpDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbHElcblTfrdcnLmtOcpQuery;

import java.util.List;

import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 电票系统-历史表-日终转贴现额度占用同步数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbHElcblTfrdcnLmtOcpDao extends IBaseDao<LbHElcblTfrdcnLmtOcpDo> {
    /**
     * 分页查询电票系统-历史表-日终转贴现额度占用同步
     *
     * @param lbHElcblTfrdcnLmtOcpQuery 条件
     * @return PageInfo<LbHElcblTfrdcnLmtOcpDo>
     */
    PageInfo<LbHElcblTfrdcnLmtOcpDo> selectPage(LbHElcblTfrdcnLmtOcpQuery lbHElcblTfrdcnLmtOcpQuery,
        PageParam pageParam);

    /**
     * 根据key查询电票系统-历史表-日终转贴现额度占用同步
     *
     * @param batchNumb
     * @param billNumb
     * @param billRangeStart
     * @param billRangeEnd
     * @param dataDate
     * @return
     */
    LbHElcblTfrdcnLmtOcpDo selectByKey(String batchNumb, String billNumb, String billRangeStart, String billRangeEnd,
        String dataDate);

    /**
     * 根据key删除电票系统-历史表-日终转贴现额度占用同步
     *
     * @param batchNumb
     * @param billNumb
     * @param billRangeStart
     * @param billRangeEnd
     * @param dataDate
     * @return
     */
    int deleteByKey(String batchNumb, String billNumb, String billRangeStart, String billRangeEnd, String dataDate);

    /**
     * 查询电票系统-历史表-日终转贴现额度占用同步信息
     *
     * @param lbHElcblTfrdcnLmtOcpQuery 条件
     * @return List<LbHElcblTfrdcnLmtOcpDo>
     */
    List<LbHElcblTfrdcnLmtOcpDo> selectByExample(LbHElcblTfrdcnLmtOcpQuery lbHElcblTfrdcnLmtOcpQuery);

    /**
     * 新增电票系统-历史表-日终转贴现额度占用同步信息
     *
     * @param lbHElcblTfrdcnLmtOcp 条件
     * @return int>
     */
    int insertBySelective(LbHElcblTfrdcnLmtOcpDo lbHElcblTfrdcnLmtOcp);

    /**
     * 修改电票系统-历史表-日终转贴现额度占用同步信息
     *
     * @param lbHElcblTfrdcnLmtOcp
     * @return
     */
    int updateBySelective(LbHElcblTfrdcnLmtOcpDo lbHElcblTfrdcnLmtOcp);

    /**
     * 修改电票系统-历史表-日终转贴现额度占用同步信息
     *
     * @param lbHElcblTfrdcnLmtOcp
     * @param lbHElcblTfrdcnLmtOcpQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbHElcblTfrdcnLmtOcpDo lbHElcblTfrdcnLmtOcp,
        LbHElcblTfrdcnLmtOcpQuery lbHElcblTfrdcnLmtOcpQuery);

    int deleteByDataDate(String dataDate);
}
