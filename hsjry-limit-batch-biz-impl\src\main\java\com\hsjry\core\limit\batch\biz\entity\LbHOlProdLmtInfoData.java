/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.entity;

import java.math.BigDecimal;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 网贷系统历史表产品额度信息文件数据实体
 * 对应LB_H_OL_PROD_LMT_INFO表结构
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/21 15:30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LbHOlProdLmtInfoData {

    /** 额度编号 - 系统生成的UUID，全局唯一标识一个额度产品 */
    private String creditLimitId;

    /** 客户编号 - 关联客户主表的customer_id字段 */
    private String userId;

    /** 客户姓名 - 客户在系统中登记的姓名或企业名称 */
    private String userName;

    /** 客户类型 - 客户分类：P-个人，E-企业，G-政府机构，O-其他组织 */
    private String userType;

    /** 证件号码 - 个人客户为身份证号，企业客户为统一社会信用代码 */
    private String certificateNo;

    /** 证件类型 - 证件类型编码：01-居民身份证，02-护照，03-营业执照等 */
    private String certificateType;

    /** 手机号码 - 客户在系统中登记的主要联系电话 */
    private String userMobile;

    /** 产品编号 - 关联产品配置表的product_code字段 */
    private String productId;

    /** 产品名称 - 额度产品在系统中的展示名称 */
    private String productName;

    /** 额度类型 - 1-循环额度（还款后额度恢复），2-非循环额度（使用后需重新申请） */
    private String creditType;

    /** 状态 - 额度产品当前的业务状态 */
    private String status;

    /** 总额度 - 审批通过的额度总金额 */
    private BigDecimal totalAmount;

    /** 冻结额度 - 因风险控制或司法冻结的额度金额 */
    private BigDecimal frozenAmount;

    /** 使用中额度 - 已放款但未还清的额度金额 */
    private BigDecimal usingAmount;

    /** 已使用额度 - 历史累计使用的额度金额 */
    private BigDecimal usedAmount;

    /** 用款次数 - 客户已使用该额度产品的放款次数 */
    private Integer useLoanTimes;

    /** 用款次数限制 - 该额度产品允许的最大放款次数 */
    private Integer loanTimesLimit;

    /** 生效开始时间 - 额度产品开始生效的日期时间 */
    private Date effectiveStartTime;

    /** 生效结束时间 - 额度产品失效的日期时间 */
    private Date effectiveEndTime;

    /** 操作员编号 - 最后一次修改该记录的操作员标识 */
    private String operatorId;

    /** 归属机构编号 - 该额度产品归属的业务机构代码 */
    private String ownOrganId;

    /** 创建时间 - 记录在数据库中的创建时间戳 */
    private Date createTime;

    /** 更新时间 - 记录最后一次更新的时间戳 */
    private Date updateTime;

    /** 租户编号 - 多租户系统中的租户标识 */
    private String tenantId;

    /** 数据日期 - 历史表特有字段，标识该记录所属的业务日期 */
    private String dataDate;
} 