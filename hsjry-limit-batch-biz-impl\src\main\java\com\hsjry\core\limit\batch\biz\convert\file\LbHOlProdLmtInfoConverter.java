/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.convert.file;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.hsjry.core.limit.batch.biz.entity.LbHOlProdLmtInfoData;
import com.hsjry.core.limit.batch.dal.dao.model.LbHOlProdLmtInfoDo;

import lombok.extern.slf4j.Slf4j;

/**
 * 网贷系统历史表产品额度信息文件数据转换器
 * 封装转换逻辑的工具类
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/21 16:00
 */
@Slf4j
public class LbHOlProdLmtInfoConverter {

    /** 默认初始容量 */
    private static final int DEFAULT_INITIAL_CAPACITY = 16;
    /** 负载因子 */
    private static final float LOAD_FACTOR = 0.75f;

    /**
     * Data 转换为 Do
     * 使用MapStruct进行高性能对象映射
     *
     * @param data 文件数据对象
     * @return DO对象
     */
    public static LbHOlProdLmtInfoDo data2Do(LbHOlProdLmtInfoData data) {
        return LbHOlProdLmtInfoCnvs.INSTANCE.data2Do(data);
    }

    /**
     * Data列表转DO列表
     * 使用并行流提高大批量数据转换性能
     *
     * @param dataList Data对象列表
     * @return DO对象列表
     */
    public static List<LbHOlProdLmtInfoDo> dataListToDoList(List<LbHOlProdLmtInfoData> dataList) {
        if (Objects.isNull(dataList)) {
            return Collections.emptyList();
        }
        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (dataList.size() / LOAD_FACTOR) + 1, DEFAULT_INITIAL_CAPACITY);
        return dataList.parallelStream()
                .map(LbHOlProdLmtInfoConverter::data2Do)
                .collect(Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
    }

    /**
     * DO 转换为 Data
     * 反向转换，用于数据回显或其他场景
     *
     * @param dataObject DO对象
     * @return Data对象
     */
    public static LbHOlProdLmtInfoData do2Data(LbHOlProdLmtInfoDo dataObject) {
        return LbHOlProdLmtInfoCnvs.INSTANCE.do2Data(dataObject);
    }

    /**
     * DO列表转Data列表
     * 反向批量转换
     *
     * @param doList DO对象列表
     * @return Data对象列表
     */
    public static List<LbHOlProdLmtInfoData> doListToDataList(List<LbHOlProdLmtInfoDo> doList) {
        if (Objects.isNull(doList)) {
            return Collections.emptyList();
        }
        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (doList.size() / LOAD_FACTOR) + 1, DEFAULT_INITIAL_CAPACITY);
        return doList.parallelStream()
                .map(LbHOlProdLmtInfoConverter::do2Data)
                .collect(Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
    }
} 