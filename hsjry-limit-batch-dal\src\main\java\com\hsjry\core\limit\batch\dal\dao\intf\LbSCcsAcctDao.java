package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbSCcsAcctDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSCcsAcctQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 信用卡-落地表-第一币种贷记帐户数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 03:51:17
 */
public interface LbSCcsAcctDao extends IBaseDao<LbSCcsAcctDo> {
    /**
     * 分页查询信用卡-落地表-第一币种贷记帐户
     *
     * @param lbSCcsAcctQuery 条件
     * @return PageInfo<LbSCcsAcctDo>
     */
    PageInfo<LbSCcsAcctDo> selectPage(LbSCcsAcctQuery lbSCcsAcctQuery, PageParam pageParam);

    /**
     * 根据key查询信用卡-落地表-第一币种贷记帐户
     *
     * @param xaccount
     * @param bank
     * @return
     */
    LbSCcsAcctDo selectByKey(Integer xaccount, Integer bank);

    /**
     * 根据key删除信用卡-落地表-第一币种贷记帐户
     *
     * @param xaccount
     * @param bank
     * @return
     */
    int deleteByKey(Integer xaccount, Integer bank);

    /**
     * 查询信用卡-落地表-第一币种贷记帐户信息
     *
     * @param lbSCcsAcctQuery 条件
     * @return List<LbSCcsAcctDo>
     */
    List<LbSCcsAcctDo> selectByExample(LbSCcsAcctQuery lbSCcsAcctQuery);

    /**
     * 新增信用卡-落地表-第一币种贷记帐户信息
     *
     * @param lbSCcsAcct 条件
     * @return int>
     */
    int insertBySelective(LbSCcsAcctDo lbSCcsAcct);

    /**
     * 修改信用卡-落地表-第一币种贷记帐户信息
     *
     * @param lbSCcsAcct
     * @return
     */
    int updateBySelective(LbSCcsAcctDo lbSCcsAcct);

    /**
     * 修改信用卡-落地表-第一币种贷记帐户信息
     *
     * @param lbSCcsAcct
     * @param lbSCcsAcctQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbSCcsAcctDo lbSCcsAcct, LbSCcsAcctQuery lbSCcsAcctQuery);

    /**
     * 批量插入信用卡落地表信息
     *
     * @param lbSCcsAcctList 批量数据
     * @return int
     */
    @Override
    int insertList(List<LbSCcsAcctDo> lbSCcsAcctList);

    /**
     * 清空信用卡落地表所有数据
     *
     * @return int
     */
    int deleteAll();

    /**
     * 查询分片数据
     *
     * @param query 查询条件
     * @return List<LbSCcsAcctDo>
     */
    List<LbSCcsAcctDo> selectShardList(LbSCcsAcctQuery query);

    /**
     * 获取第一个对象，limit m，1
     *
     * @param query 查询条件
     * @return LbSCcsAcctDo
     */
    LbSCcsAcctDo selectFirstOne(LbSCcsAcctQuery query);

    /**
     * 获取当前组的数据量
     *
     * @param query 查询条件
     * @return Integer
     */
    Integer selectCountByCurrentGroup(LbSCcsAcctQuery query);
}
