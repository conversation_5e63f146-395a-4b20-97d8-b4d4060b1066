package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.core.limit.batch.dal.dao.intf.LbTReclcIndvOpLoanDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTReclcIndvOpLoanMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIndvOpLoanDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIndvOpLoanExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIndvOpLoanKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcIndvOpLoanQuery;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中个人额度中经营贷额度数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-08-21 13:10:51
 */
@Repository
public class LbTReclcIndvOpLoanDaoImpl extends AbstractBaseDaoImpl<LbTReclcIndvOpLoanDo, LbTReclcIndvOpLoanMapper>
    implements LbTReclcIndvOpLoanDao {
    /**
     * 分页查询
     *
     * @param lbTReclcIndvOpLoan 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTReclcIndvOpLoanDo> selectPage(LbTReclcIndvOpLoanQuery lbTReclcIndvOpLoan, PageParam pageParam) {
        LbTReclcIndvOpLoanExample example = buildExample(lbTReclcIndvOpLoan);
        return PageHelper.<LbTReclcIndvOpLoanDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询额度中心-中间表-额度重算中个人额度中经营贷额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public LbTReclcIndvOpLoanDo selectByKey(String custNo, String custLimitId) {
        LbTReclcIndvOpLoanKeyDo lbTReclcIndvOpLoanKeyDo = new LbTReclcIndvOpLoanKeyDo();
        lbTReclcIndvOpLoanKeyDo.setCustNo(custNo);
        lbTReclcIndvOpLoanKeyDo.setCustLimitId(custLimitId);
        return getMapper().selectByPrimaryKey(lbTReclcIndvOpLoanKeyDo);
    }

    /**
     * 根据key删除额度中心-中间表-额度重算中个人额度中经营贷额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public int deleteByKey(String custNo, String custLimitId) {
        LbTReclcIndvOpLoanKeyDo lbTReclcIndvOpLoanKeyDo = new LbTReclcIndvOpLoanKeyDo();
        lbTReclcIndvOpLoanKeyDo.setCustNo(custNo);
        lbTReclcIndvOpLoanKeyDo.setCustLimitId(custLimitId);
        return getMapper().deleteByPrimaryKey(lbTReclcIndvOpLoanKeyDo);
    }

    /**
     * 查询额度中心-中间表-额度重算中个人额度中经营贷额度信息
     *
     * @param lbTReclcIndvOpLoan 条件
     * @return List<LbTReclcIndvOpLoanDo>
     */
    @Override
    public List<LbTReclcIndvOpLoanDo> selectByExample(LbTReclcIndvOpLoanQuery lbTReclcIndvOpLoan) {
        return getMapper().selectByExample(buildExample(lbTReclcIndvOpLoan));
    }

    /**
     * 新增额度中心-中间表-额度重算中个人额度中经营贷额度信息
     *
     * @param lbTReclcIndvOpLoan 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTReclcIndvOpLoanDo lbTReclcIndvOpLoan) {
        if (lbTReclcIndvOpLoan == null) {
            return -1;
        }

        return getMapper().insertSelective(lbTReclcIndvOpLoan);
    }

    /**
     * 修改额度中心-中间表-额度重算中个人额度中经营贷额度信息
     *
     * @param lbTReclcIndvOpLoan
     * @return
     */
    @Override
    public int updateBySelective(LbTReclcIndvOpLoanDo lbTReclcIndvOpLoan) {
        if (lbTReclcIndvOpLoan == null) {
            return -1;
        }

        return getMapper().updateByPrimaryKeySelective(lbTReclcIndvOpLoan);
    }

    @Override
    public int updateBySelectiveByExample(LbTReclcIndvOpLoanDo lbTReclcIndvOpLoan,
        LbTReclcIndvOpLoanQuery lbTReclcIndvOpLoanQuery) {
        return getMapper().updateByExampleSelective(lbTReclcIndvOpLoan, buildExample(lbTReclcIndvOpLoanQuery));
    }

    /**
     * 构建额度中心-中间表-额度重算中个人额度中经营贷额度Example信息
     *
     * @param lbTReclcIndvOpLoan
     * @return
     */
    public LbTReclcIndvOpLoanExample buildExample(LbTReclcIndvOpLoanQuery lbTReclcIndvOpLoan) {
        LbTReclcIndvOpLoanExample example = new LbTReclcIndvOpLoanExample();
        LbTReclcIndvOpLoanExample.Criteria criteria = example.createCriteria();
        if (lbTReclcIndvOpLoan != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbTReclcIndvOpLoan.getCustNo())) {
                criteria.andCustNoEqualTo(lbTReclcIndvOpLoan.getCustNo());
            }
            if (StringUtil.isNotEmpty(lbTReclcIndvOpLoan.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lbTReclcIndvOpLoan.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lbTReclcIndvOpLoan.getTemplateNodeId())) {
                criteria.andTemplateNodeIdEqualTo(lbTReclcIndvOpLoan.getTemplateNodeId());
            }
            if (StringUtil.isNotEmpty(lbTReclcIndvOpLoan.getLimitStatus())) {
                criteria.andLimitStatusEqualTo(lbTReclcIndvOpLoan.getLimitStatus());
            }
            if (null != lbTReclcIndvOpLoan.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbTReclcIndvOpLoan.getTotalAmount());
            }
            if (null != lbTReclcIndvOpLoan.getPreOccupyAmount()) {
                criteria.andPreOccupyAmountEqualTo(lbTReclcIndvOpLoan.getPreOccupyAmount());
            }
            if (null != lbTReclcIndvOpLoan.getRealOccupyAmount()) {
                criteria.andRealOccupyAmountEqualTo(lbTReclcIndvOpLoan.getRealOccupyAmount());
            }
            if (null != lbTReclcIndvOpLoan.getLowRiskAmount()) {
                criteria.andLowRiskAmountEqualTo(lbTReclcIndvOpLoan.getLowRiskAmount());
            }
            if (null != lbTReclcIndvOpLoan.getPreOccupyLowRiskAmt()) {
                criteria.andPreOccupyLowRiskAmtEqualTo(lbTReclcIndvOpLoan.getPreOccupyLowRiskAmt());
            }
            if (null != lbTReclcIndvOpLoan.getRealOccupyLowRiskAmt()) {
                criteria.andRealOccupyLowRiskAmtEqualTo(lbTReclcIndvOpLoan.getRealOccupyLowRiskAmt());
            }
        }
        buildExampleExt(lbTReclcIndvOpLoan, criteria);
        return example;
    }

    /**
     * 构建额度中心-中间表-额度重算中个人额度中经营贷额度ExampleExt方法
     *
     * @param lbTReclcIndvOpLoan
     * @return
     */
    public void buildExampleExt(LbTReclcIndvOpLoanQuery lbTReclcIndvOpLoan,
        LbTReclcIndvOpLoanExample.Criteria criteria) {

        //自定义实现
    }

    // ==================== 经营贷额度重算相关方法实现 ====================

    /**
     * 1.1.清空经营贷额度中间表
     */
    @Override
    public int truncateOpLoanLimit() {
        return getMapper().truncateOpLoanLimit();
    }

    /**
     * 1.2.插入经营贷额度客户编号和额度编号
     */
    @Override
    public int insertOpLoanLimit() {
        return getMapper().insertOpLoanLimit();
    }

    /**
     * 1.3.更新经营贷额度中间表金额信息
     */
    @Override
    public int mergeOpLoanLimitAmount() {
        return getMapper().mergeOpLoanLimitAmount();
    }

    /**
     * 1.4.更新额度实例金额信息
     */
    @Override
    public int mergeOpLoanLimitInstance() {
        return getMapper().mergeOpLoanLimitInstance();
    }

}
