package com.hsjry.core.limit.batch.common.utils;

import ch.ethz.ssh2.ChannelCondition;
import ch.ethz.ssh2.Connection;
import ch.ethz.ssh2.Session;
import ch.ethz.ssh2.StreamGobbler;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.Charset;

/**
 * ssh工具
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2024/5/21 10:55
 */
@Data
@Slf4j
public class SSHClient {

    private String ip;
    private String username;
    private String password;
    private int port;

    private String charset = Charset.defaultCharset().toString();
    private static final int TIME_OUT = 1000 * 5 * 60;

    private Connection conn;

    public SSHClient(String ip, String username, String password, int port) {
        this.ip = ip;
        this.username = username;
        this.password = password;
        this.port = port;
    }

    /**
     * 登录指远程服务器
     *
     * @return
     * @throws IOException
     */
    private boolean login() throws IOException {
        conn = new Connection(ip, port);
        conn.connect();
        return conn.authenticateWithPassword(username, password);
    }

    public int exec(String shell) throws Exception {
        int ret = -1;
        try {
            if (login()) {
                Session session = conn.openSession();
                session.execCommand(shell);
                String returnValue = processStdout(session.getStdout());
                String returnErr = processStdout(session.getStderr());
                log.info("sh脚本执行过程:{}", returnValue);
                log.info("错误信息:{}", returnErr);
                session.waitForCondition(ChannelCondition.EXIT_STATUS, TIME_OUT);
                ret = session.getExitStatus();
                log.info("ret:{}", ret);
            } else {
                throw new Exception("登录远程机器失败" + ip); // 自定义异常类 实现略
            }
        } finally {
            if (conn != null) {
                conn.close();
            }
        }
        return ret;
    }

    public String processStdout(InputStream in) {
        InputStream stdout = new StreamGobbler(in);
        StringBuffer buffer = new StringBuffer();
        try {
            BufferedReader br = new BufferedReader(new InputStreamReader(stdout));
            String line;
            while ((line = br.readLine()) != null) {
                buffer.append(line).append(";\n");
            }
            br.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return buffer.toString();
    }


    public static void main(String[] args) {
        try {
            SSHClient sshClient = new SSHClient("************", "root", "su@1234", 22);
            sshClient.exec("/hsdata/test/file_clear.sh " + 123555456);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
