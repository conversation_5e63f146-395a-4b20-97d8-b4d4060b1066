/*
 * yunrong.cn Inc. Copyright (c) 2014-2019 All Rights Reserved
 */

package com.hsjry.core.limit.batch.biz.job.sharding;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;

/**
 * 借据核心业务工厂类
 *
 * <AUTHOR>
 * @version V2.1
 * @since 2.1.0 2019-03-12 14:56
 */
@Component
public class JobCoreBusinessFactory implements ApplicationContextAware {

    private static Map<String, JobCoreBusiness> jobCoreBusinessMap;

    public static JobCoreBusiness getJobCoreBusiness(String jobTrade) {
        return jobCoreBusinessMap.get(jobTrade);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        Map<String, JobCoreBusiness> map = applicationContext.getBeansOfType(JobCoreBusiness.class);
        jobCoreBusinessMap = new HashMap<>(16);
        for (Map.Entry<String, JobCoreBusiness> entry : map.entrySet()) {
            jobCoreBusinessMap.put(entry.getValue().getJobTrade().getCode(), entry.getValue());
        }

    }

}
