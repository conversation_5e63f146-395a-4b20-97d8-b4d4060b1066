/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.entity;

import java.math.BigDecimal;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 网贷系统产品额度信息文件数据实体
 * 对应LB_S_OL_PROD_LMT_INFO表结构
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/21 15:30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LbSOlProdLmtInfoData {

    /** 额度编号 */
    private String creditLimitId;

    /** 客户编号 */
    private String userId;

    /** 客户姓名 */
    private String userName;

    /** 客户类型 */
    private String userType;

    /** 证件号码 */
    private String certificateNo;

    /** 证件类型 */
    private String certificateType;

    /** 手机号码 */
    private String userMobile;

    /** 产品编号 */
    private String productId;

    /** 产品名称 */
    private String productName;

    /** 额度类型(1循环/2非循环) */
    private String creditType;

    /** 状态 */
    private String status;

    /** 总额度 */
    private BigDecimal totalAmount;

    /** 冻结额度 */
    private BigDecimal frozenAmount;

    /** 使用中额度 */
    private BigDecimal usingAmount;

    /** 已使用额度 */
    private BigDecimal usedAmount;

    /** 已使用支用次数 */
    private Integer useLoanTimes;

    /** 支用次数限制(-1表示无限制) */
    private Integer loanTimesLimit;

    /** 生效起始时间 */
    private Date effectiveStartTime;

    /** 生效结束时间 */
    private Date effectiveEndTime;

    /** 操作人ID */
    private String operatorId;

    /** 所属组织ID */
    private String ownOrganId;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 租户号 */
    private String tenantId;
} 