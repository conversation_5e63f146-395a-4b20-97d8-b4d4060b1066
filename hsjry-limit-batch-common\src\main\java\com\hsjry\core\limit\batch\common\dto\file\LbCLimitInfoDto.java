package com.hsjry.core.limit.batch.common.dto.file;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 额度实例信息文件传输对象
 *
 * <AUTHOR>
 * @version V3.0.5
 * @since 2025/7/510:32
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LbCLimitInfoDto implements Serializable {

    private static final long serialVersionUID = 112796876171413162L;

    /** 额度编号 */
    private String custLimitId;

    /** 额度状态;EnumCustLimitStatus(010-未生效，020-生效，030-冻结，040-终止-失效，050-到期-失效) */
    private String limitStatus;

    /** 操作人编号 */
    private String operatorId;

    /** 所属组织编号 */
    private String ownOrganId;

    /** 生效起始时间 */
    private Date effectiveStartTime;

    /** 生效结束时间 */
    private Date effectiveEndTime;

    /** 期限 */
    private Integer limitTerm;

    /** 期限单位;001-年、002-月、003-日 */
    private String limitTermUnit;

    /** 宽限期限 */
    private Integer limitGraceTerm;

    /** 宽限期限单位;001-年、002-月、003-日 */
    private String limitGraceTermUnit;

    /** 额度最后到期日 */
    private Date limitLastTime;

    /** 批复日期 */
    private Date limitApprovalDate;

    /** 额度启动到期日 */
    private Date limitEnableEndTime;

    /** 启用期限 */
    private Integer limitEnableTerm;

    /** 启用期限单位 */
    private String limitEnableTermUnit;

    /** 三方额度编号 */
    private String outCustLimitId;

    /** 额度类型编号 */
    private String custLimitTypeId;

    /** 模板节点编号 */
    private String templateNodeId;

    /** 额度模板编号 */
    private String limitTemplateId;

    /** 所属对象编号 */
    private String limitObjectId;

    /** 所属对象核心编号 */
    private String limitCoreObjectId;

    /** 所属对象类型 */
    private String limitObjectType;

    /** 已占用次数 */
    private Integer useOccupyTimes;

    /** 占用次数限制 */
    private Integer occupyTimesLimit;

    /** 业务品种【产品】 */
    private String productId;

    /** 额度层级;EnumLimitLevel:001-根节点、002-中间节点、003-末级节点 */
    private String limitLevel;

    /** 额度分类;dictKey:LIMIT_CLASSIFICATION */
    private String limitClassification;

    /** 额度使用方式;EnumUsageType(001-非循环授信, 002-循环授信） */
    private String limitUsageType;

    /** 额度设立方式;EnumLimitGrantType 001-拆分、002-共享、003-汇总 */
    private String limitGrantType;

    /** 额度占用方式;EnumOccupationType(001-合同占用, 002-余额占用,003-总量占用) */
    private String limitOccupationType;

    /** 是否向上占用;EnumBool(Y-是，N-否) */
    private String upFlag;

    /** 是否合同额度;EnumBool(Y-是，N-否) */
    private String contractLimitFlag;

    /** 是否唯一;EnumBool(Y-是，N-否) */
    private String soleFlag;

    /** 汇率版本 */
    private Integer exchangeRateVersion;

    /** 合同额度汇率重算标记 */
    private String contractRecalFlag;

    /** 额度序号 */
    private Integer seq;

    /** 超额占用方式;EnumExcessOccupationType:001-压缩占用、002-超额占用 */
    private String excessOccupationType;

    /** 实例编号 */
    private String instId;

    /** 关联编号 */
    private String relationId;

    /** 业务条线 */
    private String bizLine;

    /** 是否允许被串用 ,EnumBool(Y-是，N-否) */
    private String supportSharedFlag;

    /** 是否允许串用 EnumBool(Y-是，N-否) */
    private String supportShareFlag;

    /** 虚拟合同标识;EnumBool(Y-是，N-否) */
    private String virtualContractFlag;

    /** 发生类型,EnumHappenType */
    private String happenType;

    /** 重组标签,EnumRegroupTag */
    private String regroupTag;

    /** 主担保方式,EnumMainGuarType */
    private String mainGuarType;

    /** 首次提款有效期 */
    private Date ftmDwnAvlDt;

    /** 所属法人的核心机构号 */
    private String blngLglpsnCoreInsNo;

    /** 所属法人的核心机构名称 */
    private String blngLglpsnCoreInsNm;
}
