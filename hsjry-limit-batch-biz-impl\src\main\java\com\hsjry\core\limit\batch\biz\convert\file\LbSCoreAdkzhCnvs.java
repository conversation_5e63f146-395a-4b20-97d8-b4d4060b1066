/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.convert.file;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import com.hsjry.core.limit.batch.biz.entity.LbSCoreAdkzhData;
import com.hsjry.core.limit.batch.dal.dao.model.LbSCoreAdkzhDo;

/**
 * 核心系统-落地表-贷款账户主表文件数据转换器
 * 使用MapStruct进行高性能对象映射
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/21 11:00
 */
@Mapper(componentModel = "spring")
public interface LbSCoreAdkzhCnvs {

    LbSCoreAdkzhCnvs INSTANCE = Mappers.getMapper(LbSCoreAdkzhCnvs.class);

    /**
     * 单个Data转DO
     * 基础转换方法，使用MapStruct自动映射
     *
     * @param data 源数据对象
     * @return 目标DO对象
     */
    LbSCoreAdkzhDo data2Do(LbSCoreAdkzhData data);

    /**
     * 单个DO转Data
     * 基础转换方法，使用MapStruct自动映射
     *
     * @param dataObject DO对象
     * @return 目标Data对象
     */
    LbSCoreAdkzhData do2Data(LbSCoreAdkzhDo dataObject);

    /**
     * Data列表转DO列表
     * 批量转换方法，提升处理效率
     *
     * @param dataList 源数据列表
     * @return 目标DO列表
     */
    List<LbSCoreAdkzhDo> dataListToDoList(List<LbSCoreAdkzhData> dataList);

    /**
     * DO列表转Data列表
     * 批量转换方法，提升处理效率
     *
     * @param doList DO列表
     * @return 目标Data列表
     */
    List<LbSCoreAdkzhData> doListToDataList(List<LbSCoreAdkzhDo> doList);
} 