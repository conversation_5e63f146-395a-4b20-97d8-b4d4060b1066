package com.hsjry.core.limit.batch.dal.dao.intf;

import com.hsjry.core.limit.batch.dal.dao.model.LbSElcblTfrdcnLmtRlseDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbSElcblTfrdcnLmtRlseQuery;

import java.util.List;

import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 电票日终转贴现额度释放同步-落地数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbSElcblTfrdcnLmtRlseDao extends IBaseDao<LbSElcblTfrdcnLmtRlseDo> {
    /**
     * 分页查询电票日终转贴现额度释放同步-落地
     *
     * @param lbSElcblTfrdcnLmtRlseQuery 条件
     * @return PageInfo<LbSElcblTfrdcnLmtRlseDo>
     */
    PageInfo<LbSElcblTfrdcnLmtRlseDo> selectPage(LbSElcblTfrdcnLmtRlseQuery lbSElcblTfrdcnLmtRlseQuery,
        PageParam pageParam);

    /**
     * 根据key查询电票日终转贴现额度释放同步-落地
     *
     * @param batchNumb
     * @param billNumb
     * @param billRangeStart
     * @param billRangeEnd
     * @return
     */
    LbSElcblTfrdcnLmtRlseDo selectByKey(String batchNumb, String billNumb, String billRangeStart, String billRangeEnd);

    /**
     * 根据key删除电票日终转贴现额度释放同步-落地
     *
     * @param batchNumb
     * @param billNumb
     * @param billRangeStart
     * @param billRangeEnd
     * @return
     */
    int deleteByKey(String batchNumb, String billNumb, String billRangeStart, String billRangeEnd);

    /**
     * 查询电票日终转贴现额度释放同步-落地信息
     *
     * @param lbSElcblTfrdcnLmtRlseQuery 条件
     * @return List<LbSElcblTfrdcnLmtRlseDo>
     */
    List<LbSElcblTfrdcnLmtRlseDo> selectByExample(LbSElcblTfrdcnLmtRlseQuery lbSElcblTfrdcnLmtRlseQuery);

    /**
     * 新增电票日终转贴现额度释放同步-落地信息
     *
     * @param lbSElcblTfrdcnLmtRlse 条件
     * @return int>
     */
    int insertBySelective(LbSElcblTfrdcnLmtRlseDo lbSElcblTfrdcnLmtRlse);

    /**
     * 修改电票日终转贴现额度释放同步-落地信息
     *
     * @param lbSElcblTfrdcnLmtRlse
     * @return
     */
    int updateBySelective(LbSElcblTfrdcnLmtRlseDo lbSElcblTfrdcnLmtRlse);

    /**
     * 修改电票日终转贴现额度释放同步-落地信息
     *
     * @param lbSElcblTfrdcnLmtRlse
     * @param lbSElcblTfrdcnLmtRlseQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbSElcblTfrdcnLmtRlseDo lbSElcblTfrdcnLmtRlse,
        LbSElcblTfrdcnLmtRlseQuery lbSElcblTfrdcnLmtRlseQuery);
}
