// package com.hsjry.core.limit.batch.biz.job.sharding.biz.file;
//
// import java.io.File;
// import java.util.ArrayList;
// import java.util.List;
//
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.stereotype.Service;
//
// import com.hsjry.base.common.fs.service.FileProcessServiceFactory;
// import com.hsjry.base.common.fs.util.processor.FileUtils;
// import com.hsjry.base.common.job.dto.IEnumTrade;
// import com.hsjry.base.common.job.dto.JobInitDto;
// import com.hsjry.base.common.job.dto.JobShared;
// import com.hsjry.core.limit.batch.biz.PushCreditCustomerLimitFileBiz;
// import com.hsjry.core.limit.batch.biz.PushShardingPathService;
// import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
// import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
// import com.hsjry.core.limit.batch.biz.job.sharding.biz.limit.AbstractShardingPrepareBiz;
// import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
// import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitObjectInfoBatchDao;
// import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitObjectInfoDo;
// import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
// import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitObjectInfoQuery;
// import com.hsjry.lang.common.utils.CollectionUtil;
// import com.hsjry.lang.common.utils.GsonUtil;
// import com.hsjry.lang.common.utils.StringUtil;
//
// import lombok.extern.slf4j.Slf4j;
//
// /**
//  * 分片信贷客户维度文件下档
//  * <AUTHOR>
//  *
//  */
// @Service
// @Slf4j
// public class PushLimitCustomerFileJob extends AbstractShardingPrepareBiz<LcCustLimitObjectInfoQuery>
//         implements JobCoreBusiness<String>{
//     @Autowired
//     private LcCustLimitObjectInfoBatchDao lcCustLimitObjectInfoBatchDao;
//     @Autowired
//     private PushCreditCustomerLimitFileBiz pushCreditCustomerLimitFileBiz;
//     @Autowired
//     private PushShardingPathService pushShardingPathService;
//     @Autowired
//     protected FileProcessServiceFactory serviceFactory;
//
//     @Override
//     public Integer selectCountByCurrentGroupFromDb(LcCustLimitObjectInfoQuery query) {
//         return lcCustLimitObjectInfoBatchDao.selectCountByCurrentGroup(query);
//     }
//
//     @Override
//     public IEnumTrade getJobTrade() {
//         return EnumJobTrade.PUSH_CUSTOMER_LIMIT_FILE;
//     }
//
//     @Override
//     public ShardingResult<String> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo, JobInitDto jobInitDto, JobShared jobShared) {
//         ShardingResult<String> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
//         if (StringUtil.isBlank(jobShared.getExtParam())) {
//             return shardingResult;
//         }
//         //原始查询条件
//         Integer batchFixNum = jobInitDto.getFixNum();
//         LcCustLimitObjectInfoQuery query = LcCustLimitObjectInfoQuery.builder()
//                 .offset(batchFixNum * (lcSliceBatchSerialDo.getBatchNum() - 1))
//                 .limit(jobShared.getLimit())
//                 .build();
//         log.info("=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");
//         List<String> list = lcCustLimitObjectInfoBatchDao.selectShardList(query);
//         shardingResult.setShardingResultList(list);
//         return shardingResult;
//     }
//
//     @Override
//     public void execJobCoreBusiness(ShardingResult<String> shardingResult) {
//         log.info("=========[{}]分片执行开始:[{}]===========", getJobTrade().getDescription(),shardingResult.getJobShared());
//         Integer busDate = shardingResult.getJobShared().getBusinessDate();
//         List<String> shardingResultList = shardingResult.getShardingResultList();
//         //分片数据写入路径
//         String shardingFilePath = pushShardingPathService.getShardingFilePath(EnumJobTrade.PUSH_CUSTOMER_LIMIT_FILE,busDate);
//         File file = new File(shardingFilePath);
//         if(!file.exists()){
//             file.mkdirs();
//         }
//         String batchNum = String.valueOf(shardingResult.getLcSliceBatchSerialDo().getBatchNum());
//         //分片数据文件名
//         String shardingFileName = pushShardingPathService.getShardingFileName(EnumJobTrade.PUSH_CUSTOMER_LIMIT_FILE,busDate,batchNum);
//         String absShardingFilePath = shardingFilePath + File.separator + shardingFileName;
//         FileUtils.deleteIfExists(absShardingFilePath);
//
//         log.info("批次号={},开始执行......",batchNum);
//         if(CollectionUtil.isEmpty(shardingResultList)){
//             log.info("=========[{}]分片执行结束:" + batchNum + "数量为空===========",getJobTrade().getDescription());
//             //更新分片流水成功
//             normalUpdateSliceSerial(0, shardingResult.getLcSliceBatchSerialDo());
//             pushShardingPathService.writeFile(null,absShardingFilePath);
//             return;
//         }
//         //获取数据
//         List<String> result = pushCreditCustomerLimitFileBiz.generateCustomerLimitData(shardingResultList);
//         //写分片文件
//         pushShardingPathService.writeFile(result,absShardingFilePath);
//         //更新分片流水成功
//         normalUpdateSliceSerial(result.size(), shardingResult.getLcSliceBatchSerialDo());
//         log.info("=========[{}]分片执行结束:[{}]数量为[{}]===========", getJobTrade().getDescription(),shardingResult.getJobShared()
//                 .getBatchNum(), result.size());
//     }
//
//     @Override
//     public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
//         log.info("====================== 接入业务{}分片逻辑 start ================================================",
//                 getJobTrade().getDescription());
//         List<JobShared> jobSharedList = new ArrayList<>();
//         //sql 批处理数量 暂定为分片数量，不放大
//         Integer batchFixNum = jobInitDto.getFixNum();
//         //当前分组的最大值，为下次 批处理的最小值
//         LcCustLimitObjectInfoDo maxDo = new LcCustLimitObjectInfoDo();
//         //构造查询条件 查询当前 分批处理的 排序 最大 对象
//         LcCustLimitObjectInfoQuery query = LcCustLimitObjectInfoQuery.builder()
//                 .offset(batchFixNum - 1)
//                 .limit(1)
//                 .build();
//         //分片流水
//         int batchNum = 0;
//         while (maxDo != null) {
//             query.setUserId(maxDo.getUserId());
//             maxDo = lcCustLimitObjectInfoBatchDao.selectFirstOne(query);
//             //统计分片 数量
//             batchNum = countBatchNum(batchFixNum, query, maxDo, batchNum, jobInitDto, jobSharedList,
//                     query.getUserId(), false);
//         }
//         log.info("====================== 接入业务{}分片逻辑 end ================================================",
//                 getJobTrade().getDescription());
//         return jobSharedList;
//     }
//
// }
