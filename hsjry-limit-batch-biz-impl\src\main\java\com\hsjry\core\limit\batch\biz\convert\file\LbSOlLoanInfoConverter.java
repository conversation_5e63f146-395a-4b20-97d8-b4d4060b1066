/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.convert.file;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.hsjry.core.limit.batch.biz.entity.LbSOlLoanInfoData;
import com.hsjry.core.limit.batch.dal.dao.model.LbSOlLoanInfoDo;

import lombok.extern.slf4j.Slf4j;

/**
 * 网贷借据信息文件数据转换器
 * 封装Data到DO的转换逻辑
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/21 16:00
 */
@Slf4j
public class LbSOlLoanInfoConverter {

    /** 默认初始容量 */
    private static final int DEFAULT_INITIAL_CAPACITY = 16;
    /** 负载因子 */
    private static final float LOAD_FACTOR = 0.75f;

    /**
     * Data 转换为 Do
     * 使用MapStruct进行高性能转换
     *
     * @param data 文件数据
     * @return DO对象
     */
    public static LbSOlLoanInfoDo data2Do(LbSOlLoanInfoData data) {
        if (Objects.isNull(data)) {
            return null;
        }
        return LbSOlLoanInfoCnvs.INSTANCE.data2Do(data);
    }

    /**
     * DO 转换为 Data
     * 反向转换，用于数据回显或其他场景
     *
     * @param dataObject DO对象
     * @return Data对象
     */
    public static LbSOlLoanInfoData do2Data(LbSOlLoanInfoDo dataObject) {
        if (Objects.isNull(dataObject)) {
            return null;
        }
        return LbSOlLoanInfoCnvs.INSTANCE.do2Data(dataObject);
    }

    /**
     * Data列表转DO列表
     * 使用并行流提高性能，预估结果集大小避免动态扩容
     *
     * @param dataList Data列表
     * @return DO列表
     */
    public static List<LbSOlLoanInfoDo> dataListToDoList(List<LbSOlLoanInfoData> dataList) {
        if (Objects.isNull(dataList) || dataList.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (dataList.size() / LOAD_FACTOR) + 1, DEFAULT_INITIAL_CAPACITY);
        
        return dataList.parallelStream()
            .filter(Objects::nonNull) // 过滤空对象
            .map(LbSOlLoanInfoConverter::data2Do)
            .filter(Objects::nonNull) // 过滤转换失败的对象
            .collect(Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
    }

    /**
     * DO列表转Data列表
     * 反向批量转换
     *
     * @param doList DO列表
     * @return Data列表
     */
    public static List<LbSOlLoanInfoData> doListToDataList(List<LbSOlLoanInfoDo> doList) {
        if (Objects.isNull(doList) || doList.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (doList.size() / LOAD_FACTOR) + 1, DEFAULT_INITIAL_CAPACITY);
        
        return doList.parallelStream()
            .filter(Objects::nonNull) // 过滤空对象
            .map(LbSOlLoanInfoConverter::do2Data)
            .filter(Objects::nonNull) // 过滤转换失败的对象
            .collect(Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
    }
} 