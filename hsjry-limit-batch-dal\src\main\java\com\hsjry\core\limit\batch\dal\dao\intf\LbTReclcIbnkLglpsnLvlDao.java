package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIbnkLglpsnLvlDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcIbnkLglpsnLvlQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中同业客户中法人综合授信额度数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-08-21 12:16:19
 */
public interface LbTReclcIbnkLglpsnLvlDao extends IBaseDao<LbTReclcIbnkLglpsnLvlDo> {
    /**
     * 分页查询额度中心-中间表-额度重算中同业客户中法人综合授信额度
     *
     * @param lbTReclcIbnkLglpsnLvlQuery 条件
     * @return PageInfo<LbTReclcIbnkLglpsnLvlDo>
     */
    PageInfo<LbTReclcIbnkLglpsnLvlDo> selectPage(LbTReclcIbnkLglpsnLvlQuery lbTReclcIbnkLglpsnLvlQuery,
        PageParam pageParam);

    /**
     * 根据key查询额度中心-中间表-额度重算中同业客户中法人综合授信额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    LbTReclcIbnkLglpsnLvlDo selectByKey(String custNo, String custLimitId);

    /**
     * 根据key删除额度中心-中间表-额度重算中同业客户中法人综合授信额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    int deleteByKey(String custNo, String custLimitId);

    /**
     * 查询额度中心-中间表-额度重算中同业客户中法人综合授信额度信息
     *
     * @param lbTReclcIbnkLglpsnLvlQuery 条件
     * @return List<LbTReclcIbnkLglpsnLvlDo>
     */
    List<LbTReclcIbnkLglpsnLvlDo> selectByExample(LbTReclcIbnkLglpsnLvlQuery lbTReclcIbnkLglpsnLvlQuery);

    /**
     * 新增额度中心-中间表-额度重算中同业客户中法人综合授信额度信息
     *
     * @param lbTReclcIbnkLglpsnLvl 条件
     * @return int>
     */
    int insertBySelective(LbTReclcIbnkLglpsnLvlDo lbTReclcIbnkLglpsnLvl);

    /**
     * 修改额度中心-中间表-额度重算中同业客户中法人综合授信额度信息
     *
     * @param lbTReclcIbnkLglpsnLvl
     * @return
     */
    int updateBySelective(LbTReclcIbnkLglpsnLvlDo lbTReclcIbnkLglpsnLvl);

    /**
     * 修改额度中心-中间表-额度重算中同业客户中法人综合授信额度信息
     *
     * @param lbTReclcIbnkLglpsnLvl
     * @param lbTReclcIbnkLglpsnLvlQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbTReclcIbnkLglpsnLvlDo lbTReclcIbnkLglpsnLvl,
        LbTReclcIbnkLglpsnLvlQuery lbTReclcIbnkLglpsnLvlQuery);

    // ==================== 同业客户法人层额度重算相关方法 ====================

    /**
     * 1.1.清空法人层额度中间表
     */
    int truncateLegalPersonLevelLimit();

    /**
     * 1.2.插入法人层额度客户编号和额度编号
     */
    int insertLegalPersonLevelLimit();

    /**
     * 1.3.更新法人层额度中间表金额信息
     */
    int mergeLegalPersonLevelLimitAmount();

    /**
     * 1.4.更新额度实例金额信息
     */
    int mergeLegalPersonLevelLimitInstance();
}
