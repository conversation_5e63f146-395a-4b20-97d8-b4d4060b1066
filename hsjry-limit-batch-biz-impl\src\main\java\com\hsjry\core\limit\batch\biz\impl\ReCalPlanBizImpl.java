/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hsjry.core.limit.batch.biz.ReCalPlanBiz;
import com.hsjry.core.limit.center.dal.dao.intf.LcRecalBalanceOccupyPlanDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcRecalContractOccupyPlanDao;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/22 16:16
 */
@Service
public class ReCalPlanBizImpl implements ReCalPlanBiz {
    @Autowired
    private LcRecalContractOccupyPlanDao lcRecalContractOccupyPlanDao;
    @Autowired
    private LcRecalBalanceOccupyPlanDao lcRecalBalanceOccupyPlanDao;

    @Override
    public void clear() {
        lcRecalContractOccupyPlanDao.deleteAll();
        lcRecalBalanceOccupyPlanDao.deleteAll();
    }
}
