package com.hsjry.core.limit.batch.dal.dao.intf;

import com.hsjry.core.limit.batch.dal.dao.model.LbHOlLoanInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbHOlLoanInfoQuery;

import java.util.List;

import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 网贷系统-历史表-借据信息（记录客户借款信息）数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbHOlLoanInfoDao extends IBaseDao<LbHOlLoanInfoDo> {
    /**
     * 分页查询网贷系统-历史表-借据信息（记录客户借款信息）
     *
     * @param lbHOlLoanInfoQuery 条件
     * @return PageInfo<LbHOlLoanInfoDo>
     */
    PageInfo<LbHOlLoanInfoDo> selectPage(LbHOlLoanInfoQuery lbHOlLoanInfoQuery, PageParam pageParam);

    /**
     * 根据key查询网贷系统-历史表-借据信息（记录客户借款信息）
     *
     * @param loanApplyId
     * @param dataDate
     * @return
     */
    LbHOlLoanInfoDo selectByKey(String loanApplyId, String dataDate);

    /**
     * 根据key删除网贷系统-历史表-借据信息（记录客户借款信息）
     *
     * @param loanApplyId
     * @param dataDate
     * @return
     */
    int deleteByKey(String loanApplyId, String dataDate);

    /**
     * 查询网贷系统-历史表-借据信息（记录客户借款信息）信息
     *
     * @param lbHOlLoanInfoQuery 条件
     * @return List<LbHOlLoanInfoDo>
     */
    List<LbHOlLoanInfoDo> selectByExample(LbHOlLoanInfoQuery lbHOlLoanInfoQuery);

    /**
     * 新增网贷系统-历史表-借据信息（记录客户借款信息）信息
     *
     * @param lbHOlLoanInfo 条件
     * @return int>
     */
    int insertBySelective(LbHOlLoanInfoDo lbHOlLoanInfo);

    /**
     * 修改网贷系统-历史表-借据信息（记录客户借款信息）信息
     *
     * @param lbHOlLoanInfo
     * @return
     */
    int updateBySelective(LbHOlLoanInfoDo lbHOlLoanInfo);

    /**
     * 修改网贷系统-历史表-借据信息（记录客户借款信息）信息
     *
     * @param lbHOlLoanInfo
     * @param lbHOlLoanInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbHOlLoanInfoDo lbHOlLoanInfo, LbHOlLoanInfoQuery lbHOlLoanInfoQuery);

    /**
     * 清空网贷系统-历史表-借据信息表中当前租户的所有数据
     * 用于批量处理前的数据清理
     *
     * @return 删除的记录数
     */
    int deleteAll();

    /**
     * 批量插入网贷系统-历史表-借据信息数据
     * 用于提高批量插入性能
     *
     * @param lbHOlLoanInfoList 借据信息列表
     * @return 插入的记录数
     */
    int insertList(List<LbHOlLoanInfoDo> lbHOlLoanInfoList);

    /**
     * 删除历史表-借据信息当前业务日期的数据
     * @param dataDate
     * @return
     */
    int deleteByDataDate(String dataDate);
}
