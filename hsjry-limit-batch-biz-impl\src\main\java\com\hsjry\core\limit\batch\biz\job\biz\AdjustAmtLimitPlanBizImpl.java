/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.biz;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.util.JobUtil;
import com.hsjry.core.limit.batch.biz.AmtLimitBiz;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/8 10:18
 */
@Service
@Slf4j
public class AdjustAmtLimitPlanBizImpl implements BaseOrdinaryBiz {
    @Autowired
    private AmtLimitBiz amtLimitBiz;

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.AMT_LIMIT_PLAN_ADJUST;
    }

    @Override
    public void execBaseJob(JobInitDto jobInitDto) {
        log.info("========执行限额调整批量开始========" + jobInitDto.getBusinessDate());
        amtLimitBiz.amtLimitPlanAdjust(JobUtil.getNum2Date(jobInitDto.getBusinessDate()));
        log.info("========执行限额调整批量结束========" + jobInitDto.getBusinessDate());
    }
}
