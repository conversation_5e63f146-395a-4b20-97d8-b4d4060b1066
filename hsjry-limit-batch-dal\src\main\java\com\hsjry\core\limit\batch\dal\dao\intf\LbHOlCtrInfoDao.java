package com.hsjry.core.limit.batch.dal.dao.intf;

import com.hsjry.core.limit.batch.dal.dao.model.LbHOlCtrInfoDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbHOlCtrInfoQuery;

import java.util.List;

import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 网贷系统-历史表-合同信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbHOlCtrInfoDao extends IBaseDao<LbHOlCtrInfoDo> {
    /**
     * 分页查询网贷系统-历史表-合同信息
     *
     * @param lbHOlCtrInfoQuery 条件
     * @return PageInfo<LbHOlCtrInfoDo>
     */
    PageInfo<LbHOlCtrInfoDo> selectPage(LbHOlCtrInfoQuery lbHOlCtrInfoQuery, PageParam pageParam);

    /**
     * 根据key查询网贷系统-历史表-合同信息
     *
     * @param contractId
     * @param dataDate
     * @return
     */
    LbHOlCtrInfoDo selectByKey(String contractId, String dataDate);

    /**
     * 根据key删除网贷系统-历史表-合同信息
     *
     * @param contractId
     * @param dataDate
     * @return
     */
    int deleteByKey(String contractId, String dataDate);

    /**
     * 查询网贷系统-历史表-合同信息信息
     *
     * @param lbHOlCtrInfoQuery 条件
     * @return List<LbHOlCtrInfoDo>
     */
    List<LbHOlCtrInfoDo> selectByExample(LbHOlCtrInfoQuery lbHOlCtrInfoQuery);

    /**
     * 新增网贷系统-历史表-合同信息信息
     *
     * @param lbHOlCtrInfo 条件
     * @return int>
     */
    int insertBySelective(LbHOlCtrInfoDo lbHOlCtrInfo);

    /**
     * 修改网贷系统-历史表-合同信息信息
     *
     * @param lbHOlCtrInfo
     * @return
     */
    int updateBySelective(LbHOlCtrInfoDo lbHOlCtrInfo);

    /**
     * 修改网贷系统-历史表-合同信息信息
     *
     * @param lbHOlCtrInfo
     * @param lbHOlCtrInfoQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbHOlCtrInfoDo lbHOlCtrInfo, LbHOlCtrInfoQuery lbHOlCtrInfoQuery);

    /**
     * 清空网贷系统-历史表-合同信息所有数据
     * 用于文件同步作业的全量数据刷新
     *
     * @return 删除的记录数
     */
    int deleteAll();

    /**
     * 删除历史表-合同信息当前业务日期的数据
     * @param dataDate
     * @return
     */
    int deleteByDataDate(String dataDate);
}
