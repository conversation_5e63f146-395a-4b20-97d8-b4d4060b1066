package com.hsjry.core.limit.batch.biz.job.sharding.biz.reclc;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.biz.job.sharding.biz.limit.AbstractShardingPrepareBiz;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTReclcCorpCoPrtnDao;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTReclcCorpCoPrtnLvlDao;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTReclcCorpComCrdtDao;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTReclcCorpCprsvCrdtDao;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTReclcCorpNoCrdtDao;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTReclcCorpPerReplyDao;
import com.hsjry.core.limit.batch.dal.dao.intf.LbTReclcCorpTotlDao;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitObjectInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.CustLimitObjectInfoQuery;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 额度中心-对公客户额度重算
 *
 * <AUTHOR>
 * @date 2025-01-16
 */
@Slf4j
@Service("lbTReclcCorpProcessImpl")
@RequiredArgsConstructor
public class LbTReclcCorpProcessImpl extends AbstractShardingPrepareBiz<CustLimitObjectInfoQuery>
    implements JobCoreBusiness<LcCustLimitObjectInfoDo> {

    private final LbTReclcCorpComCrdtDao lbTReclcCorpComCrdtDao;
    private final LbTReclcCorpCoPrtnDao lbTReclcCorpCoPrtnDao;
    private final LbTReclcCorpCoPrtnLvlDao lbTReclcCorpCoPrtnLvlDao;
    private final LbTReclcCorpCprsvCrdtDao lbTReclcCorpCprsvCrdtDao;
    private final LbTReclcCorpNoCrdtDao lbTReclcCorpNoCrdtDao;
    private final LbTReclcCorpPerReplyDao lbTReclcCorpPerReplyDao;
    private final LbTReclcCorpTotlDao lbTReclcCorpTotlDao;

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.LB_T_RECLC_CORP_PROCESS;
    }

    @Override
    public Integer selectCountByCurrentGroupFromDb(CustLimitObjectInfoQuery query) {
        return 0;
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobInitDto.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);
        log.info(prefixLog + "====================== 分片逻辑 start ================================================");
        List<JobShared> jobSharedList = new ArrayList<>();
        log.info(prefixLog + "====================== 分片逻辑 end ================================================");
        log.info(prefixLog + "对公客户额度重算分片任务生成完成,共{}个分片",
            CollectionUtil.isEmpty(jobSharedList) ? 0 : jobSharedList.size());
        return jobSharedList;
    }

    @Override
    public ShardingResult<LcCustLimitObjectInfoDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {

        Integer businessDate = jobInitDto.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]查询处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);

        log.info(prefixLog + "开始查询分片数据,分片号:{}", jobShared.getBatchNum());

        ShardingResult<LcCustLimitObjectInfoDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }
        // 创建查询条件
        CustLimitObjectInfoQuery query = CustLimitObjectInfoQuery.builder().tenantId(AppParamUtil.getTenantId())//
            .offset(jobShared.getOffset()).limit(jobShared.getLimit()).build();
        log.info(prefixLog + "=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");

        // 查询分片数据
        List<LcCustLimitObjectInfoDo> dataList = Lists.newArrayList();
        shardingResult.setShardingResultList(dataList);

        log.info("分片数据查询完成,分片号:{},数据量:{}", jobShared.getBatchNum(),
            CollectionUtil.isEmpty(dataList) ? 0 : dataList.size());

        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LcCustLimitObjectInfoDo> shardingResult) {
        List<LcCustLimitObjectInfoDo> shardingDataList = shardingResult.getShardingResultList();
        JobShared jobShared = shardingResult.getJobShared();
        Integer businessDate = jobShared.getBusinessDate();
        String jobTradeCode = getJobTrade().getCode();
        String jobTradeDesc = getJobTrade().getDescription();
        String batchSerialNo = jobShared.getBatchSerialNo();
        Integer batchNum = jobShared.getBatchNum();
        Integer dataSize = shardingResult.getShardingResultList().size();

        String prefixLog = String.format("营运日期:[%s]处理JobTrade:[%s-%s],批量流水号:[%s]执行处理分片数据,",
            businessDate, jobTradeCode, jobTradeDesc, batchSerialNo);
        log.info(prefixLog + "=========分片执行开始:[{}]数量为[{}]===========", batchNum, dataSize);

        try {
            LcSliceBatchSerialDo sliceBatchSerialDo = shardingResult.getLcSliceBatchSerialDo();

            // 1.处理一般授信额度重算
            log.info(prefixLog + "步骤1:开始处理一般授信额度重算");
            // 1.1.清空一般授信额度中间表
            int truncateCommonCount = lbTReclcCorpComCrdtDao.truncateCommonCreditLimit();
            log.info(prefixLog + "步骤1.1:清空一般授信额度中间表完成,影响记录数:[{}]", truncateCommonCount);

            // 1.2.插入一般授信额度客户编号和额度编号
            int insertCommonCount = lbTReclcCorpComCrdtDao.insertCommonCreditLimit();
            log.info(prefixLog + "步骤1.2:插入一般授信额度客户编号和额度编号完成,影响记录数:[{}]", insertCommonCount);

            // 1.3.更新一般授信额度中间表金额信息
            int mergeCommonAmountCount = lbTReclcCorpComCrdtDao.mergeCommonCreditLimitAmount();
            log.info(prefixLog + "步骤1.3:更新一般授信额度中间表金额信息完成,影响记录数:[{}]", mergeCommonAmountCount);

            // 1.4.更新额度实例金额信息
            int mergeCommonInstanceCount = lbTReclcCorpComCrdtDao.mergeCommonCreditLimitInstance();
            log.info(prefixLog + "步骤1.4:更新额度实例金额信息完成,影响记录数:[{}]", mergeCommonInstanceCount);

            // 2.处理综合授信额度重算
            log.info(prefixLog + "步骤2:开始处理综合授信额度重算");
            // 2.1.清空综合授信额度中间表
            int truncateComprehensiveCount = lbTReclcCorpCprsvCrdtDao.truncateComprehensiveCreditLimit();
            log.info(prefixLog + "步骤2.1:清空综合授信额度中间表完成,影响记录数:[{}]", truncateComprehensiveCount);

            // 2.2.插入综合授信额度客户编号和额度编号
            int insertComprehensiveCount = lbTReclcCorpCprsvCrdtDao.insertComprehensiveCreditLimit();
            log.info(prefixLog + "步骤2.2:插入综合授信额度客户编号和额度编号完成,影响记录数:[{}]",
                insertComprehensiveCount);

            // 2.3.更新综合授信额度中间表金额信息
            int mergeComprehensiveAmountCount = lbTReclcCorpCprsvCrdtDao.mergeComprehensiveCreditLimitAmount();
            log.info(prefixLog + "步骤2.3:更新综合授信额度中间表金额信息完成,影响记录数:[{}]",
                mergeComprehensiveAmountCount);

            // 2.4.更新额度实例金额信息
            int mergeComprehensiveInstanceCount = lbTReclcCorpCprsvCrdtDao.mergeComprehensiveCreditLimitInstance();
            log.info(prefixLog + "步骤2.4:更新额度实例金额信息完成,影响记录数:[{}]", mergeComprehensiveInstanceCount);

            // 3.处理单笔单批额度重算
            log.info(prefixLog + "步骤3:开始处理单笔单批额度重算");
            // 3.1.清空单笔单批额度中间表
            int truncateSingleBatchCount = lbTReclcCorpPerReplyDao.truncateSingleBatchLimit();
            log.info(prefixLog + "步骤3.1:清空单笔单批额度中间表完成,影响记录数:[{}]", truncateSingleBatchCount);

            // 3.2.插入单笔单批额度客户编号和额度编号
            int insertSingleBatchCount = lbTReclcCorpPerReplyDao.insertSingleBatchLimit();
            log.info(prefixLog + "步骤3.2:插入单笔单批额度客户编号和额度编号完成,影响记录数:[{}]",
                insertSingleBatchCount);

            // 3.3.更新单笔单批额度中间表金额信息
            int mergeSingleBatchAmountCount = lbTReclcCorpPerReplyDao.mergeSingleBatchLimitAmount();
            log.info(prefixLog + "步骤3.3:更新单笔单批额度中间表金额信息完成,影响记录数:[{}]",
                mergeSingleBatchAmountCount);

            // 3.4.更新额度实例金额信息
            int mergeSingleBatchInstanceCount = lbTReclcCorpPerReplyDao.mergeSingleBatchLimitInstance();
            log.info(prefixLog + "步骤3.4:更新额度实例金额信息完成,影响记录数:[{}]", mergeSingleBatchInstanceCount);

            // 4.处理合作方层额度重算
            log.info(prefixLog + "步骤4:开始处理合作方层额度重算");
            // 4.1.清空合作方层额度中间表
            int truncateCoPartnerLevelCount = lbTReclcCorpCoPrtnLvlDao.truncateCoPartnerLevelLimit();
            log.info(prefixLog + "步骤4.1:清空合作方层额度中间表完成,影响记录数:[{}]", truncateCoPartnerLevelCount);

            // 4.2.插入合作方层额度客户编号和额度编号
            int insertCoPartnerLevelCount = lbTReclcCorpCoPrtnLvlDao.insertCoPartnerLevelLimit();
            log.info(prefixLog + "步骤4.2:插入合作方层额度客户编号和额度编号完成,影响记录数:[{}]",
                insertCoPartnerLevelCount);

            // 4.3.更新合作方层额度中间表金额信息
            int mergeCoPartnerLevelAmountCount = lbTReclcCorpCoPrtnLvlDao.mergeCoPartnerLevelLimitAmount();
            log.info(prefixLog + "步骤4.3:更新合作方层额度中间表金额信息完成,影响记录数:[{}]",
                mergeCoPartnerLevelAmountCount);

            // 4.4.更新额度实例金额信息
            int mergeCoPartnerLevelInstanceCount = lbTReclcCorpCoPrtnLvlDao.mergeCoPartnerLevelLimitInstance();
            log.info(prefixLog + "步骤4.4:更新额度实例金额信息完成,影响记录数:[{}]", mergeCoPartnerLevelInstanceCount);

            // 5.处理合作方额度重算
            log.info(prefixLog + "步骤5:开始处理合作方额度重算");
            // 5.1.清空合作方额度中间表
            int truncateCoPartnerCount = lbTReclcCorpCoPrtnDao.truncateCoPartnerLimit();
            log.info(prefixLog + "步骤5.1:清空合作方额度中间表完成,影响记录数:[{}]", truncateCoPartnerCount);

            // 5.2.插入合作方额度客户编号和额度编号
            int insertCoPartnerCount = lbTReclcCorpCoPrtnDao.insertCoPartnerLimit();
            log.info(prefixLog + "步骤5.2:插入合作方额度客户编号和额度编号完成,影响记录数:[{}]", insertCoPartnerCount);

            // 5.3.更新合作方额度中间表金额信息
            int mergeCoPartnerAmountCount = lbTReclcCorpCoPrtnDao.mergeCoPartnerLimitAmount();
            log.info(prefixLog + "步骤5.3:更新合作方额度中间表金额信息完成,影响记录数:[{}]", mergeCoPartnerAmountCount);

            // 5.4.更新额度实例金额信息
            int mergeCoPartnerInstanceCount = lbTReclcCorpCoPrtnDao.mergeCoPartnerLimitInstance();
            log.info(prefixLog + "步骤5.4:更新额度实例金额信息完成,影响记录数:[{}]", mergeCoPartnerInstanceCount);

            // 6.处理非授信额度重算
            log.info(prefixLog + "步骤6:开始处理非授信额度重算");
            // 6.1.清空非授信额度中间表
            int truncateNoCreditCount = lbTReclcCorpNoCrdtDao.truncateNoCreditLimit();
            log.info(prefixLog + "步骤6.1:清空非授信额度中间表完成,影响记录数:[{}]", truncateNoCreditCount);

            // 6.2.插入非授信额度客户编号和额度编号
            int insertNoCreditCount = lbTReclcCorpNoCrdtDao.insertNoCreditLimit();
            log.info(prefixLog + "步骤6.2:插入非授信额度客户编号和额度编号完成,影响记录数:[{}]", insertNoCreditCount);

            // 6.3.更新非授信额度中间表金额信息
            int mergeNoCreditAmountCount = lbTReclcCorpNoCrdtDao.mergeNoCreditLimitAmount();
            log.info(prefixLog + "步骤6.3:更新非授信额度中间表金额信息完成,影响记录数:[{}]", mergeNoCreditAmountCount);

            // 6.4.更新额度实例金额信息
            int mergeNoCreditInstanceCount = lbTReclcCorpNoCrdtDao.mergeNoCreditLimitInstance();
            log.info(prefixLog + "步骤6.4:更新额度实例金额信息完成,影响记录数:[{}]", mergeNoCreditInstanceCount);

            // 7.处理客户总额度重算
            log.info(prefixLog + "步骤7:开始处理客户总额度重算");
            // 7.1.清空客户总额度中间表
            int truncateTotalCount = lbTReclcCorpTotlDao.truncateTotalLimit();
            log.info(prefixLog + "步骤7.1:清空客户总额度中间表完成,影响记录数:[{}]", truncateTotalCount);

            // 7.2.插入客户总额度客户编号和额度编号
            int insertTotalCount = lbTReclcCorpTotlDao.insertTotalLimit();
            log.info(prefixLog + "步骤7.2:插入客户总额度客户编号和额度编号完成,影响记录数:[{}]", insertTotalCount);

            // 7.3.更新客户总额度中间表金额信息
            int mergeTotalAmountCount = lbTReclcCorpTotlDao.mergeTotalLimitAmount();
            log.info(prefixLog + "步骤7.3:更新客户总额度中间表金额信息完成,影响记录数:[{}]", mergeTotalAmountCount);

            // 7.4.更新额度实例金额信息
            int mergeTotalInstanceCount = lbTReclcCorpTotlDao.mergeTotalLimitInstance();
            log.info(prefixLog + "步骤7.4:更新额度实例金额信息完成,影响记录数:[{}]", mergeTotalInstanceCount);

            // 更新分片流水成功
            normalUpdateSliceSerial(0, sliceBatchSerialDo);

        } catch (Exception e) {
            log.error(prefixLog + "分片执行异常:[{}]", batchNum, e);
            throw new HsjryBizException(EnumBatchJobError.SYSTEM_ERR.getCode(),
                EnumBatchJobError.SYSTEM_ERR.getDescription());
        }

        log.info(prefixLog + "=========分片执行结束:[{}]===========", batchNum);
    }
}
