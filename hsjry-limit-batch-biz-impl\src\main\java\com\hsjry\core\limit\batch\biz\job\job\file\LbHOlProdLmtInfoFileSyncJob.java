package com.hsjry.core.limit.batch.biz.job.job.file;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * 网贷系统历史表产品额度信息文件同步处理任务
 * 负责H_OL_PROD_LMT_INFO文件的批量同步处理
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/21 16:30
 */
@Slf4j
@Service("lbHOlProdLmtInfoFileSyncJob")
public class LbHOlProdLmtInfoFileSyncJob extends AbstractBaseBatchJob {
    
    public LbHOlProdLmtInfoFileSyncJob() {
        log.info("LbHOlProdLmtInfoFileSyncJob Bean初始化完成");
    }

    @Autowired
    @Qualifier("lbHOlProdLmtInfoFileSyncBizImpl")
    private BaseOrdinaryBiz lbHOlProdLmtInfoFileSyncBizImpl;

    @Override
    public BaseOrdinaryBiz getBaseOrdinaryBiz() {
        return lbHOlProdLmtInfoFileSyncBizImpl;
    }
} 