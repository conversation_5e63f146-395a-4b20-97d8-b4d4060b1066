package com.hsjry.core.limit.batch.biz.convert.copy;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.hsjry.core.limit.batch.dal.dao.model.LbCEntityInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcEntityInfoDo;
import com.hsjry.lang.common.utils.CollectionUtil;

/**
 * 实体信息转换器
 *
 * <AUTHOR>
 * @date 2025-07-09 12:31:05
 */
public class LbCEntityInfoConverter {

    /**
     * 源DO转换为目标DO（主要转换方法）
     * 从LcEntityInfoDo转换为LbCEntityInfoDo
     *
     * @param model 源DO对象
     * @return 目标DO对象
     */
    public static LbCEntityInfoDo do2Copy(LcEntityInfoDo model) {
        if (model == null) {
            return null;
        }
        return LbCEntityInfoCnvs.INSTANCE.do2Copy(model);
    }

    /**
     * 源DO列表转换为目标DO列表
     * 从LcEntityInfoDo列表转换为LbCEntityInfoDo列表
     *
     * @param sourceList 源DO列表
     * @return 目标DO列表
     */
    public static List<LbCEntityInfoDo> doList2CopyList(List<LcEntityInfoDo> sourceList) {
        if (CollectionUtil.isEmpty(sourceList)) {
            return Collections.emptyList();
        }
        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (sourceList.size() / 0.75f) + 1, 16);
        return sourceList.parallelStream().map(LbCEntityInfoConverter::do2Copy)
            .collect(Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
    }
}