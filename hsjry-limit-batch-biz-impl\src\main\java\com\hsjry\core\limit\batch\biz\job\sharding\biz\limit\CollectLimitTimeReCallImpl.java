package com.hsjry.core.limit.batch.biz.job.sharding.biz.limit;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.limit.EnumCustLimitStatus;
import com.hsjry.base.common.model.enums.limit.EnumLimitGrantType;
import com.hsjry.base.common.model.enums.limit.EnumLimitLevel;
import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitInfoBatchDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitRelationBatchDao;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitRelationDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitInfoQuery;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitRelationQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.stereotype.enums.EnumBool;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class CollectLimitTimeReCallImpl extends AbstractShardingPrepareBiz<LcCustLimitInfoQuery>
    implements JobCoreBusiness<LcCustLimitInfoDo> {
    @Autowired
    private LcCustLimitInfoBatchDao lcCustLimitInfoDao;
    @Autowired
    private LcCustLimitRelationBatchDao lcCustLimitRelationDao;
    /** 对公总额度节点编号 */
    @Value("${total.node.id:DGLN0001}")
    private String totalNodeId;
    /** 统一授信额度（集团客户） */
    @Value("${jt.credit.limit.node.id:JTLN0002}")
    private String jtCreditLimitNodeId;
    /** 统一授信额度（单一对公客户） */
    @Value("${dg.credit.limit.node.id:DGLN0002}")
    private String dgCreditLimitNodeId;

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.COLLECT_LIMIT_TIME_RE_CALL;
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        log.info("====================== 接入业务{}分片逻辑 start ================================================",
            getJobTrade().getDescription());
        List<JobShared> jobSharedList = new ArrayList<>();
        //sql 批处理数量 暂定为分片数量，不放大
        Integer batchFixNum = jobInitDto.getFixNum();
        //当前分组的最大值，为下次 批处理的最小值
        LcCustLimitInfoDo maxDo = new LcCustLimitInfoDo();
        //构造查询条件 查询当前 分批处理的 排序 最大 对象
        LcCustLimitInfoQuery query = LcCustLimitInfoQuery.builder()
            .offset(batchFixNum - 1)
            .limit(1)
            .limitLevel(EnumLimitLevel.ROOT.getCode())
            .contractLimitFlag(EnumBool.NO.getCode())
            .tenantId(AppParamUtil.getTenantId())
            .build();
        //分片流水
        int batchNum = 0;
        while (maxDo != null) {
            query.setCustLimitId(maxDo.getCustLimitId());
            maxDo = lcCustLimitInfoDao.selectFirstOne(query);
            //统计分片 数量
            batchNum = countBatchNum(batchFixNum, query, maxDo, batchNum, jobInitDto, jobSharedList,
                query.getLimitObjectId(), false);
        }
        log.info("====================== 接入业务{}分片逻辑 end ================================================",
            getJobTrade().getDescription());
        return jobSharedList;
    }

    @Override
    public ShardingResult<LcCustLimitInfoDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        ShardingResult<LcCustLimitInfoDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }
        //原始查询条件
        LcCustLimitInfoQuery limitInfoQuery = GsonUtil.json2Obj(jobShared.getExtParam(), LcCustLimitInfoQuery.class);
        Integer batchFixNum = jobInitDto.getFixNum();
        LcCustLimitInfoQuery query = LcCustLimitInfoQuery.builder()
            .offset(batchFixNum - 1)
            .limit(jobShared.getLimit())
            .custLimitId(limitInfoQuery.getCustLimitId())
            .limitLevel(limitInfoQuery.getLimitLevel())
            .contractLimitFlag(limitInfoQuery.getContractLimitFlag())
            .tenantId(limitInfoQuery.getTenantId())
            .build();
        log.info("=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");
        List<LcCustLimitInfoDo> list = lcCustLimitInfoDao.selectShardList(query);
        shardingResult.setShardingResultList(list);
        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LcCustLimitInfoDo> shardingResult) {
        log.info("=========分片执行开始:[{}]===========", shardingResult.getJobShared().getBatchNum());
        List<LcCustLimitInfoDo> shardingResultList = shardingResult.getShardingResultList();
        if (CollectionUtils.isEmpty(shardingResultList)) {
            log.info("=========分片执行结束:" + shardingResult.getJobShared().getBatchNum() + "数量为空=========");
            return;
        }
        //计划分组
        for (LcCustLimitInfoDo rootNode : shardingResultList) {
            if (Objects.isNull(rootNode)) {
                log.info("lcCustLimitInfoDo对象为空跳过");
                continue;
            }
            String limitTemplateId = rootNode.getLimitTemplateId();
            String limitObjectId = rootNode.getLimitObjectId();
            if (StringUtil.isEmpty(limitObjectId)) {
                log.info("[{}]额度objectId为空跳过", rootNode.getCustLimitId());
                continue;
            }
            if (StringUtil.isEmpty(limitTemplateId)) {
                log.info("[{}]额度limitTemplateId为空跳过", rootNode.getCustLimitId());
                continue;
            }
            List<LcCustLimitInfoDo> groupLcCustLimitInfoDoList = lcCustLimitInfoDao.selectByExample(
                LcCustLimitInfoQuery.builder()
                    .limitObjectId(limitObjectId)
                    .limitTemplateId(limitTemplateId)
                    .contractLimitFlag("N")
                    .build());
            if (CollectionUtil.isEmpty(groupLcCustLimitInfoDoList)) {
                log.info("额度[{}]分组为空,跳过", rootNode.getCustLimitId());
                continue;
            }
            List<String> groupCustLimitIdList = groupLcCustLimitInfoDoList.stream().map(
                LcCustLimitInfoDo::getCustLimitId).collect(Collectors.toList());

            List<LcCustLimitRelationDo> lcCustLimitRelationDos = lcCustLimitRelationDao.selectByExample(
                LcCustLimitRelationQuery.builder().parentNodeLimitIdList(groupCustLimitIdList).build());

            List<LcCustLimitInfoDo> updateLcCustLimitInfoDos = doCalCollectLimit(groupLcCustLimitInfoDoList,
                lcCustLimitRelationDos, rootNode.getCustLimitId());
            if (CollectionUtils.isEmpty(updateLcCustLimitInfoDos)) {
                continue;
            }
            lcCustLimitInfoDao.updateByPrimaryKeySelectiveList(updateLcCustLimitInfoDos);
        }
        //更新分片流水成功
        normalUpdateSliceSerial(shardingResultList.size(), shardingResult.getLcSliceBatchSerialDo());
        log.info("=========分片执行结束:[{}]数量为[{}]===========", shardingResult.getJobShared().getBatchNum(),
            shardingResultList.size());
    }

    /**
     * 计算汇总到期日
     *
     * @return
     */
    private List<LcCustLimitInfoDo> doCalCollectLimit(List<LcCustLimitInfoDo> custLimitDoList,
        List<LcCustLimitRelationDo> lcCustLimitRelationDos, String rootNodeCustLimitId) {
        Map<String, LcCustLimitInfoDo> custLimitBoMap = custLimitDoList.stream().collect(
            Collectors.toMap(LcCustLimitInfoDo::getCustLimitId, Function.identity()));
        // 父子关系map
        Map<String, List<String>> sonRelationMap = lcCustLimitRelationDos.stream().collect(
            Collectors.groupingBy(LcCustLimitRelationDo::getParentNodeLimitId,
                Collectors.mapping(LcCustLimitRelationDo::getCurrentNodeLimitId, Collectors.toList())));
        return doCalCollectLimitTree(sonRelationMap, rootNodeCustLimitId, custLimitBoMap);
    }

    private List<LcCustLimitInfoDo> doCalCollectLimitTree(Map<String, List<String>> sonRelationMap, String limitId,
        Map<String, LcCustLimitInfoDo> custLimitBoMap) {
        List<LcCustLimitInfoDo> list = new ArrayList<>();
        doCalCollectLimitTree(sonRelationMap, limitId, list, custLimitBoMap);
        return list;
    }

    private void doCalCollectLimitTree(Map<String, List<String>> sonRelationMap, String currentLimitId,
        List<LcCustLimitInfoDo> resultList, Map<String, LcCustLimitInfoDo> custLimitBoMap) {
        log.info("遍历树计算汇总节点到期截止日,开始进入节点[{}]", currentLimitId);
        List<String> childLimitIdList = Optional.ofNullable(sonRelationMap.get(currentLimitId)).orElseGet(
            ArrayList::new);
        for (String childLimitId : childLimitIdList) {
            doCalCollectLimitTree(sonRelationMap, childLimitId, resultList, custLimitBoMap);
        }
        LcCustLimitInfoDo custLimitBo = custLimitBoMap.get(currentLimitId);
        if (Objects.isNull(custLimitBo)) {
            log.info("遍历树计算汇总节点到期截止日,[{}]当前节点为合同节点,或者不在树上,跳过", currentLimitId);
            return;
        }
        if (EnumBool.YES.getCode().equals(custLimitBo.getContractLimitFlag())) {
            log.info("遍历树计算汇总节点到期截止日,[{}]当前节点为合同节点,返回空值", currentLimitId);
            return;
        }
        if (EnumLimitLevel.ROOT.getCode().equals(custLimitBo.getLimitLevel())) {
            log.info("遍历树计算汇总节点到期截止日,[{}]当前节点类型为根节点,返回空值", currentLimitId);
            return;
        }
        List<String> skipNodeCodeList = Lists.newArrayList(dgCreditLimitNodeId, jtCreditLimitNodeId, totalNodeId);
        if (skipNodeCodeList.contains(custLimitBo.getTemplateNodeId())) {
            // custLimitBo.setEffectiveEndTime(LimitCenterConstants.DEFAULT_EFFECTIVE_END_TIME);
            LcCustLimitInfoDo custLimitInfoDo = new LcCustLimitInfoDo();
            custLimitInfoDo.setCustLimitId(custLimitBo.getCustLimitId());
            custLimitInfoDo.setTenantId(AppParamUtil.getTenantId());
            custLimitInfoDo.setUpdateTime(BusinessDateUtil.getDate());
            // custLimitInfoDo.setEffectiveEndTime(LimitCenterConstants.DEFAULT_EFFECTIVE_END_TIME);
            // log.info("遍历树计算汇总节点到期截止日,[{}]节点为默认值节点节点,返回时间[{}]",currentLimitId,LimitCenterConstants.DEFAULT_EFFECTIVE_END_TIME);
            resultList.add(custLimitInfoDo);
            return;
        }
        if (EnumLimitGrantType.COLLECT.getCode().equals(custLimitBo.getLimitGrantType())) {
            Date longestEffectiveEndTime = null;
            for (String childLimitId : childLimitIdList) {
                LcCustLimitInfoDo lcCustLimitInfoDo = custLimitBoMap.get(childLimitId);
                if (Objects.isNull(lcCustLimitInfoDo)) {
                    log.info("遍历树计算汇总节点到期截止日,对象为空跳过");
                    continue;
                }
                List<String> ableStatus = Lists.newArrayList(EnumCustLimitStatus.INEFFECTIVE.getCode(),
                    EnumCustLimitStatus.VALID.getCode(), EnumCustLimitStatus.FROZEN.getCode(),
                    EnumCustLimitStatus.BREAK.getCode(), EnumCustLimitStatus.EXPIRE.getCode());
                if (!ableStatus.contains(lcCustLimitInfoDo.getLimitStatus())) {
                    log.info("遍历树计算汇总节点到期截止日,子节点[{}]状态[{}]异常,不参与最长时间的计算",
                        lcCustLimitInfoDo.getCustLimitId(), lcCustLimitInfoDo.getLimitStatus());
                    continue;
                }
                Date effectiveEndTime = lcCustLimitInfoDo.getEffectiveEndTime();
                if (Objects.isNull(longestEffectiveEndTime)) {
                    longestEffectiveEndTime = effectiveEndTime;
                } else if (Objects.isNull(effectiveEndTime)) {
                    longestEffectiveEndTime = longestEffectiveEndTime;
                } else if (longestEffectiveEndTime.compareTo(effectiveEndTime) < 0) {
                    longestEffectiveEndTime = effectiveEndTime;
                }
            }
            custLimitBo.setEffectiveEndTime(longestEffectiveEndTime);
            LcCustLimitInfoDo custLimitInfoDo = new LcCustLimitInfoDo();
            custLimitInfoDo.setCustLimitId(custLimitBo.getCustLimitId());
            custLimitInfoDo.setTenantId(AppParamUtil.getTenantId());
            custLimitInfoDo.setUpdateTime(BusinessDateUtil.getDate());
            custLimitInfoDo.setEffectiveEndTime(longestEffectiveEndTime);
            log.info("遍历树计算汇总节点到期截止日,[{}]节点未汇总节点,返回最长时间[{}]", currentLimitId,
                longestEffectiveEndTime);
            resultList.add(custLimitInfoDo);
            return;
        }
        log.info("遍历树计算汇总节点到期截止日,[{}]节点进入结束", currentLimitId);
    }

    @Override
    public Integer selectCountByCurrentGroupFromDb(LcCustLimitInfoQuery query) {
        return lcCustLimitInfoDao.selectCountByCurrentGroup(query);
    }
}
