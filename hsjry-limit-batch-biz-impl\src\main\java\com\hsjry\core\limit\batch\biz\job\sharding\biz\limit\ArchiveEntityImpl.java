package com.hsjry.core.limit.batch.biz.job.sharding.biz.limit;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.model.enums.limit.EnumLimitEntityStatus;
import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.center.dal.dao.intf.EntityInfoBatchDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcAmtLimitRecordSerialDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcCustLimitOperateSerialDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcEntityAmtLimitRelDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcEntityDimensionDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcEntityHistoryInfoDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcEntityInfoDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcEntityOperateSerialDao;
import com.hsjry.core.limit.center.dal.dao.intf.LcEntityStatisticSerialDao;
import com.hsjry.core.limit.center.dal.dao.model.LcAmtLimitRecordSerialDo;
import com.hsjry.core.limit.center.dal.dao.model.LcCustLimitOperateSerialDo;
import com.hsjry.core.limit.center.dal.dao.model.LcEntityAmtLimitRelDo;
import com.hsjry.core.limit.center.dal.dao.model.LcEntityDimensionDo;
import com.hsjry.core.limit.center.dal.dao.model.LcEntityHistoryInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcEntityInfoDo;
import com.hsjry.core.limit.center.dal.dao.model.LcEntityOperateSerialDo;
import com.hsjry.core.limit.center.dal.dao.model.LcEntityStatisticSerialDo;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.EntityInfoBatchQuery;
import com.hsjry.core.limit.center.dal.dao.query.LcAmtLimitRecordSerialQuery;
import com.hsjry.core.limit.center.dal.dao.query.LcCustLimitOperateSerialQuery;
import com.hsjry.core.limit.center.dal.dao.query.LcEntityAmtLimitRelQuery;
import com.hsjry.core.limit.center.dal.dao.query.LcEntityDimensionQuery;
import com.hsjry.core.limit.center.dal.dao.query.LcEntityInfoQuery;
import com.hsjry.core.limit.center.dal.dao.query.LcEntityOperateSerialQuery;
import com.hsjry.core.limit.center.dal.dao.query.LcEntityStatisticSerialQuery;
import com.hsjry.lang.business.date.BusinessDateUtil;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.extern.slf4j.Slf4j;
/**
 * 实体归档
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0 2023/3/22 14:21
 */
@Service
@Slf4j
public class ArchiveEntityImpl extends AbstractShardingPrepareBiz<EntityInfoBatchQuery>
    implements JobCoreBusiness<LcEntityInfoDo> {
    @Autowired
    private EntityInfoBatchDao entityInfoBatchDao;
    @Autowired
    private LcEntityInfoDao lcEntityInfoDao;
    @Autowired
    private LcEntityAmtLimitRelDao lcEntityAmtLimitRelDao;
    @Autowired
    private LcEntityDimensionDao lcEntityDimensionDao;
    @Autowired
    private LcEntityOperateSerialDao lcEntityOperateSerialDao;
    @Autowired
    private LcEntityStatisticSerialDao lcEntityStatisticSerialDao;
    @Autowired
    private LcAmtLimitRecordSerialDao lcAmtLimitRecordSerialDao;
    @Autowired
    private LcCustLimitOperateSerialDao lcCustLimitOperateSerialDao;
    @Autowired
    private LcEntityHistoryInfoDao lcEntityHistoryInfoDao;
    @Autowired
    @Qualifier("limitTransactionNewTemplate")
    private TransactionTemplate transactionTemplate;

    @Override
    public Integer selectCountByCurrentGroupFromDb(EntityInfoBatchQuery query) {
        return entityInfoBatchDao.selectCountByCurrentGroup(query);
    }

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.ENTITY_ARCHIVE;
    }

    @Override
    public ShardingResult<LcEntityInfoDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
        JobInitDto jobInitDto, JobShared jobShared) {
        ShardingResult<LcEntityInfoDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo, jobShared);
        if (StringUtil.isBlank(jobShared.getExtParam())) {
            return shardingResult;
        }
        //原始查询条件
        EntityInfoBatchQuery entityInfoBatchQuery = GsonUtil.json2Obj(jobShared.getExtParam(),
            EntityInfoBatchQuery.class);
        EntityInfoBatchQuery query = EntityInfoBatchQuery.builder()
            .offset(jobShared.getOffset())
            .limit(jobShared.getLimit())
            .statusList(entityInfoBatchQuery.getStatusList())
            .build();
        log.info("=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");
        List<LcEntityInfoDo> list = entityInfoBatchDao.selectShardList(query);
        shardingResult.setShardingResultList(list);
        return shardingResult;
    }

    @Override
    public void execJobCoreBusiness(ShardingResult<LcEntityInfoDo> shardingResult) {
        List<LcEntityInfoDo> lcEntityInfoDoList = shardingResult.getShardingResultList();
        if (CollectionUtils.isEmpty(lcEntityInfoDoList)) {
            log.info("=========分片执行结束:" + shardingResult.getJobShared()
                .getBatchNum() + "数量为空=========");
            return;
        }
        handle(lcEntityInfoDoList);
    }

    /**
     * 额度处理
     *
     * @param lcEntityInfoDoList
     */
    private void handle(List<LcEntityInfoDo> lcEntityInfoDoList) {
        transactionTemplate.execute(transactionStatus -> {
            List<String> entityIdList = lcEntityInfoDoList.stream()
                .map(LcEntityInfoDo::getEntityId)
                .collect(Collectors.toList());
            Map<String, LcEntityInfoDo> entityInfoMap = lcEntityInfoDoList.stream()
                .collect(Collectors.toMap(LcEntityInfoDo::getEntityId, o -> o));
            List<LcEntityAmtLimitRelDo> lcEntityAmtLimitRelDoList = lcEntityAmtLimitRelDao.selectByExample(
                LcEntityAmtLimitRelQuery.builder()
                    .entityIdList(entityIdList)
                    .build());
            Map<String, List<LcEntityAmtLimitRelDo>> entityAmtLimitRelMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(lcEntityAmtLimitRelDoList)) {
                entityAmtLimitRelMap = lcEntityAmtLimitRelDoList.stream()
                    .collect(Collectors.groupingBy(LcEntityAmtLimitRelDo::getEntityId));
            }
            List<LcEntityDimensionDo> lcEntityDimensionDoList = lcEntityDimensionDao.selectByExample(
                LcEntityDimensionQuery.builder()
                    .entityIdList(entityIdList)
                    .build());
            Map<String, List<LcEntityDimensionDo>> entityDimensionMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(lcEntityDimensionDoList)) {
                entityDimensionMap = lcEntityDimensionDoList.stream()
                    .collect(Collectors.groupingBy(LcEntityDimensionDo::getEntityId));
            }
            List<LcEntityOperateSerialDo> lcEntityOperateSerialDoList = lcEntityOperateSerialDao.selectByExample(
                LcEntityOperateSerialQuery.builder()
                    .entityIdList(entityIdList)
                    .build());
            Map<String, List<LcEntityOperateSerialDo>> entityOperateSeriaMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(lcEntityOperateSerialDoList)) {
                entityOperateSeriaMap = lcEntityOperateSerialDoList.stream()
                    .collect(Collectors.groupingBy(LcEntityOperateSerialDo::getEntityId));
            }
            List<LcEntityStatisticSerialDo> lcEntityStatisticSerialDoList = lcEntityStatisticSerialDao.selectByExample(
                LcEntityStatisticSerialQuery.builder()
                    .entityIdList(entityIdList)
                    .build());
            Map<String, List<LcEntityStatisticSerialDo>> entityStatisticSerialMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(lcEntityStatisticSerialDoList)) {
                entityStatisticSerialMap = lcEntityStatisticSerialDoList.stream()
                    .collect(Collectors.groupingBy(LcEntityStatisticSerialDo::getEntityId));
            }
            List<LcAmtLimitRecordSerialDo> lcAmtLimitRecordSerialDoList = lcAmtLimitRecordSerialDao.selectByExample(
                LcAmtLimitRecordSerialQuery.builder()
                    .entityIdList(entityIdList)
                    .build());
            Map<String, List<LcAmtLimitRecordSerialDo>> amtLimitRecordSerialMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(lcAmtLimitRecordSerialDoList)) {
                amtLimitRecordSerialMap = lcAmtLimitRecordSerialDoList.stream()
                    .collect(Collectors.groupingBy(LcAmtLimitRecordSerialDo::getEntityId));
            }
            List<LcCustLimitOperateSerialDo> lcCustLimitOperateSerialDoList
                = lcCustLimitOperateSerialDao.selectByExample(LcCustLimitOperateSerialQuery.builder()
                .entityIdList(entityIdList)
                .build());
            Map<String, List<LcCustLimitOperateSerialDo>> custLimitOperateSerialMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(lcCustLimitOperateSerialDoList)) {
                custLimitOperateSerialMap = lcCustLimitOperateSerialDoList.stream()
                    .collect(Collectors.groupingBy(LcCustLimitOperateSerialDo::getEntityId));
            }
            List<LcEntityHistoryInfoDo> lcEntityHistoryInfoDoList = Lists.newArrayList();
            for (String entityId : entityIdList) {
                LcEntityHistoryInfoDo lcEntityHistoryInfoDo = new LcEntityHistoryInfoDo();
                lcEntityHistoryInfoDo.setEntityInfo(GsonUtil.obj2Json(entityInfoMap.get(entityId)));
                lcEntityHistoryInfoDo.setEntityAmtLimitRel(GsonUtil.obj2Json(entityAmtLimitRelMap.get(entityId)));
                lcEntityHistoryInfoDo.setEntityDimension(GsonUtil.obj2Json(entityDimensionMap.get(entityId)));
                lcEntityHistoryInfoDo.setEntityOperateSerial(GsonUtil.obj2Json(entityOperateSeriaMap.get(entityId)));
                lcEntityHistoryInfoDo.setEntityStatisticSerial(
                    GsonUtil.obj2Json(entityStatisticSerialMap.get(entityId)));
                lcEntityHistoryInfoDo.setAmtLimitRecordSerial(GsonUtil.obj2Json(amtLimitRecordSerialMap.get(entityId)));
                lcEntityHistoryInfoDo.setCustLimitOperateSerial(
                    GsonUtil.obj2Json(custLimitOperateSerialMap.get(entityId)));
                lcEntityHistoryInfoDo.setEntityId(entityId);
                lcEntityHistoryInfoDo.setCreateTime(BusinessDateUtil.getDate());
                lcEntityHistoryInfoDo.setUpdateTime(BusinessDateUtil.getDate());
                lcEntityHistoryInfoDo.setTenantId(AppParamUtil.getTenantId());
                lcEntityHistoryInfoDoList.add(lcEntityHistoryInfoDo);
            }
            lcEntityHistoryInfoDao.insertList(lcEntityHistoryInfoDoList);
            deleteEntity(entityIdList);
            return true;
        });
    }

    /**
     * 删除实体信息
     *
     * @param entityIdList
     */
    private void deleteEntity(List<String> entityIdList) {
        lcEntityInfoDao.deleteByExample(LcEntityInfoQuery.builder()
            .entityIdList(entityIdList)
            .build());
        lcEntityAmtLimitRelDao.deleteByExample(LcEntityAmtLimitRelQuery.builder()
            .entityIdList(entityIdList)
            .build());
        lcEntityDimensionDao.deleteByExample(LcEntityDimensionQuery.builder()
            .entityIdList(entityIdList)
            .build());
        lcEntityOperateSerialDao.deleteByExample(LcEntityOperateSerialQuery.builder()
            .entityIdList(entityIdList)
            .build());
        lcEntityStatisticSerialDao.deleteByExample(LcEntityStatisticSerialQuery.builder()
            .entityIdList(entityIdList)
            .build());
        lcAmtLimitRecordSerialDao.deleteByExample(LcAmtLimitRecordSerialQuery.builder()
            .entityIdList(entityIdList)
            .build());
        lcCustLimitOperateSerialDao.deleteByExample(LcCustLimitOperateSerialQuery.builder()
            .entityIdList(entityIdList)
            .build());
    }

    @Override
    public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
        log.info("====================== 接入业务{}分片逻辑 start ================================================",
            getJobTrade().getDescription());
        List<JobShared> jobSharedList = new ArrayList<>();
        //sql 批处理数量 暂定为分片数量，不放大
        Integer batchFixNum = jobInitDto.getFixNum();
        //当前分组的最大值，为下次 批处理的最小值
        LcEntityInfoDo maxLimitInfoDo = new LcEntityInfoDo();
        //构造查询条件 查询当前 分批处理的 排序 最大 对象
        EntityInfoBatchQuery query = EntityInfoBatchQuery.builder()
            .statusList(
                Lists.newArrayList(EnumLimitEntityStatus.SETTLE.getCode(), EnumLimitEntityStatus.REVERSE.getCode()))

            .offset(batchFixNum - 1)
            .limit(1)
            .build();
        //分片流水
        int batchNum = 0;
        while (maxLimitInfoDo != null) {
            query.setEntityId(maxLimitInfoDo.getEntityId());
            maxLimitInfoDo = entityInfoBatchDao.selectFirstOne(query);
            //统计分片 数量
            batchNum = countBatchNum(batchFixNum, query, maxLimitInfoDo, batchNum, jobInitDto, jobSharedList,
                query.getEntityId(), false);
        }
        log.info("====================== 接入业务{}分片逻辑 end ================================================",
            getJobTrade().getDescription());
        return jobSharedList;
    }
}
