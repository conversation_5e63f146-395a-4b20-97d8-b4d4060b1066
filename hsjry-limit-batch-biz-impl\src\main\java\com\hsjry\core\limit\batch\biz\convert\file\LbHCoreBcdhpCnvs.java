package com.hsjry.core.limit.batch.biz.convert.file;

import com.hsjry.core.limit.batch.biz.entity.LbHCoreBcdhpData;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCoreBcdhpDo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 银承汇票历史表MapStruct转换接口
 * 使用MapStruct框架进行对象转换，提供更高效的转换性能
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/22 10:30
 */
@Mapper(componentModel = "spring")
public interface LbHCoreBcdhpCnvs {

    /**
     * 转换器实例
     */
    LbHCoreBcdhpCnvs INSTANCE = Mappers.getMapper(LbHCoreBcdhpCnvs.class);

    /**
     * 将数据实体转换为DAO对象
     * 
     * @param data 数据实体
     * @return DAO对象
     */
    LbHCoreBcdhpDo data2Do(LbHCoreBcdhpData data);

    /**
     * 将DAO对象转换为数据实体
     * 
     * @param doObj DAO对象
     * @return 数据实体
     */
    LbHCoreBcdhpData do2Data(LbHCoreBcdhpDo dataObject);
} 