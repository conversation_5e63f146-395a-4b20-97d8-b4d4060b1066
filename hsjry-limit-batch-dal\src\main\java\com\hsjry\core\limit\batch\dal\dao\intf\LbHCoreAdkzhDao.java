package com.hsjry.core.limit.batch.dal.dao.intf;

import com.hsjry.core.limit.batch.dal.dao.model.LbHCoreAdkzhDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbHCoreAdkzhQuery;

import java.util.List;

import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 核心系统-历史表-贷款账户主数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
public interface LbHCoreAdkzhDao extends IBaseDao<LbHCoreAdkzhDo> {
    /**
     * 分页查询核心系统-历史表-贷款账户主
     *
     * @param lbHCoreAdkzhQuery 条件
     * @return PageInfo<LbHCoreAdkzhDo>
     */
    PageInfo<LbHCoreAdkzhDo> selectPage(LbHCoreAdkzhQuery lbHCoreAdkzhQuery, PageParam pageParam);

    /**
     * 根据key查询核心系统-历史表-贷款账户主
     *
     * @param faredm
     * @param daikzh
     * @return
     */
    LbHCoreAdkzhDo selectByKey(String faredm, String daikzh);

    /**
     * 根据key删除核心系统-历史表-贷款账户主
     *
     * @param faredm
     * @param daikzh
     * @return
     */
    int deleteByKey(String faredm, String daikzh);

    /**
     * 查询核心系统-历史表-贷款账户主信息
     *
     * @param lbHCoreAdkzhQuery 条件
     * @return List<LbHCoreAdkzhDo>
     */
    List<LbHCoreAdkzhDo> selectByExample(LbHCoreAdkzhQuery lbHCoreAdkzhQuery);

    /**
     * 新增核心系统-历史表-贷款账户主信息
     *
     * @param lbHCoreAdkzh 条件
     * @return int>
     */
    int insertBySelective(LbHCoreAdkzhDo lbHCoreAdkzh);

    /**
     * 修改核心系统-历史表-贷款账户主信息
     *
     * @param lbHCoreAdkzh
     * @return
     */
    int updateBySelective(LbHCoreAdkzhDo lbHCoreAdkzh);

    /**
     * 修改核心系统-历史表-贷款账户主信息
     *
     * @param lbHCoreAdkzh
     * @param lbHCoreAdkzhQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbHCoreAdkzhDo lbHCoreAdkzh, LbHCoreAdkzhQuery lbHCoreAdkzhQuery);

    /**
     * 批量插入核心系统-历史表-贷款账户主信息
     *
     * @param lbHCoreAdkzhList 批量数据
     * @return int
     */
    int insertList(List<LbHCoreAdkzhDo> lbHCoreAdkzhList);

    /**
     * 清空核心系统-历史表-贷款账户主所有数据
     *
     * @return int
     */
    int deleteAll();

    int deleteByDataDate(String dataDate);
}
