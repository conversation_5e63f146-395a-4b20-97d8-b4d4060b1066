package com.hsjry.core.limit.batch.dal.dao.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 额度中心-中间表-集团客户信息主键
 *
 * <AUTHOR>
 * @date 2025-07-08 02:50:36
 */
@Table(name = "lb_t_grp_cust_info")
@Getter
@Setter
@EqualsAndHashCode
@ToString
public class LbTGrpCustInfoKeyDo implements Serializable {

    private static final long serialVersionUID = 1942415996337979416L;
    /** 客户编号 */
    @Id
    @Column(name = "cust_no")
    private String custNo;
    /** 租户号 */
    @Id
    @Column(name = "tenant_id")
    private String tenantId;
}