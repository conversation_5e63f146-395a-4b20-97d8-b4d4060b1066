package com.hsjry.core.limit.batch.dal.dao.impl;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.hsjry.core.limit.batch.dal.dao.intf.LbTReclcIndvTotlDao;
import com.hsjry.core.limit.batch.dal.dao.mapper.LbTReclcIndvTotlMapper;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIndvTotlDo;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIndvTotlExample;
import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIndvTotlKeyDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbTReclcIndvTotlQuery;
import com.hsjry.lang.common.utils.StringUtil;
import com.hsjry.lang.mybatis.dao.AbstractBaseDaoImpl;
import com.hsjry.lang.mybatis.pagehelper.PageHelper;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 额度中心-中间表-额度重算中个人额度中客户总额度数据库操作接口实现
 *
 * <AUTHOR>
 * @date 2025-08-21 13:10:51
 */
@Repository
public class LbTReclcIndvTotlDaoImpl extends AbstractBaseDaoImpl<LbTReclcIndvTotlDo, LbTReclcIndvTotlMapper>
    implements LbTReclcIndvTotlDao {
    /**
     * 分页查询
     *
     * @param lbTReclcIndvTotl 条件
     * @param pageParam 分页参数
     * @return
     */
    @Override
    public PageInfo<LbTReclcIndvTotlDo> selectPage(LbTReclcIndvTotlQuery lbTReclcIndvTotl, PageParam pageParam) {
        LbTReclcIndvTotlExample example = buildExample(lbTReclcIndvTotl);
        return PageHelper.<LbTReclcIndvTotlDo>startPage(pageParam.getPageNum(), pageParam.getPageSize())
            .doSelectPageInfo(() -> getMapper().selectByExample(example));
    }

    /**
     * 根据key查询额度中心-中间表-额度重算中个人额度中客户总额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public LbTReclcIndvTotlDo selectByKey(String custNo, String custLimitId) {
        LbTReclcIndvTotlKeyDo lbTReclcIndvTotlKeyDo = new LbTReclcIndvTotlKeyDo();
        lbTReclcIndvTotlKeyDo.setCustNo(custNo);
        lbTReclcIndvTotlKeyDo.setCustLimitId(custLimitId);
        return getMapper().selectByPrimaryKey(lbTReclcIndvTotlKeyDo);
    }

    /**
     * 根据key删除额度中心-中间表-额度重算中个人额度中客户总额度
     *
     * @param custNo
     * @param custLimitId
     * @return
     */
    @Override
    public int deleteByKey(String custNo, String custLimitId) {
        LbTReclcIndvTotlKeyDo lbTReclcIndvTotlKeyDo = new LbTReclcIndvTotlKeyDo();
        lbTReclcIndvTotlKeyDo.setCustNo(custNo);
        lbTReclcIndvTotlKeyDo.setCustLimitId(custLimitId);
        return getMapper().deleteByPrimaryKey(lbTReclcIndvTotlKeyDo);
    }

    /**
     * 查询额度中心-中间表-额度重算中个人额度中客户总额度信息
     *
     * @param lbTReclcIndvTotl 条件
     * @return List<LbTReclcIndvTotlDo>
     */
    @Override
    public List<LbTReclcIndvTotlDo> selectByExample(LbTReclcIndvTotlQuery lbTReclcIndvTotl) {
        return getMapper().selectByExample(buildExample(lbTReclcIndvTotl));
    }

    /**
     * 新增额度中心-中间表-额度重算中个人额度中客户总额度信息
     *
     * @param lbTReclcIndvTotl 条件
     * @return int>
     */
    @Override
    public int insertBySelective(LbTReclcIndvTotlDo lbTReclcIndvTotl) {
        if (lbTReclcIndvTotl == null) {
            return -1;
        }

        return getMapper().insertSelective(lbTReclcIndvTotl);
    }

    /**
     * 修改额度中心-中间表-额度重算中个人额度中客户总额度信息
     *
     * @param lbTReclcIndvTotl
     * @return
     */
    @Override
    public int updateBySelective(LbTReclcIndvTotlDo lbTReclcIndvTotl) {
        if (lbTReclcIndvTotl == null) {
            return -1;
        }

        return getMapper().updateByPrimaryKeySelective(lbTReclcIndvTotl);
    }

    @Override
    public int updateBySelectiveByExample(LbTReclcIndvTotlDo lbTReclcIndvTotl,
        LbTReclcIndvTotlQuery lbTReclcIndvTotlQuery) {
        return getMapper().updateByExampleSelective(lbTReclcIndvTotl, buildExample(lbTReclcIndvTotlQuery));
    }

    /**
     * 构建额度中心-中间表-额度重算中个人额度中客户总额度Example信息
     *
     * @param lbTReclcIndvTotl
     * @return
     */
    public LbTReclcIndvTotlExample buildExample(LbTReclcIndvTotlQuery lbTReclcIndvTotl) {
        LbTReclcIndvTotlExample example = new LbTReclcIndvTotlExample();
        LbTReclcIndvTotlExample.Criteria criteria = example.createCriteria();
        if (lbTReclcIndvTotl != null) {
            //添加查询条件
            if (StringUtil.isNotEmpty(lbTReclcIndvTotl.getCustNo())) {
                criteria.andCustNoEqualTo(lbTReclcIndvTotl.getCustNo());
            }
            if (StringUtil.isNotEmpty(lbTReclcIndvTotl.getCustLimitId())) {
                criteria.andCustLimitIdEqualTo(lbTReclcIndvTotl.getCustLimitId());
            }
            if (StringUtil.isNotEmpty(lbTReclcIndvTotl.getTemplateNodeId())) {
                criteria.andTemplateNodeIdEqualTo(lbTReclcIndvTotl.getTemplateNodeId());
            }
            if (StringUtil.isNotEmpty(lbTReclcIndvTotl.getLimitStatus())) {
                criteria.andLimitStatusEqualTo(lbTReclcIndvTotl.getLimitStatus());
            }
            if (null != lbTReclcIndvTotl.getTotalAmount()) {
                criteria.andTotalAmountEqualTo(lbTReclcIndvTotl.getTotalAmount());
            }
            if (null != lbTReclcIndvTotl.getPreOccupyAmount()) {
                criteria.andPreOccupyAmountEqualTo(lbTReclcIndvTotl.getPreOccupyAmount());
            }
            if (null != lbTReclcIndvTotl.getRealOccupyAmount()) {
                criteria.andRealOccupyAmountEqualTo(lbTReclcIndvTotl.getRealOccupyAmount());
            }
            if (null != lbTReclcIndvTotl.getLowRiskAmount()) {
                criteria.andLowRiskAmountEqualTo(lbTReclcIndvTotl.getLowRiskAmount());
            }
            if (null != lbTReclcIndvTotl.getPreOccupyLowRiskAmt()) {
                criteria.andPreOccupyLowRiskAmtEqualTo(lbTReclcIndvTotl.getPreOccupyLowRiskAmt());
            }
            if (null != lbTReclcIndvTotl.getRealOccupyLowRiskAmt()) {
                criteria.andRealOccupyLowRiskAmtEqualTo(lbTReclcIndvTotl.getRealOccupyLowRiskAmt());
            }
        }
        buildExampleExt(lbTReclcIndvTotl, criteria);
        return example;
    }

    /**
     * 构建额度中心-中间表-额度重算中个人额度中客户总额度ExampleExt方法
     *
     * @param lbTReclcIndvTotl
     * @return
     */
    public void buildExampleExt(LbTReclcIndvTotlQuery lbTReclcIndvTotl, LbTReclcIndvTotlExample.Criteria criteria) {

        //自定义实现
    }

    // ==================== 个人客户总额度重算相关方法实现 ====================

    /**
     * 3.1.清空个人客户总额度中间表
     */
    @Override
    public int truncateTotalLimit() {
        return getMapper().truncateTotalLimit();
    }

    /**
     * 3.2.插入个人客户总额度客户编号和额度编号
     */
    @Override
    public int insertTotalLimit() {
        return getMapper().insertTotalLimit();
    }

    /**
     * 3.3.更新个人客户总额度中间表金额信息
     */
    @Override
    public int mergeTotalLimitAmount() {
        return getMapper().mergeTotalLimitAmount();
    }

    /**
     * 3.4.更新额度实例金额信息
     */
    @Override
    public int mergeTotalLimitInstance() {
        return getMapper().mergeTotalLimitInstance();
    }

}
