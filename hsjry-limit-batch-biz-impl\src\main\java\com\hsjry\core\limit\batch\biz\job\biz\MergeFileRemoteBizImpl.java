/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.job.biz;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;
import com.hsjry.base.common.job.biz.file.FileMergeSharedBiz;
import com.hsjry.base.common.job.biz.file.IFileBaseBuilder;
import com.hsjry.base.common.job.dto.BaseFile;
import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobConstant;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.StatisticsData;
import com.hsjry.base.common.job.util.JobUtil;
import com.hsjry.base.common.model.enums.limit.EnumLimitHandlerStatus;
import com.hsjry.base.common.utils.NumberUtils;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.batch.core.BusinessDataCore;
import com.hsjry.core.limit.batch.core.bo.InboundSerialFileBo;
import com.hsjry.core.limit.center.dal.dao.intf.LcSliceBatchSerialDao;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.core.limit.center.dal.dao.query.LcSliceBatchSerialQuery;
import com.hsjry.lang.common.exception.JobBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

import lombok.extern.slf4j.Slf4j;
/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/4/17 16:36
 */
@Slf4j
@Service
public class MergeFileRemoteBizImpl implements BaseOrdinaryBiz, IFileBaseBuilder {

    @Autowired
    private LcSliceBatchSerialDao lcSliceBatchSerialDao;

    @Override
    public String initBatchSerialNo(JobInitDto jobInitDto) {
        return String.join(JobConstant.BATCH_SEPARATOR, jobInitDto.getTenantId(), jobInitDto.getBusinessDate()
            .toString(), JobUtil.parseInpara(jobInitDto.getInPara())
            .getJobTradeCode());
    }

    @Override
    public IEnumTrade getJobTrade() {
        return EnumJobTrade.MERGE_REMOTE_FILE;
    }

    @Autowired
    private FileMergeSharedBiz fileMergeSharedBiz;

    @Override
    public void execBaseJob(JobInitDto jobInitDto) {

        //统计分片信息
        StatisticsData statisticsData = statisticsDataByPage(jobInitDto.getBatchSerialNo(),
            JobConstant.BATH_DB_PAGE_SIZE, null);

        //线上文件合并
        fileMergeSharedBiz.handleMergeFileOnline(jobInitDto, statisticsData, this);

        // //日终统计、发生额文件处理
        // staticFileSharedBiz.handleStatsFiles(jobInitDto, statisticsData);
    }

    /**
     * 分页统计
     *
     * @param batchSerialNo
     * @param batchPageSize
     * @param businessDataCoreImpl
     * @return
     */
    private StatisticsData statisticsDataByPage(String batchSerialNo, int batchPageSize,
        BusinessDataCore businessDataCoreImpl) {
        //初始化 统计项
        StatisticsData statisticsDataBo = this.initStatisticsDataBo();

        LcSliceBatchSerialQuery assetSliceBatchSerialQuery = LcSliceBatchSerialQuery.builder()
            .batchSerialNo(batchSerialNo)
            .build();
        int startPageNum = 1;
        PageParam pageParam = PageParam.Builder.getInstance()
            .addPageNum(startPageNum)
            .addPageSize(batchPageSize)
            .build();
        boolean hasNextPage;
        do {
            PageInfo<LcSliceBatchSerialDo> sliceBatchSerialDoPageInfoList = lcSliceBatchSerialDao.selectPage(
                assetSliceBatchSerialQuery, pageParam);
            //检查分页数据
            checkPageData(assetSliceBatchSerialQuery, sliceBatchSerialDoPageInfoList);
            List<LcSliceBatchSerialDo> assetSliceBatchSerialDoList = sliceBatchSerialDoPageInfoList.getList();

            statisticsData(statisticsDataBo, assetSliceBatchSerialDoList, businessDataCoreImpl);
            //下次分页 条件
            hasNextPage = sliceBatchSerialDoPageInfoList.isHasNextPage();
            pageParam.setPageNum(++startPageNum);
        } while (hasNextPage);
        return statisticsDataBo;
    }

    /**
     * 多个分片流水 统计 amount 数据
     *
     * @param statisticsDataBo 统计内容
     * @param assetSliceBatchSerialDoList 待统计数据
     */
    private void statisticsData(final StatisticsData statisticsDataBo,
        List<LcSliceBatchSerialDo> assetSliceBatchSerialDoList, BusinessDataCore businessDataCoreImpl) {
        if (CollectionUtil.isNotEmpty(assetSliceBatchSerialDoList)) {
            assetSliceBatchSerialDoList.forEach(assetSliceBatchSerialDo -> {
                //统计每一分片的数据
                statisticsSliceShardingDate(statisticsDataBo, assetSliceBatchSerialDo, businessDataCoreImpl);
            });
        }
    }

    /**
     * 统计每一分片的数据
     * 校验数据状态
     *
     * @param statisticsDataBo 统计数据
     * @param lcSliceBatchSerialDo 单个分片流水数据
     */
    private void statisticsSliceShardingDate(StatisticsData statisticsDataBo, LcSliceBatchSerialDo lcSliceBatchSerialDo,
        BusinessDataCore businessDataCoreImpl) {
        //校验每一条分片流水的状态
        if (!EnumLimitHandlerStatus.SUCCESS.getCode()
            .equals(lcSliceBatchSerialDo.getSharedStatus())) {
            log.info("线上文件汇总统计时校验分片流水数据状态异常！，分片流水为{}", GsonUtil.obj2Json(lcSliceBatchSerialDo));
            throw new JobBizException(EnumBatchJobError.SHARDING_INFO_ERROR.getCode(),
                EnumBatchJobError.SHARDING_INFO_ERROR.getDescription(), getClass());
        }
        //TODO: 日终时，发生额文件中，对账数据的统计项，汇总
        // statisticsSliceSharding(statisticsDataBo.getStatisticsData(), statisticsDataBo, lcSliceBatchSerialDo,
        //     businessDataCoreImpl);
        //统计 execIp
        statisticsDataBo.getExecIpSet()
            .add(lcSliceBatchSerialDo.getExecIp());
        //当前任务批次处理成功的数据量
        setSuccessDataCount(statisticsDataBo, lcSliceBatchSerialDo);
    }

    /**
     * （累加）统计 数据分片实际成功处理的条数 总量
     *
     * @param statisticsDataBo 批次流水分片统计
     * @param lcSliceBatchSerialDo 分片流水
     */
    private void setSuccessDataCount(final StatisticsData statisticsDataBo, LcSliceBatchSerialDo lcSliceBatchSerialDo) {
        int number = NumberUtils.getIntValue(statisticsDataBo.getSuccessDataCount()) + NumberUtils.getIntValue(
            lcSliceBatchSerialDo.getSharedPassCount());
        statisticsDataBo.setSuccessDataCount(number);
    }

    /**
     * 检查分页数据 若未查询到数据 异常处理
     *
     * @param lcSliceBatchSerialQuery 分片流水查询条件
     * @param lcSliceBatchSerialDoPageInfo 分片数据信息
     */
    private void checkPageData(LcSliceBatchSerialQuery lcSliceBatchSerialQuery,
        PageInfo<LcSliceBatchSerialDo> lcSliceBatchSerialDoPageInfo) {
        if (lcSliceBatchSerialDoPageInfo == null || lcSliceBatchSerialDoPageInfo.getTotal() < 1) {
            log.info("未查询到分片数据！，查询条件批次总流水为【{}】,{}", lcSliceBatchSerialQuery.getBatchSerialNo(),
                lcSliceBatchSerialQuery.getBatchNum());
            throw new JobBizException(EnumBatchJobError.SHARDING_INFO_ERROR.getCode(),
                EnumBatchJobError.SHARDING_INFO_ERROR.getDescription(), getClass());
        }
    }

    /**
     * 初始化 统计项
     *
     * @return 统计项
     */
    @NonNull
    private StatisticsData initStatisticsDataBo() {
        //IP统计项
        Set<String> execIpSet = new HashSet<>();
        //资产发生额核对文件 统计内容
        Map<String, BigDecimal> sumMap = new TreeMap<>();
        String businessData = null;
        StatisticsData statisticsDataBo = new StatisticsData();
        statisticsDataBo.setStatisticsData(sumMap);
        statisticsDataBo.setExecIpSet(execIpSet);
        statisticsDataBo.setSuccessDataCount(0);
        statisticsDataBo.setBusinessData(businessData);
        return statisticsDataBo;
    }

    @Override
    public BaseFile getBaseFile() {
        return InboundSerialFileBo.builder()
            .build();
    }
}
