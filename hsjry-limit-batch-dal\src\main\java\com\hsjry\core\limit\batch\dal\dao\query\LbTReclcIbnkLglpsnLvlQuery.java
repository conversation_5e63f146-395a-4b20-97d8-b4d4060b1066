package com.hsjry.core.limit.batch.dal.dao.query;

import java.io.Serializable;

import lombok.Builder;
import lombok.Data;

/**
 * 额度中心-中间表-额度重算中同业客户中法人综合授信额度查询条件
 *
 * <AUTHOR>
 * @date 2025-08-21 12:16:19
 */
@Data
@Builder
public class LbTReclcIbnkLglpsnLvlQuery implements Serializable {

    /** serialVersionUID */
    private static final long serialVersionUID = 1958503429970264064L;

    /** 客户编号 */
    private String custNo;
    /** 额度编号 */
    private String custLimitId;
    /** 模板节点编号 */
    private String templateNodeId;
    /** 额度状态 */
    private String limitStatus;
    /** 总额度 */
    private java.math.BigDecimal totalAmount;
    /** 预占额度 */
    private java.math.BigDecimal preOccupyAmount;
    /** 实占额度 */
    private java.math.BigDecimal realOccupyAmount;
    /** 总低风险额度 */
    private java.math.BigDecimal lowRiskAmount;
    /** 预占低风险 */
    private java.math.BigDecimal preOccupyLowRiskAmt;
    /** 实占低风险 */
    private java.math.BigDecimal realOccupyLowRiskAmt;
    /** 所属法人的核心机构号 */
    private String blngLglpsnCoreInsNo;
}
