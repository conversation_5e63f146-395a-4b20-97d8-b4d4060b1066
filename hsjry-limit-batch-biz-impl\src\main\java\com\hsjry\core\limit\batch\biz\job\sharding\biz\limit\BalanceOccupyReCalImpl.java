// /**
//  * hsjry.com Inc.
//  * Copyright (c) 2014-2023 All Rights Reserved.
//  */
// package com.hsjry.core.limit.batch.biz.job.sharding.biz.limit;
//
// import java.math.BigDecimal;
// import java.util.ArrayList;
// import java.util.Date;
// import java.util.List;
// import java.util.Map;
// import java.util.stream.Collectors;
//
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.beans.factory.annotation.Qualifier;
// import org.springframework.stereotype.Service;
// import org.springframework.transaction.support.TransactionTemplate;
// import org.springframework.util.CollectionUtils;
//
// import com.google.common.collect.HashBasedTable;
// import com.google.common.collect.Lists;
// import com.google.common.collect.Table;
// import com.hsjry.base.common.job.dto.IEnumTrade;
// import com.hsjry.base.common.job.dto.JobInitDto;
// import com.hsjry.base.common.job.dto.JobShared;
// import com.hsjry.base.common.model.enums.limit.EnumReCalFlag;
// import com.hsjry.base.common.utils.AppParamUtil;
// import com.hsjry.core.limit.batch.biz.job.JobCoreBusiness;
// import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
// import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
// import com.hsjry.core.limit.batch.dal.dao.intf.LcRecalBalanceOccupyPlanDao;
// import com.hsjry.core.limit.batch.dal.dao.model.LcRecalBalanceOccupyPlanDo;
// import com.hsjry.core.limit.batch.dal.dao.model.LcSliceBatchSerialDo;
// import com.hsjry.core.limit.batch.dal.dao.query.LcRecalBalanceOccupyPlanQuery;
// import com.hsjry.core.limit.center.core.ICustLimitCalculateCore;
// import com.hsjry.core.limit.center.core.ICustLimitLockCore;
// import com.hsjry.core.limit.center.core.ICustLimitOperateCore;
// import com.hsjry.core.limit.center.core.ICustLimitQueryCore;
// import com.hsjry.core.limit.center.core.ICustLimitTreeCore;
// import com.hsjry.core.limit.center.core.bo.CustLimitAmountBo;
// import com.hsjry.core.limit.center.core.bo.CustLimitBo;
// import com.hsjry.core.limit.center.core.bo.CustLimitRelationBo;
// import com.hsjry.core.limit.center.core.bo.OperateAmountBo;
// import com.hsjry.core.limit.center.dal.dao.intf.LcEntityInfoDao;
// import com.hsjry.core.limit.center.dal.dao.model.LcEntityInfoDo;
// import com.hsjry.lang.business.date.BusinessDateUtil;
// import com.hsjry.lang.common.utils.CollectionUtil;
// import com.hsjry.lang.common.utils.GsonUtil;
// import com.hsjry.lang.common.utils.StringUtil;
//
// import lombok.extern.slf4j.Slf4j;
//
// /**
//  * <AUTHOR>
//  * @version V4.0
//  * @since 4.0.1 2023/3/15 9:58
//  */
// @Service
// @Slf4j
// public class BalanceOccupyReCalImpl extends AbstractShardingPrepareBiz<LcRecalBalanceOccupyPlanQuery>
//     implements JobCoreBusiness<LcRecalBalanceOccupyPlanDo> {
//
//     @Autowired
//     private LcRecalBalanceOccupyPlanDao lcRecalBalanceOccupyPlanDao;
//     @Autowired
//     private ICustLimitQueryCore iCustLimitQueryCore;
//     @Autowired
//     private ICustLimitLockCore iCustLimitLockCore;
//     @Autowired
//     private ICustLimitTreeCore iCustLimitTreeCore;
//     @Autowired
//     private ICustLimitOperateCore iCustLimitOperateCore;
//     @Autowired
//     private ICustLimitCalculateCore iCustLimitCalculateCore;
//     @Autowired
//     @Qualifier("limitTransactionNewTemplate")
//     private TransactionTemplate transactionTemplate;
//     @Autowired
//     private LcEntityInfoDao lcEntityInfoDao;
//
//     @Override
//     public Integer selectCountByCurrentGroupFromDb(LcRecalBalanceOccupyPlanQuery query) {
//         return lcRecalBalanceOccupyPlanDao.selectCountByCurrentGroup(query);
//     }
//
//     @Override
//     public IEnumTrade getJobTrade() {
//         return EnumJobTrade.BALANCE_LIMIT_OCCUPY_RETRY_CAL;
//     }
//
//     @Override
//     public ShardingResult<LcRecalBalanceOccupyPlanDo> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo,
//         JobInitDto jobInitDto, JobShared jobShared) {
//         ShardingResult<LcRecalBalanceOccupyPlanDo> shardingResult = new ShardingResult<>(lcSliceBatchSerialDo,
//             jobShared);
//         if (StringUtil.isBlank(jobShared.getExtParam())) {
//             return shardingResult;
//         }
//         //原始查询条件
//         LcRecalBalanceOccupyPlanQuery lcRecalBalanceOccupyPlanQuery = GsonUtil.json2Obj(jobShared.getExtParam(),
//             LcRecalBalanceOccupyPlanQuery.class);
//         LcRecalBalanceOccupyPlanQuery query = LcRecalBalanceOccupyPlanQuery.builder()
//             .limitObjectId(lcRecalBalanceOccupyPlanQuery.getLimitObjectId())
//             .currentMaxLimitObjectId(jobShared.getSupremumValue())
//             .build();
//         log.info("=========处理分片查询条件:" + GsonUtil.obj2Json(query) + "=========");
//         List<LcRecalBalanceOccupyPlanDo> list = lcRecalBalanceOccupyPlanDao.selectShardList(query);
//         shardingResult.setShardingResultList(list);
//         return shardingResult;
//     }
//
//     @Override
//     public void execJobCoreBusiness(ShardingResult<LcRecalBalanceOccupyPlanDo> shardingResult) {
//         log.info("=========分片执行开始:[{}]===========", shardingResult.getJobShared()
//             .getBatchNum());
//         List<LcRecalBalanceOccupyPlanDo> occupyPlanDoList = shardingResult.getShardingResultList();
//         if (CollectionUtils.isEmpty(occupyPlanDoList)) {
//             log.info("=========分片执行结束:" + shardingResult.getJobShared()
//                 .getBatchNum() + "数量为空=========");
//             return;
//         }
//         //计划分组
//         Table<String, String, List<LcRecalBalanceOccupyPlanDo>> table = group(occupyPlanDoList);
//         for (Map.Entry<String, Map<String, List<LcRecalBalanceOccupyPlanDo>>> mapEntry : table.rowMap()
//             .entrySet()) {
//             handle(mapEntry.getValue());
//         }
//         //更新分片流水成功
//         normalUpdateSliceSerial(occupyPlanDoList.size(), shardingResult.getLcSliceBatchSerialDo());
//         log.info("=========分片执行结束:[{}]数量为[{}]===========", shardingResult.getJobShared()
//             .getBatchNum(), occupyPlanDoList.size());
//
//     }
//
//     /**
//      * 额度处理
//      *
//      * @param value
//      */
//     private void handle(Map<String, List<LcRecalBalanceOccupyPlanDo>> value) {
//         List<CustLimitBo> limitBoList = Lists.newArrayList();
//         if (CollectionUtil.isEmpty(value)) {
//             return;
//         }
//         List<String> custLimitIdList = Lists.newArrayList(value.keySet());
//         limitBoList.addAll(iCustLimitQueryCore.queryCustLimitBoList(custLimitIdList));
//         //排序
//         iCustLimitTreeCore.lockLimitSort(limitBoList);
//         try {
//
//             //加锁
//             iCustLimitLockCore.lock(limitBoList, null, getJobTrade().getCode());
//             transactionTemplate.execute(transactionStatus -> {
//                 Integer nextVersion = null;
//                 for (CustLimitBo custLimitBo : limitBoList) {
//                     List<LcRecalBalanceOccupyPlanDo> occupyPlanDoList = value.get(custLimitBo.getCustLimitId());
//                     nextVersion = occupyPlanDoList.get(0)
//                         .getNewExchangeRateVersion();
//                     //撤销预占金额
//                     BigDecimal oldPre = BigDecimal.ZERO;
//                     //撤销实占金额
//                     BigDecimal oldReal = BigDecimal.ZERO;
//                     //撤销预占低风险
//                     BigDecimal oldPreLowRisk = BigDecimal.ZERO;
//                     //撤销实占低风险
//                     BigDecimal oldRealLowRisk = BigDecimal.ZERO;
//                     //撤销实占串用
//                     BigDecimal oldSharedReal = BigDecimal.ZERO;
//                     //撤销预占串用
//                     BigDecimal oldSharedPre = BigDecimal.ZERO;
//                     //预占金额
//                     BigDecimal newPre = BigDecimal.ZERO;
//                     //实占金额
//                     BigDecimal newReal = BigDecimal.ZERO;
//                     //预占低风险
//                     BigDecimal newPreLowRisk = BigDecimal.ZERO;
//                     //实占低风险
//                     BigDecimal newRealLowRisk = BigDecimal.ZERO;
//                     //实占串用
//                     BigDecimal newSharedReal = BigDecimal.ZERO;
//                     //预占串用
//                     BigDecimal newSharedPre = BigDecimal.ZERO;
//                     for (LcRecalBalanceOccupyPlanDo lcRecalBalanceOccupyPlanDo : occupyPlanDoList) {
//                         oldPre = oldPre.add(lcRecalBalanceOccupyPlanDo.getCaPreAmountBalance());
//                         oldReal = oldReal.add(lcRecalBalanceOccupyPlanDo.getCaAmountBalance());
//                         oldPreLowRisk = oldPreLowRisk.add(lcRecalBalanceOccupyPlanDo.getCaPreLowRiskAmtBalance());
//                         oldRealLowRisk = oldRealLowRisk.add(lcRecalBalanceOccupyPlanDo.getCaLowRiskAmtBalance());
//                         oldSharedReal = oldSharedReal.add(lcRecalBalanceOccupyPlanDo.getCaAmountShare());
//                         oldSharedPre = oldSharedPre.add(lcRecalBalanceOccupyPlanDo.getCaPreAmountShare());
//                         newPre = newPre.add(lcRecalBalanceOccupyPlanDo.getPreAmountBalance());
//                         newReal = newReal.add(lcRecalBalanceOccupyPlanDo.getAmountBalance());
//                         newPreLowRisk = newPreLowRisk.add(lcRecalBalanceOccupyPlanDo.getPreLowRiskAmtBalance());
//                         newRealLowRisk = newRealLowRisk.add(lcRecalBalanceOccupyPlanDo.getLowRiskAmtBalance());
//                         newSharedReal = newSharedReal.add(lcRecalBalanceOccupyPlanDo.getAmountShare());
//                         newSharedPre = newSharedPre.add(lcRecalBalanceOccupyPlanDo.getPreAmountShare());
//                     }
//                     CustLimitAmountBo custLimitAmountBo = custLimitBo.getCustLimitAmountBo();
//                     String lowRiskCurrency = custLimitAmountBo.getLowRiskCurrency();
//                     custLimitAmountBo.setRealOccupyAmount(custLimitAmountBo.getRealOccupyAmount()
//                         .subtract(oldReal)
//                         .add(newReal));
//                     custLimitAmountBo.setPreOccupyAmount(custLimitAmountBo.getPreOccupyAmount()
//                         .subtract(oldPre)
//                         .add(newPre));
//                     custLimitAmountBo.setSharedRealOccupyAmount(custLimitAmountBo.getSharedRealOccupyAmount()
//                         .subtract(oldSharedReal)
//                         .add(newSharedReal));
//                     custLimitAmountBo.setSharedPreOccupyAmount(custLimitAmountBo.getSharedPreOccupyAmount()
//                         .subtract(oldSharedPre)
//                         .add(newSharedPre));
//                     custLimitAmountBo.getPreOccupyLowRiskAmt()
//                         .subtract(OperateAmountBo.builder()
//                             .amount(oldPreLowRisk)
//                             .currency(lowRiskCurrency)
//                             .build());
//                     custLimitAmountBo.getPreOccupyLowRiskAmt()
//                         .add(OperateAmountBo.builder()
//                             .amount(newPreLowRisk)
//                             .currency(lowRiskCurrency)
//                             .build());
//                     custLimitAmountBo.getRealOccupyLowRiskAmt()
//                         .subtract(OperateAmountBo.builder()
//                             .amount(oldRealLowRisk)
//                             .currency(lowRiskCurrency)
//                             .build());
//                     custLimitAmountBo.getRealOccupyLowRiskAmt()
//                         .add(OperateAmountBo.builder()
//                             .amount(newRealLowRisk)
//                             .currency(lowRiskCurrency)
//                             .build());
//                 }
//                 List<CustLimitRelationBo> relationBoList = Lists.newArrayList();
//                 iCustLimitTreeCore.supplementCollectParentAndRelation(limitBoList, relationBoList);
//                 iCustLimitCalculateCore.collectCalculate(limitBoList, relationBoList);
//                 iCustLimitOperateCore.updateDataWithoutInfo(limitBoList);
//                 updateEntityReCalFlag(custLimitIdList, nextVersion);
//
//                 return true;
//             });
//
//         } finally {
//             iCustLimitLockCore.unLock(limitBoList);
//         }
//     }
//
//     /**
//      * 更新重算标记
//      *
//      * @param custLimitIdList
//      * @param nextVersion
//      */
//     private void updateEntityReCalFlag(List<String> custLimitIdList, Integer nextVersion) {
//         if (CollectionUtil.isEmpty(custLimitIdList)) {
//             return;
//         }
//         List<LcRecalBalanceOccupyPlanDo> occupyPlanDoList = lcRecalBalanceOccupyPlanDao.selectByExample(
//             LcRecalBalanceOccupyPlanQuery.builder()
//                 .custLimitIdList(custLimitIdList)
//                 .build());
//         List<String> entityIdList = occupyPlanDoList.stream()
//             .map(LcRecalBalanceOccupyPlanDo::getEntityId)
//             .distinct()
//             .collect(Collectors.toList());
//         List<LcEntityInfoDo> updateList = Lists.newArrayList();
//         Date now = BusinessDateUtil.getDate();
//         for (String entityId : entityIdList) {
//             LcEntityInfoDo lcEntityInfoDo = new LcEntityInfoDo();
//             lcEntityInfoDo.setEntityId(entityId);
//             lcEntityInfoDo.setTenantId(AppParamUtil.getTenantId());
//             lcEntityInfoDo.setCustLimitExRateRecalFlag(EnumReCalFlag.DONE.getCode());
//             if (null != nextVersion) {
//                 lcEntityInfoDo.setNextExchangeRateVersion(nextVersion);
//             }
//             lcEntityInfoDo.setUpdateTime(now);
//             updateList.add(lcEntityInfoDo);
//         }
//         if (CollectionUtil.isNotEmpty(updateList)) {
//             lcEntityInfoDao.updateByPrimaryKeySelectiveList(updateList);
//         }
//     }
//
//     @Override
//     public List<JobShared> generateJobSharding(JobInitDto jobInitDto) {
//         log.info("====================== 接入业务{}分片逻辑 start ================================================",
//             getJobTrade().getDescription());
//         List<JobShared> jobSharedList = new ArrayList<>();
//         //sql 批处理数量 暂定为分片数量，不放大
//         Integer batchFixNum = jobInitDto.getFixNum();
//         //当前分组的最大值，为下次 批处理的最小值
//         String maxPlanDo = "";
//         //构造查询条件 查询当前 分批处理的 排序 最大 对象
//         LcRecalBalanceOccupyPlanQuery query = LcRecalBalanceOccupyPlanQuery.builder()
//             .offset(batchFixNum - 1)
//             .limit(1)
//             .build();
//         //分片流水
//         int batchNum = 0;
//         while (maxPlanDo != null) {
//             query.setLimitObjectId(maxPlanDo);
//             maxPlanDo = lcRecalBalanceOccupyPlanDao.selectFirstOne(query);
//             //统计分片 数量
//             batchNum = countBatchNum(batchFixNum, query, maxPlanDo, batchNum, jobInitDto, jobSharedList,
//                 query.getEntityId(), maxPlanDo, false);
//         }
//         log.info("====================== 接入业务{}分片逻辑 end ================================================",
//             getJobTrade().getDescription());
//         return jobSharedList;
//     }
//
//     /**
//      * 计划根据额度分组
//      *
//      * @param occupyPlanDoList
//      * @return
//      */
//     private Table<String, String, List<LcRecalBalanceOccupyPlanDo>> group(
//         List<LcRecalBalanceOccupyPlanDo> occupyPlanDoList) {
//         Table<String, String, List<LcRecalBalanceOccupyPlanDo>> table = HashBasedTable.create();
//         if (CollectionUtil.isEmpty(occupyPlanDoList)) {
//             return table;
//         }
//         for (LcRecalBalanceOccupyPlanDo occupyPlanDo : occupyPlanDoList) {
//             List<LcRecalBalanceOccupyPlanDo> doList = table.get(occupyPlanDo.getLimitObjectId(),
//                 occupyPlanDo.getCustLimitId());
//             if (null == doList) {
//                 doList = Lists.newArrayList();
//             }
//             doList.add(occupyPlanDo);
//             table.put(occupyPlanDo.getLimitObjectId(), occupyPlanDo.getCustLimitId(), doList);
//         }
//         return table;
//     }
//
// }
