package com.hsjry.core.limit.batch.common.enums;

import com.hsjry.lang.common.stereotype.enums.IEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 借据操作状态
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0 2023/11/13 17:07
 */
@Getter
@AllArgsConstructor
public enum EnumInvoiceOperateStatus implements IEnum {
    /** 处理中 */
    IN_HAND("010", "处理中"),
    /** 成功 */
    SUCCESS("020", "成功"),
    /** 失败 */
    FAIL("030", "失败"),
    ;

    /** 状态码 */
    private String code;

    /** 状态描述 */
    private String description;

    /**
     * 根据编码查找枚举
     *
     * @param code 编码
     * @return {@link EnumInvoiceOperateStatus } 实例
     **/
    public static EnumInvoiceOperateStatus find(String code) {
        for (EnumInvoiceOperateStatus instance : EnumInvoiceOperateStatus.values()) {
            if (instance.getCode()
                .equals(code)) {
                return instance;
            }
        }
        return null;
    }
}