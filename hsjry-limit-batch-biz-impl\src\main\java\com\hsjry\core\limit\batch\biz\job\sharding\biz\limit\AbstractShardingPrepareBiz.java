/*
 * yunrong.cn Inc. Copyright (c) 2014-2019 All Rights Reserved
 */

package com.hsjry.core.limit.batch.biz.job.sharding.biz.limit;

import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;

import com.hsjry.base.common.job.biz.BaseShardingPrepareBiz;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.core.limit.batch.common.enums.EnumBatchJobError;
import com.hsjry.core.limit.batch.common.utils.ExceptionUtil;
import com.hsjry.core.limit.batch.core.slice.SliceBatchSerialCore;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.lang.common.exception.JobBizException;
import com.hsjry.lang.common.utils.GsonUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version V3.0
 * @since 3.0.1 2020/10/17 16:34
 */
@Slf4j
public abstract class AbstractShardingPrepareBiz<T> implements BaseShardingPrepareBiz {
    @Autowired
    protected SliceBatchSerialCore sliceBatchSerialCore;

    /**
     * 统计当前批处理分组 的总数
     * 若当前分组查询不到最大的对象 （即找不到下一分组的最小值），则当前分组为最后一组，
     * 统计总数取 当前分组统计的数量
     * 若能找到当前分组的最大对象（即找到下一分组的最小值），则统计总数为 batchFixNum  分片 批处理数量
     *
     * @param batchFixNum 分片 批处理数量，暂定为分片数量的10倍
     * @param query 查询条件
     * @param maxDo 若当前分组查询到的最大的对象
     * @return 统计当前批处理分组 的总数
     */
    public Integer calculateGroupTotal(Integer batchFixNum, final T query, final Object maxDo) {
        Integer total;
        if (maxDo == null) {
            total = selectCountByCurrentGroupFromDb(query);
            if (Objects.isNull(total)){
                total=0;
            }
            log.info("批处理末次查询{}条数据", total);
            if (total > batchFixNum) {
                log.error("末次查询的条数 大于预设批处理数量!");
                throw new JobBizException(EnumBatchJobError.SYSTEM_ERR.getCode(),
                    EnumBatchJobError.SYSTEM_ERR.getDescription(), getClass());
            }
        } else {
            total = batchFixNum;
        }
        return total;
    }

    /**
     * 从数据库查询 当前分组的总数量
     *
     * @param query 查询条件
     * @return 当前分组的总数量
     */
    public abstract Integer selectCountByCurrentGroupFromDb(final T query);

    /**
     * 统计分片 数量
     *
     * @param total 当前分组的总数量
     * @param fixNum 固定分片数量
     * @return 当前组分片数量
     */
    private Integer calculateJobSharedCount(Integer total, Integer fixNum) {
        return total % fixNum == 0 ? total / fixNum : (total / fixNum) + 1;
    }

    /**
     * 计算分片 批次号
     *
     * @param batchFixNum 分片 批处理数量，暂定为分片数量的10倍
     * @param query 查询条件
     * @param maxDo 若当前分组查询到的最大的对象
     * @param batchNum 分片批次号
     * @param jobInitDto 初始化job 入参
     * @param jobSharedList 分片结果集
     * @param minBusinessObjectId 当前业务分组的查询条件的 业务对象id的最小值
     * @return 批次号
     */
    public Integer countBatchNum(Integer batchFixNum, final T query, final Object maxDo, int batchNum,
        JobInitDto jobInitDto, final List<JobShared> jobSharedList, String minBusinessObjectId,
        boolean multipleSettlementFlag) {
        //统计当前分组的总数
        Integer total = calculateGroupTotal(batchFixNum, query, maxDo);
        //统计分片 数量
        Integer jobSharedCount = calculateJobSharedCount(total, jobInitDto.getFixNum());
        for (int i = 0; i < jobSharedCount; i++) {
            batchNum++;
            JobShared jobShared = new JobShared(total, i, jobInitDto.getBatchSerialNo(), jobInitDto.getBusinessDate(),
                batchNum, jobInitDto.getFixNum(), minBusinessObjectId, jobInitDto.getInPara());
            //将原始查询条件 赋值到额外参数中
            jobShared.setExtParam(GsonUtil.obj2Json(query));
            jobSharedList.add(jobShared);
            //如果不支持多次结算，跳出循环，只取第一个分片
            if (!multipleSettlementFlag) {
                i = jobSharedCount;
            }
        }
        return batchNum;
    }

    /**
     * 计算分片 批次号
     *
     * @param batchFixNum 分片 批处理数量，暂定为分片数量的10倍
     * @param query 查询条件
     * @param maxDo 若当前分组查询到的最大的对象
     * @param batchNum 分片批次号
     * @param jobInitDto 初始化job 入参
     * @param jobSharedList 分片结果集
     * @param minBusinessObjectId 当前业务分组的查询条件的 业务对象id的最小值
     * @return 批次号
     */
    public Integer countBatchNum(Integer batchFixNum, final T query, final Object maxDo, int batchNum,
        JobInitDto jobInitDto, final List<JobShared> jobSharedList, String minBusinessObjectId,
        String maxBusinessObjectId, boolean multipleSettlementFlag) {
        //统计当前分组的总数
        Integer total = calculateGroupTotal(batchFixNum, query, maxDo);
        //统计分片 数量
        Integer jobSharedCount = calculateJobSharedCount(total, jobInitDto.getFixNum());
        for (int i = 0; i < jobSharedCount; i++) {
            batchNum++;
            JobShared jobShared = new JobShared(total, i, jobInitDto.getBatchSerialNo(), jobInitDto.getBusinessDate(),
                batchNum, jobInitDto.getFixNum(), minBusinessObjectId, maxBusinessObjectId, jobInitDto.getInPara());
            //将原始查询条件 赋值到额外参数中
            jobShared.setExtParam(GsonUtil.obj2Json(query));
            jobSharedList.add(jobShared);
            //如果不支持多次结算，跳出循环，只取第一个分片
            if (!multipleSettlementFlag) {
                i = jobSharedCount;
            }
        }
        return batchNum;
    }

    /**
     * 通用粉脸流水更新
     *
     * @param sharedPassCount
     * @param sliceBatchSerialDo
     */
    protected void normalUpdateSliceSerial(Integer sharedPassCount, LcSliceBatchSerialDo sliceBatchSerialDo) {
        try {
            sliceBatchSerialDo.setSharedPassCount(sharedPassCount);
            //更新成功分片信息
            sliceBatchSerialCore.updateSuccessSliceBatchSerial(sliceBatchSerialDo);
        } catch (Exception e) {
            log.error("{}[失败]", getJobTrade().getDescription());
            throw ExceptionUtil.getBizException(EnumBatchJobError.SHARDING_INFO_ERROR,
                getJobTrade().getDescription() + "【异常】", this.getClass(), e);
        }
    }
}
