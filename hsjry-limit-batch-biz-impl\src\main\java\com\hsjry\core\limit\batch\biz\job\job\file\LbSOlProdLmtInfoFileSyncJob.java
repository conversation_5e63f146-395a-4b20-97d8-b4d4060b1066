package com.hsjry.core.limit.batch.biz.job.job.file;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * 网贷系统产品额度信息文件的同步处理任务
 * 负责S_OL_PROD_LMT_INFO文件的批量同步处理
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/21 15:30
 */
@Slf4j
@Service("lbSOlProdLmtInfoFileSyncJob")
public class LbSOlProdLmtInfoFileSyncJob extends AbstractBaseBatchJob {
    
    public LbSOlProdLmtInfoFileSyncJob() {
        log.info("LbSOlProdLmtInfoFileSyncJob Bean初始化完成");
    }

    @Autowired
    @Qualifier("lbSOlProdLmtInfoFileSyncBizImpl")
    private BaseOrdinaryBiz lbSOlProdLmtInfoFileSyncBizImpl;

    @Override
    public BaseOrdinaryBiz getBaseOrdinaryBiz() {
        return lbSOlProdLmtInfoFileSyncBizImpl;
    }

    /**
     * 设置基础业务逻辑对象
     * 
     * @param baseOrdinaryBiz 基础业务逻辑对象
     */
    public void setBaseOrdinaryBiz(BaseOrdinaryBiz baseOrdinaryBiz) {
        this.lbSOlProdLmtInfoFileSyncBizImpl = baseOrdinaryBiz;
    }
} 