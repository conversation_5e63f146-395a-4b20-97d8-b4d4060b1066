---
description: 数据访问层开发指南，包含DAO接口、MyBatis映射器和SQL映射文件规范
alwaysApply: true
---
# 数据访问层（DAL）指南

## 模块结构

数据访问层分为两个模块：
- **`hsjry-limit-batch-dal`**: 定义DAO接口、数据模型(DO)和查询对象
- **`hsjry-limit-batch-dal-impl`**: 实现DAO接口，包含MyBatis映射器和SQL映射文件

## DAO接口

DAO接口位于 `hsjry-limit-batch-dal/src/main/java/com/hsjry/core/limit/batch/dal/dao/intf/` 目录下：

### 主要DAO接口
- [CustLimitInfoBatchDao.java](mdc:hsjry-limit-batch-dal/src/main/java/com/hsjry/core/limit/batch/dal/dao/intf/CustLimitInfoBatchDao.java) - 客户限额信息批处理DAO
- [AmtLimitAdjustPlanBatchDao.java](mdc:hsjry-limit-batch-dal/src/main/java/com/hsjry/core/limit/batch/dal/dao/intf/AmtLimitAdjustPlanBatchDao.java) - 金额限额调整计划批处理DAO
- [AmtLimitRecordInfoBatchDao.java](mdc:hsjry-limit-batch-dal/src/main/java/com/hsjry/core/limit/batch/dal/dao/intf/AmtLimitRecordInfoBatchDao.java) - 金额限额记录信息批处理DAO
- [AmtLimitRuleBatchDao.java](mdc:hsjry-limit-batch-dal/src/main/java/com/hsjry/core/limit/batch/dal/dao/intf/AmtLimitRuleBatchDao.java) - 金额限额规则批处理DAO

## DAO实现

DAO实现位于 `hsjry-limit-batch-dal-impl/src/main/java/com/hsjry/core/limit/batch/dal/dao/impl/` 目录下：

### 实现类
- [CustLimitInfoBatchDaoImpl.java](mdc:hsjry-limit-batch-dal-impl/src/main/java/com/hsjry/core/limit/batch/dal/dao/impl/CustLimitInfoBatchDaoImpl.java)
- [AmtLimitAdjustPlanBatchDaoImpl.java](mdc:hsjry-limit-batch-dal-impl/src/main/java/com/hsjry/core/limit/batch/dal/dao/impl/AmtLimitAdjustPlanBatchDaoImpl.java)
- [AmtLimitRecordInfoBatchDaoImpl.java](mdc:hsjry-limit-batch-dal-impl/src/main/java/com/hsjry/core/limit/batch/dal/dao/impl/AmtLimitRecordInfoBatchDaoImpl.java)

## MyBatis映射器

MyBatis映射器接口位于 `hsjry-limit-batch-dal-impl/src/main/java/com/hsjry/core/limit/batch/dal/dao/mapper/` 目录下：

### 映射器接口
- [CustLimitInfoBatchMapper.java](mdc:hsjry-limit-batch-dal-impl/src/main/java/com/hsjry/core/limit/batch/dal/dao/mapper/CustLimitInfoBatchMapper.java)
- [AmtLimitRecordInfoBatchMapper.java](mdc:hsjry-limit-batch-dal-impl/src/main/java/com/hsjry/core/limit/batch/dal/dao/mapper/AmtLimitRecordInfoBatchMapper.java)
- [CustLimitOperateSerialBatchMapper.java](mdc:hsjry-limit-batch-dal-impl/src/main/java/com/hsjry/core/limit/batch/dal/dao/mapper/CustLimitOperateSerialBatchMapper.java)

## SQL映射文件

SQL映射文件位于 `hsjry-limit-batch-dal-impl/src/main/resources/com/hsjry/core/limit/batch/dal/dao/sqlmap/` 目录下：

### XML映射文件
- [CustLimitInfoBatchMapper.xml](mdc:hsjry-limit-batch-dal-impl/src/main/resources/com/hsjry/core/limit/batch/dal/dao/sqlmap/CustLimitInfoBatchMapper.xml)
- [AmtLimitRecordInfoBatchMapper.xml](mdc:hsjry-limit-batch-dal-impl/src/main/resources/com/hsjry/core/limit/batch/dal/dao/sqlmap/AmtLimitRecordInfoBatchMapper.xml)
- [CustLimitOperateSerialBatchMapper.xml](mdc:hsjry-limit-batch-dal-impl/src/main/resources/com/hsjry/core/limit/batch/dal/dao/sqlmap/CustLimitOperateSerialBatchMapper.xml)

## 数据模型

### DO类（Data Objects）
位于 `hsjry-limit-batch-dal/src/main/java/com/hsjry/core/limit/batch/dal/dao/model/` 目录下：
- [LcCustLimitInfoDo.java](mdc:hsjry-limit-batch-dal/src/main/java/com/hsjry/core/limit/batch/dal/dao/model/LcCustLimitInfoDo.java) - 客户限额信息DO
- [LcCustLimitObjectInfoDo.java](mdc:hsjry-limit-batch-dal/src/main/java/com/hsjry/core/limit/batch/dal/dao/model/LcCustLimitObjectInfoDo.java) - 客户限额对象信息DO

### Query类
位于 `hsjry-limit-batch-dal/src/main/java/com/hsjry/core/limit/batch/dal/dao/query/` 目录下：
- [CustLimitInfoQuery.java](mdc:hsjry-limit-batch-dal/src/main/java/com/hsjry/core/limit/batch/dal/dao/query/CustLimitInfoQuery.java) - 客户限额信息查询对象
- [AmtLimitRecordInfoBatchQuery.java](mdc:hsjry-limit-batch-dal/src/main/java/com/hsjry/core/limit/batch/dal/dao/query/AmtLimitRecordInfoBatchQuery.java) - 金额限额记录信息批处理查询对象

## 集成层

### 集成接口
- [IBusinessEntityOperateIntegration.java](mdc:hsjry-limit-batch-dal/src/main/java/com/hsjry/core/limit/batch/dal/integration/IBusinessEntityOperateIntegration.java) - 业务实体操作集成接口
- [BusinessEntityOperateIntegrationImpl.java](mdc:hsjry-limit-batch-dal-impl/src/main/java/com/hsjry/core/limit/batch/dal/integration/impl/BusinessEntityOperateIntegrationImpl.java) - 业务实体操作集成实现

