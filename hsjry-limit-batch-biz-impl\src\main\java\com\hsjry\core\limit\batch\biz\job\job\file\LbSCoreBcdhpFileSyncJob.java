package com.hsjry.core.limit.batch.biz.job.job.file;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import com.hsjry.base.common.job.base.impl.AbstractBaseBatchJob;
import com.hsjry.base.common.job.biz.BaseOrdinaryBiz;

import lombok.extern.slf4j.Slf4j;

/**
 * 银承汇票落地表文件的同步处理任务
 * 负责S_CORE_BCDHP文件的批量同步处理
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/22 10:30
 */
@Slf4j
@Service("lbSCoreBcdhpFileSyncJob")
public class LbSCoreBcdhpFileSyncJob extends AbstractBaseBatchJob {
    
    public LbSCoreBcdhpFileSyncJob() {
        log.info("LbSCoreBcdhpFileSyncJob Bean初始化完成");
    }

    @Autowired
    @Qualifier("lbSCoreBcdhpFileSyncBizImpl")
    private BaseOrdinaryBiz lbSCoreBcdhpFileSyncBizImpl;

    @Override
    public BaseOrdinaryBiz getBaseOrdinaryBiz() {
        return lbSCoreBcdhpFileSyncBizImpl;
    }
} 