/*
 * yunrong.cn Inc. Copyright (c) 2014-2019 All Rights Reserved
 */

/*
 * yunrong.cn Inc. Copyright (c) 2014-2019 All Rights Reserved
 */

package com.hsjry.core.limit.batch.biz.job;

import com.hsjry.base.common.job.dto.IEnumTrade;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.core.limit.batch.biz.job.entity.ShardingResult;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;

/**
 * 批量调度任务下游
 *
 * <AUTHOR>
 * @version V3.0
 * @since 3.0.1 2020/10/17 16:34
 */
public interface JobCoreBusiness<T>  {

    /**
     * 获取任务交易码 匹配实现类时用到
     *
     * @return 任务交易码
     */
    IEnumTrade getJobTrade();

    /**
     * 查询处理分片数据
     *
     * @param lcSliceBatchSerialDo 分片信息
     * @param jobInitDto 初始化任务参数
     * @param jobShared 分片信息
     * @return
     */
    ShardingResult<T> queryShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo, JobInitDto jobInitDto,
        JobShared jobShared);

    /**
     * 执行 批量调度任务下游 核心业务
     *
     * @param shardingResult 分片结果
     */
    void execJobCoreBusiness(ShardingResult<T> shardingResult);

    /**
     * 分片任务执行前，前置处理
     *
     * @param jobInitDto
     */
    default void preHandle(JobInitDto jobInitDto) {
    }

    /**
     *
     */
    /**
     * 分片任务执行后，后面处理
     *
     * @param jobInitDto
     */
    default void afterHandle(JobInitDto jobInitDto) {
    }

}
