package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbHCoreBtxpjDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbHCoreBtxpjQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 核心系统-历史表-贴现票据信息数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-08-20 03:17:24
 */
public interface LbHCoreBtxpjDao extends IBaseDao<LbHCoreBtxpjDo> {
    /**
     * 分页查询核心系统-历史表-贴现票据信息
     *
     * @param lbHCoreBtxpjQuery 条件
     * @return PageInfo<LbHCoreBtxpjDo>
     */
    PageInfo<LbHCoreBtxpjDo> selectPage(LbHCoreBtxpjQuery lbHCoreBtxpjQuery, PageParam pageParam);

    /**
     * 根据key查询核心系统-历史表-贴现票据信息
     *
     * @param faredm
     * @param txnjjh
     * @param dataDate
     * @return
     */
    LbHCoreBtxpjDo selectByKey(String faredm, String txnjjh, String dataDate);

    /**
     * 根据key删除核心系统-历史表-贴现票据信息
     *
     * @param faredm
     * @param txnjjh
     * @param dataDate
     * @return
     */
    int deleteByKey(String faredm, String txnjjh, String dataDate);

    /**
     * 查询核心系统-历史表-贴现票据信息信息
     *
     * @param lbHCoreBtxpjQuery 条件
     * @return List<LbHCoreBtxpjDo>
     */
    List<LbHCoreBtxpjDo> selectByExample(LbHCoreBtxpjQuery lbHCoreBtxpjQuery);

    /**
     * 新增核心系统-历史表-贴现票据信息信息
     *
     * @param lbHCoreBtxpj 条件
     * @return int>
     */
    int insertBySelective(LbHCoreBtxpjDo lbHCoreBtxpj);

    /**
     * 修改核心系统-历史表-贴现票据信息信息
     *
     * @param lbHCoreBtxpj
     * @return
     */
    int updateBySelective(LbHCoreBtxpjDo lbHCoreBtxpj);

    /**
     * 修改核心系统-历史表-贴现票据信息信息
     *
     * @param lbHCoreBtxpj
     * @param lbHCoreBtxpjQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbHCoreBtxpjDo lbHCoreBtxpj, LbHCoreBtxpjQuery lbHCoreBtxpjQuery);

    /**
     * 批量插入核心系统贴现票据信息表-历史信息
     *
     * @param lbHCoreBtxpjlist 批量数据
     * @return int
     */
    int insertList(List<LbHCoreBtxpjDo> lbHCoreBtxpjlist);

    /**
     * 清空核心系统贴现票据信息表-落地所有数据
     *
     * @return int
     */
    int deleteAll();

    int deleteByDataDate(String dataDate);
}
