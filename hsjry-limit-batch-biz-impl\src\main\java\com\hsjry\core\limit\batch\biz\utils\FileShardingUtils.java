/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.utils;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;

import com.google.common.collect.Lists;
import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.core.limit.batch.biz.entity.FileLineData;
import com.hsjry.core.limit.batch.common.constants.CharsetConstants;
import com.hsjry.core.limit.batch.common.enums.EnumLimitBatchErrorCode;
import com.hsjry.lang.common.exception.HsjryBizException;
import com.hsjry.lang.common.utils.CollectionUtil;
import com.hsjry.lang.common.utils.GsonUtil;
import com.hsjry.lang.common.utils.StringUtil;

import lombok.extern.slf4j.Slf4j;
import oracle.sql.CharacterSet;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/11/7 10:11
 */
@Slf4j
public class FileShardingUtils {
    /** 账务日期 {ACCTDATE} 替换符 */
    public static final String ACCT_DATE_CODE_MARK = "${ACCTDATE}";

    public static final String TENANT_ID_CODE_MARK = "${TENANTID}";

    /** 文件下载日期 [DATE] 替换符 */
    public static final String FILE_DATE_CODE_MARK = "[DATE]";

    /**
     * 根据文件规则获取本地文件路径
     *
     * @param businessDate
     * @param merchantId
     * @param fileDirRule
     * @return
     */
    public static String getLocalFilePath(Integer businessDate, String fileDirRule) {

        if (!Objects.equals(null, businessDate) && fileDirRule.contains(FileShardingUtils.ACCT_DATE_CODE_MARK)) {
            fileDirRule = fileDirRule.replace(FileShardingUtils.ACCT_DATE_CODE_MARK, String.valueOf(businessDate));
        }
        if (fileDirRule.contains(FileShardingUtils.TENANT_ID_CODE_MARK)) {
            fileDirRule = fileDirRule.replace(FileShardingUtils.TENANT_ID_CODE_MARK, AppParamUtil.getTenantId());
        }
        return fileDirRule;
    }

    /**
     * 获取文件分片数据
     *
     * @param shared
     * @param fileCharsetName 文件字符集
     * @return
     */
    public static List<String> readFileSharedData(JobShared shared, boolean skipFirstLine, String charsetName, String fileCharsetName)
    throws IOException {
        String extParam = shared.getExtParam();
        List<String> lineDataList = Lists.newArrayList();
        if (StringUtil.isBlank(extParam)) {
            return null;
        }
        //获取文件分片属性
        FileLineData fileLineData = GsonUtil.json2Obj(extParam, FileLineData.class);
        File localFile = new File(fileLineData.getFileName());
        try (RandomAccessFile raf = new RandomAccessFile(localFile, "r")) {
            // 获取RandomAccessFile对象文件指针的位置，初始位置是0
            Long pointer = fileLineData.getPointer();
            // 移动文件指针位置
            raf.seek(pointer);
            // 文件行号
            int lineNum = fileLineData.getLineNum();
            int nextLineNum = fileLineData.getNextLineNum();
            // 行内容
            String line;
            // 循环读取
            while ((line = raf.readLine()) != null) {
                //文本转为utf-8
                line = new String(line.getBytes(fileCharsetName), charsetName);
                if (skipFirstLine) {
                    //第一行跳过读取
                    if (lineNum != 1) {
                        lineDataList.add(line);
                    }
                } else {
                    lineDataList.add(line);
                }
                //游标前移
                pointer = raf.getFilePointer();
                //行号叠加
                lineNum++;
                if (--nextLineNum <= 0) {
                    break;
                }
            }
        } catch (Exception ex) {
            log.error("文件分片读取异常", ex);
            throw new HsjryBizException(EnumLimitBatchErrorCode.FILE_SHARDING_READ_ERROR.getCode(),
                EnumLimitBatchErrorCode.FILE_SHARDING_READ_ERROR.getDescription());
        }
        return lineDataList;
    }

    public static List<String> readFileSharedData(JobShared shared, boolean skipFirst) throws IOException {
        return readFileSharedData(shared, skipFirst, CharsetConstants.UTF_8, CharsetConstants.ISO_8859_1);
    }

    /**
     *
     * @param shared
     * @param skipFirst
     * @param fileCharsetName 文件字符集
     * @return
     * @throws IOException
     */
    public static List<String> readFileSharedData(JobShared shared, boolean skipFirst, String fileCharsetName) throws IOException {
        return readFileSharedData(shared, skipFirst, CharsetConstants.UTF_8, fileCharsetName);
    }

    /**
     * 获取文件分片情况
     *
     * @param jobInitDto
     * @param localFile
     * @param fileAttr
     * @return
     * @throws IOException
     */
    public static List<JobShared> getFileSharedData(JobInitDto jobInitDto, File localFile, String fileAttr,
        boolean skipFirst) throws IOException {
        List<JobShared> jobSharedList = Lists.newArrayList();
        int fixNum = jobInitDto.getFixNum();
        // 使用NIO算法读取文件
        try (RandomAccessFile raf = new RandomAccessFile(localFile, "r")) {
            // 获取RandomAccessFile对象文件指针的位置，初始位置为0
            Long pointer = 0L;
            // 移动文件指针位置
            raf.seek(pointer);
            FileChannel fileChannel = raf.getChannel();
            ByteBuffer readByteBuffer = ByteBuffer.allocate(10000);
            // 行号
            int lineNumber = 0;
            // 最后一次分片标记行号
            int lastMarkLineNum = 0;
            // 最后一次分片标记坐标
            long lastMarkPointer = 0;
            // 分片流水
            int batchNum = 0;
            //换行符
            final int LF = 10;
            //回车键
            final int CR = 13;
            byte[] lineByte = new byte[0];
            byte[] temp = new byte[0];
            while ((fileChannel.read(readByteBuffer) != -1)) {
                int readSize = readByteBuffer.position();
                byte[] bs = new byte[readSize];
                readByteBuffer.rewind();
                readByteBuffer.get(bs);
                readByteBuffer.clear();
                int startNum = 0;
                boolean hasLF = false;
                for (int i = 0; i < readSize; i++) {
                    if (bs[i] == LF) {
                        hasLF = true;
                        int tempNum = temp.length;
                        int lineNum = i - startNum;
                        lineByte = new byte[tempNum + lineNum];
                        System.arraycopy(temp, 0, lineByte, 0, tempNum);
                        temp = new byte[0];
                        System.arraycopy(bs, startNum, lineByte, tempNum, lineNum);
                        if (lineNumber % fixNum == 0) {
                            lastMarkLineNum = lineNumber;
                            lastMarkPointer = pointer;
                            FileLineData fileLineData = new FileLineData();
                            fileLineData.setLineNum(lastMarkLineNum + 1);
                            fileLineData.setPointer(lastMarkPointer);
                            fileLineData.setNextLineNum(fixNum);
                            fileLineData.setFileName(localFile.getAbsolutePath());
                            fileLineData.setFileAttr(fileAttr);
                            fileLineData.setSkipFirst(skipFirst);

                            // 流水号批次号叠加
                            batchNum++;
                            JobShared jobShared = new JobShared();
                            jobShared.setBatchSerialNo(jobInitDto.getBatchSerialNo());
                            jobShared.setInPara(jobInitDto.getInPara());
                            jobShared.setBatchNum(batchNum);
                            jobShared.setOffset(lastMarkLineNum + 1);
                            jobShared.setFixNum(jobInitDto.getFixNum());
                            jobShared.setLimit(fileLineData.getNextLineNum());
                            jobShared.setInfimumValue(String.valueOf(pointer));
                            jobShared.setBusinessDate(jobInitDto.getBusinessDate());
                            // 将原始查询条件 赋值到额外参数中
                            jobShared.setExtParam(GsonUtil.obj2Json(fileLineData));
                            jobSharedList.add(jobShared);
                        }
                        // 游标前移
                        pointer = raf.getFilePointer() - readSize + i + 1;
                        // 当前行
                        ++lineNumber;
                        if (i + 1 < readSize && bs[i + 1] == CR) {
                            startNum = i + 2;
                            pointer++;
                        } else {
                            startNum = i + 1;
                        }
                    }
                }
                if (hasLF) {
                    temp = new byte[bs.length - startNum];
                    System.arraycopy(bs, startNum, temp, 0, temp.length);
                } else {
                    //不跳过第一行,没有换行
                    if (!skipFirst) {
                        FileLineData fileLineData = new FileLineData();
                        fileLineData.setLineNum(lastMarkLineNum);
                        fileLineData.setPointer(lastMarkPointer);
                        fileLineData.setNextLineNum(fixNum);
                        fileLineData.setFileName(localFile.getAbsolutePath());
                        fileLineData.setFileAttr(fileAttr);

                        JobShared jobShared = new JobShared();
                        jobShared.setBatchSerialNo(jobInitDto.getBatchSerialNo());
                        jobShared.setInPara(jobInitDto.getInPara());
                        jobShared.setBatchNum(batchNum);
                        jobShared.setOffset(lastMarkLineNum + 1);
                        jobShared.setFixNum(jobInitDto.getFixNum());
                        jobShared.setLimit(fileLineData.getNextLineNum());
                        jobShared.setInfimumValue(String.valueOf(pointer));
                        jobShared.setBusinessDate(jobInitDto.getBusinessDate());
                        // 将原始查询条件 赋值到额外参数中
                        jobShared.setExtParam(GsonUtil.obj2Json(fileLineData));
                        jobSharedList.add(jobShared);
                    }
                    byte[] toTemp = new byte[temp.length + bs.length];
                    System.arraycopy(temp, 0, toTemp, 0, temp.length);
                    System.arraycopy(bs, 0, toTemp, temp.length, bs.length);
                    temp = toTemp;
                }
            }
            // 判断最后一个分片，更新最后一片的数据的行数
            if (CollectionUtil.isNotEmpty(jobSharedList)) {
                int nextLineNum = lineNumber - lastMarkLineNum;
                FileLineData fileLineData = GsonUtil.json2Obj(jobSharedList.get(jobSharedList.size() - 1)
                    .getExtParam(), FileLineData.class);
                fileLineData.setPointer(lastMarkPointer);
                fileLineData.setNextLineNum(nextLineNum+1);
                jobSharedList.get(jobSharedList.size() - 1)
                    .setExtParam(GsonUtil.obj2Json(fileLineData));
                jobSharedList.get(jobSharedList.size() - 1)
                    .setLimit(nextLineNum);
            }
        } catch (Exception ex) {
            log.error("文件分片异常", ex);
            throw new HsjryBizException(EnumLimitBatchErrorCode.FILE_SHARDING_ERROR.getCode(),
                EnumLimitBatchErrorCode.FILE_SHARDING_ERROR.getDescription());
        }
        return jobSharedList;
    }

}
