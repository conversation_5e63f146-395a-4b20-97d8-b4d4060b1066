package com.hsjry.core.limit.batch.biz.convert.file;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.hsjry.core.limit.batch.biz.entity.LbHCoreBtxpjData;
import com.hsjry.core.limit.batch.dal.dao.model.LbHCoreBtxpjDo;

/**
 * 核心系统-历史表-贴现票据信息转换类
 *
 * <AUTHOR>
 * @date 2025-08-20 03:17:24
 */
public class LbHCoreBtxpjConverter {
    /** 默认初始容量 */
    private static final int DEFAULT_INITIAL_CAPACITY = 16;
    /** 负载因子 */
    private static final float LOAD_FACTOR = 0.75f;

    /**
     * Data 转换为 Do
     *
     * @param data 文件数据
     */
    public static LbHCoreBtxpjDo data2Do(LbHCoreBtxpjData data) {
        return LbHCoreBtxpjCnvs.INSTANCE.data2Do(data);
    }

    /**
     * Data列表转DO列表
     *
     * @param dataList DTO列表
     * @return DO列表
     */
    public List<LbHCoreBtxpjDo> dataListToDoList(List<LbHCoreBtxpjData> dataList) {
        if (Objects.isNull(dataList)) {
            return Collections.emptyList();
        }
        // 预估结果集大小，避免动态扩容
        int initialCapacity = Math.max((int) (dataList.size() / LOAD_FACTOR) + 1, DEFAULT_INITIAL_CAPACITY);
        return dataList.parallelStream().map(LbHCoreBtxpjConverter::data2Do)//
            .collect(Collectors.toCollection(() -> Lists.newArrayListWithCapacity(initialCapacity)));
    }
}