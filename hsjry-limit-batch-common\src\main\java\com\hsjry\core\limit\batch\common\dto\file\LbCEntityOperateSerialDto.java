package com.hsjry.core.limit.batch.common.dto.file;

import java.io.Serializable;

import lombok.Builder;
import lombok.Data;

/**
 * 实体操作流水dto
 *
 * <AUTHOR>
 * @date 2025-07-09 12:31:05
 */
@Data
@Builder
public class LbCEntityOperateSerialDto implements Serializable {
    private static final long serialVersionUID = 1942924465449140225L;
    /** 实体所属系统 */
    private String systemSign;
    /** 客户编号 */
    private String custNo;
    /** 预发放截止时间;YYYYMMdd */
    private String preGrantExpiryDateStr;
    /** 失败原因 */
    private String failReason;
    /** 状态;EnumEntityOperatorStatus：010-处理中，020-成功，030-失败，040-取消 */
    private String operateStatus;
    /** 操作类型;EnumEntityOperateType(001-强制发放,002-预发放,003-发放,004-归还,005-强制发放取消,006-预发放取消,007-发放取消,008-归还取消) */
    private String operateType;
    /** 低风险币种 */
    private String lowRiskCurrency;
    /** 操作低风险金额编号 */
    private String lowRiskAmountId;
    /** 操作低风险 */
    private java.math.BigDecimal lowRiskAmount;
    /** 金额币种 */
    private String amountCurrency;
    /** 操作金额编号 */
    private String amountId;
    /** 操作金额 */
    private java.math.BigDecimal amount;
    /** 前置业务关联流水 */
    private String lastInboundSerialNo;
    /** 全局流水号 */
    private String globalSerialNo;
    /** 实体业务编号 */
    private String entityRelationId;
    /** 实体申请编号 */
    private String entityApplyId;
    /** 实体编号 */
    private String entityId;
    /** 实体操作流水 */
    private String leosSerialNo;
    /** 更新时间 */
    private java.util.Date updateTime;
    /** 创建时间 */
    private java.util.Date createTime;
    /** 租户号 */
    private String tenantId;
    /** 前置业务时间 */
    private java.util.Date inboundSerialDatetime;
    /** 前置业务流水 */
    private String inboundSerialNo;
    /** 业务时间 */
    private java.util.Date bizDatetime;
    /** 渠道号 */
    private String channelNo;
    /** 业务流水号 */
    private String serialNo;
}
