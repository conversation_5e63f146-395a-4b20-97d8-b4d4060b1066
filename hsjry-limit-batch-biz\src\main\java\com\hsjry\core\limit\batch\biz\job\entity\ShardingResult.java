/*
 * yunrong.cn Inc. Copyright (c) 2014-2019 All Rights Reserved
 */

package com.hsjry.core.limit.batch.biz.job.entity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.hsjry.base.common.job.dto.JobShared;
import com.hsjry.core.limit.batch.common.enums.EnumJobTrade;
import com.hsjry.core.limit.center.dal.dao.model.LcSliceBatchSerialDo;
import com.hsjry.lang.common.utils.DateUtil;

import lombok.Data;

/**
 * 分片结果 包含具体的分片数据 和 任务执行信息
 *
 * <AUTHOR>
 * @version V3.0
 * @since 3.0.1 2020/10/17 16:36
 */
@Data
public class ShardingResult<T> implements Serializable {

    private static final long serialVersionUID = 1102814277463576576L;

    /**
     * 分片流水信息
     */
    private LcSliceBatchSerialDo lcSliceBatchSerialDo;
    /**
     * 分片信息
     */
    private JobShared jobShared;

    /**
     * 分片结果集
     */
    private List<T> shardingResultList;

    /**
     * 交易码（任务类型）
     */
    private EnumJobTrade jobTrade;

    /**
     * 调度任务 业务时间 ，取分片的任务执行日期 当日的最后一毫秒
     */
    private Date jobBusinessDateTime;


    /**
     * 备用字段  渠道code
     */
    private String channelCode;

    /**
     * 备用字段  产品id
     */
    private String productId;
    /**
     * 分片流水构建 分片结果
     *
     * @param lcSliceBatchSerialDo 分片流水
     */
    public ShardingResult(LcSliceBatchSerialDo lcSliceBatchSerialDo, JobShared jobShared) {
        this.lcSliceBatchSerialDo = lcSliceBatchSerialDo;
        this.jobShared = jobShared;
        if (lcSliceBatchSerialDo != null) {
            this.setJobTrade(EnumJobTrade.find(lcSliceBatchSerialDo.getTradeCode()));
        }
        if (jobShared != null && jobShared.getBusinessDate() != null) {
            //调度任务 业务时间 ，取分片的任务执行日期 当日的最后一毫秒
            this.jobBusinessDateTime = (this.integerDateToEndTimeOfDay(jobShared.getBusinessDate()));

        }

    }

    /**
     * 返回指定日期 当天的最后一秒时间
     *
     * @param fixCeilingValue yyyyMMdd数字日期
     * @return 指定日期的最后一毫秒时间 例 2019-03-11 23:59:59
     */
    private Date integerDateToEndTimeOfDay(Integer fixCeilingValue) {
        Date date = DateUtil.getDate(fixCeilingValue.toString(), DateUtil.DATE_FORMAT_2);
        return DateUtil.getEndDateTime(date);
    }

}
