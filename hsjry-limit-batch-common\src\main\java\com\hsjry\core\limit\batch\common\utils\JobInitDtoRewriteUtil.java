package com.hsjry.core.limit.batch.common.utils;

import java.util.Date;

import com.hsjry.base.common.job.dto.JobInitDto;
import com.hsjry.base.common.job.util.JobUtil;
import com.hsjry.base.common.model.dto.base.HsjryRequest;
import com.hsjry.base.common.utils.AppParamUtil;
import com.hsjry.lang.common.utils.DateUtil;

/**
 * <AUTHOR>
 * @version V3.0
 * @since 3.0.1 2020/10/17 17:09
 */
public class JobInitDtoRewriteUtil {

    /**
     * 重写 批次流水,交易时间 到上下文
     *
     * @param jobInitDto
     * @return
     */
    public static String rewriteBusinessDate(JobInitDto jobInitDto) {
        //日期 模拟，时分秒 用当前服务器的
        String currentDateString = jobInitDto.getBusinessDate() + DateUtil.getDate(new Date(),
            DateUtil.DATETIME_FORMAT_2)
            .substring(8);

        //写入上下文
        HsjryRequest hsjryRequest = AppParamUtil.getHsjryRequest();
        hsjryRequest.setTransDateTime(DateUtil.getDate(currentDateString, DateUtil.DATETIME_FORMAT_2));
        AppParamUtil.setRequest(hsjryRequest);

        //重写 批次流水
        jobInitDto.setBatchSerialNo(
            JobUtil.generatorJobatchSerialNo(jobInitDto.getTenantId(), jobInitDto.getJobTrade(), currentDateString));
        return currentDateString;
    }

}
