package com.hsjry.core.limit.batch.dal.dao.mapper;

import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcIndvConsmLoanDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 额度中心-中间表-额度重算中个人额度中消费贷额度mapper
 *
 * <AUTHOR>
 * @date 2025-08-21 13:10:51
 */
public interface LbTReclcIndvConsmLoanMapper extends CommonMapper<LbTReclcIndvConsmLoanDo> {

    // ==================== 消费贷额度重算相关方法 ====================

    /**
     * 2.1.清空消费贷额度中间表
     */
    int truncateConsmLoanLimit();

    /**
     * 2.2.插入消费贷额度客户编号和额度编号
     */
    int insertConsmLoanLimit();

    /**
     * 2.3.更新消费贷额度中间表金额信息
     */
    int mergeConsmLoanLimitAmount();

    /**
     * 2.4.更新额度实例金额信息
     */
    int mergeConsmLoanLimitInstance();
}