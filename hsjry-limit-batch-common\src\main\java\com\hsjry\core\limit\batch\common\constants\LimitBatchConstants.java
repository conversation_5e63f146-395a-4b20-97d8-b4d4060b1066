/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.common.constants;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2023/3/8 17:10
 */
public class LimitBatchConstants {
    /** 限额明细分片数量 */
    public static final int AMT_LIMIT_DETAIL_SHARD_NUM = 10;
    /** 规则编号 */
    public static final String RULE_ID = "RULE_ID";
    /** 总额度 */
    public static final String STATISTICS_TOTAL_AMOUNT = "STATISTICS_TOTAL_AMOUNT";
    /** 可用总额度 */
    public static final String STATISTICS_AVAILABLE_AMOUNT = "STATISTICS_AVAILABLE_AMOUNT";
    /** 低风险额度 */
    public static final String STATISTICS_LOW_RISK_AMOUNT = "STATISTICS_LOW_RISK_AMOUNT";
    /** 可用低风险 */
    public static final String STATISTICS_AVAILABLE_LOW_RISK = "STATISTICS_AVAILABLE_LOW_RISK";
    /** 10000 */
    public static final BigDecimal TEN_THOUSAND = new BigDecimal("10000");
    /** 10000L */
    public static final Long TEN_THOUSAND_L = 10000L;

    // ========== 信用卡额度处理相关常量 ==========

    /** 租户ID */
    public static final String TENANT_ID = "000";
    /** 人民币货币代码 */
    public static final String CURRENCY_CNY = "CNY";
    /** 信用卡渠道编号 */
    public static final String CHANNEL_NO_CCS = "CCS";
    /** 结束日期字符串 */
    public static final String END_DATE_STR = "2999-12-31";
    /** 批量插入批次大小 */
    public static final int BATCH_INSERT_SIZE = 1000;

}
