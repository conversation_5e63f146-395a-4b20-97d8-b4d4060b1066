package com.hsjry.core.limit.batch.dal.dao.intf;

import java.util.List;

import com.hsjry.core.limit.batch.dal.dao.model.LbCLimitAmtDetailDo;
import com.hsjry.core.limit.batch.dal.dao.query.LbCLimitAmtDetailQuery;
import com.hsjry.lang.mybatis.dao.IBaseDao;
import com.hsjry.lang.mybatis.pagehelper.PageInfo;
import com.hsjry.lang.mybatis.pagehelper.PageParam;

/**
 * 金额明细数据库操作接口
 *
 * <AUTHOR>
 * @date 2025-07-03 10:03:12
 */
public interface LbCLimitAmtDetailDao extends IBaseDao<LbCLimitAmtDetailDo> {
    /**
     * 分页查询金额明细
     *
     * @param lbCLimitAmtDetailQuery 条件
     * @return PageInfo<LbCLimitAmtDetailDo>
     */
    PageInfo<LbCLimitAmtDetailDo> selectPage(LbCLimitAmtDetailQuery lbCLimitAmtDetailQuery, PageParam pageParam);

    /**
     * 根据key查询金额明细
     *
     * @param amountId
     * @param seq
     * @return
     */
    LbCLimitAmtDetailDo selectByKey(String amountId, Integer seq);

    /**
     * 根据key删除金额明细
     *
     * @param amountId
     * @param seq
     * @return
     */
    int deleteByKey(String amountId, Integer seq);

    /**
     * 查询金额明细信息
     *
     * @param lbCLimitAmtDetailQuery 条件
     * @return List<LbCLimitAmtDetailDo>
     */
    List<LbCLimitAmtDetailDo> selectByExample(LbCLimitAmtDetailQuery lbCLimitAmtDetailQuery);

    /**
     * 新增金额明细信息
     *
     * @param lbCLimitAmtDetail 条件
     * @return int>
     */
    int insertBySelective(LbCLimitAmtDetailDo lbCLimitAmtDetail);

    /**
     * 修改金额明细信息
     *
     * @param lbCLimitAmtDetail
     * @return
     */
    int updateBySelective(LbCLimitAmtDetailDo lbCLimitAmtDetail);

    /**
     * 修改金额明细信息
     *
     * @param lbCLimitAmtDetail
     * @param lbCLimitAmtDetailQuery 条件
     * @return
     */
    int updateBySelectiveByExample(LbCLimitAmtDetailDo lbCLimitAmtDetail,
        LbCLimitAmtDetailQuery lbCLimitAmtDetailQuery);
}
