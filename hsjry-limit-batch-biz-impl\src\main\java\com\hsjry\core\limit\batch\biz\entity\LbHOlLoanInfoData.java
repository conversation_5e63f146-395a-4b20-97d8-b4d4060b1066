/**
 * hsjry.com Inc.
 * Copyright (c) 2014-2023 All Rights Reserved.
 */
package com.hsjry.core.limit.batch.biz.entity;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 网贷系统历史借据信息文件数据实体
 * 对应LB_H_OL_LOAN_INFO表结构
 *
 * <AUTHOR>
 * @version V4.0
 * @since 4.0.1 2025/1/21 16:30
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LbHOlLoanInfoData {

    /** 资产借据编号 */
    private String loanInvoiceId;

    /** 支用申请编号 */
    private String loanApplyId;

    /** 用户编号 */
    private String userId;

    /** 用户姓名 */
    private String userName;

    /** 借款金额 */
    private BigDecimal loanAmount;

    /** 借款利率 */
    private BigDecimal rate;

    /** 产品编号 */
    private String productId;

    /** 产品名称 */
    private String productName;

    /** 产品种类 */
    private String productCatalog;

    /** 借款起始时间 */
    private String loanStartTime;

    /** 借款到期时间 */
    private String loanEndTime;

    /** 借据状态(1-正常,2-逾期,3-结清) */
    private String status;

    /** 放款方式 */
    private String loanType;

    /** 商户或者合作方编号 */
    private String merchantId;

    /** 商户名称 */
    private String merchantName;

    /** 门店id */
    private String storeId;

    /** 门店名称 */
    private String storeName;

    /** 还款日 */
    private Integer repayDay;

    /** 渠道编号 */
    private String channelNo;

    /** 分期金额 */
    private BigDecimal installmentAmount;

    /** 分期期数 */
    private Integer installmentNum;

    /** 放款时间 */
    private String loanPayTime;

    /** 营销中心编号 */
    private String marketCenterId;

    /** 结清日期 */
    private String settleDate;

    /** 操作者编号 */
    private String operatorId;

    /** 所属组织id */
    private String ownOrganId;

    /** 租户号 */
    private String tenantId;

    /** 创建时间 */
    private String createTime;

    /** 更新时间 */
    private String updateTime;

    /** 客户经理编号 */
    private String custMgrId;

    /** 客户手机号码 */
    private String userTel;

    /** 客户证件类型 */
    private String certificateType;

    /** 客户证件号码 */
    private String certificateNo;

    /** 代扣协议编号 */
    private String withholdProtocolId;

    /** 业务标识EnumBusinessSign */
    private String businessSign;

    /** 贷款分类 */
    private String classification;

    /** 合同编号 */
    private String contractId;

    /** 授信申请编号 */
    private String creditApplyId;

    /** 客户经理机构编号 */
    private String custMgrOrganId;

    /** 数据时间 */
    private String dataDate;
} 