package com.hsjry.core.limit.batch.dal.dao.mapper;

import com.hsjry.core.limit.batch.dal.dao.model.LbTReclcCorpPerReplyDo;
import com.hsjry.lang.mybatis.mapper.common.CommonMapper;

/**
 * 额度中心-中间表-额度重算中对公客户中单笔单批额度mapper
 *
 * <AUTHOR>
 * @date 2025-08-21 11:31:28
 */
public interface LbTReclcCorpPerReplyMapper extends CommonMapper<LbTReclcCorpPerReplyDo> {

    // ==================== 单笔单批额度重算相关方法 ====================

    /**
     * 3.1.清空单笔单批额度中间表
     */
    int truncateSingleBatchLimit();

    /**
     * 3.2.插入单笔单批额度客户编号和额度编号
     */
    int insertSingleBatchLimit();

    /**
     * 3.3.更新单笔单批额度中间表金额信息
     */
    int mergeSingleBatchLimitAmount();

    /**
     * 3.4.更新额度实例金额信息
     */
    int mergeSingleBatchLimitInstance();
}